from odoo import models, fields, _
from odoo.exceptions import UserError
import base64
import io
import pandas as pd


class ImportSerialLineWizard(models.TransientModel):
    _name = 'import.serial.line.wizard'
    _description = 'Import Serials for Move Line'

    move_id = fields.Many2one('stock.move', required=True)
    file = fields.Binary(string="Excel File", required=True)
    file_name = fields.Char(string="Filename")
    keep_existing = fields.Boolean(string="Keep Existing Serials", default=False)

    def action_import_serials(self):
        self.ensure_one()

        if not self.file:
            raise UserError(_('Please upload a valid Excel file.'))

        try:
            decoded = base64.b64decode(self.file)
            df = pd.read_excel(io.BytesIO(decoded))
        except Exception as e:
            raise UserError(_('Invalid Excel File: %s') % e)

        if df.empty or df.shape[1] < 1:
            raise UserError(_("The Excel file must have at least one column for serial numbers."))

        header_serial = df.columns[0]
        header_qty = df.columns[1] if df.shape[1] > 1 else None

        move = self.move_id
        if move.product_id.tracking == 'none':
            raise UserError(_('Product is not serial or lot tracked.'))

        if not self.keep_existing:
            move.move_line_ids.unlink()

        lines = []
        for index, row in df.iterrows():
            name = str(row[header_serial]).strip() if not pd.isna(row[header_serial]) else None
            if not name:
                continue

            qty = 1
            if header_qty and not pd.isna(row[header_qty]):
                try:
                    qty = int(row[header_qty])
                except Exception:
                    raise UserError(_("Row %s has an invalid quantity: %s") % (index + 2, row[header_qty]))

            # Try to find an existing lot, else create a new one
            lot = self.env['stock.lot'].search([
                ('name', '=', name),
                ('product_id', '=', move.product_id.id),
                ('company_id', '=', move.company_id.id),
            ], limit=1)

            if not lot:
                lot = self.env['stock.lot'].create({
                    'name': name,
                    'product_id': move.product_id.id,
                    'company_id': move.company_id.id,
                })

            for _ in range(qty):
                lines.append({
                    'picking_id': move.picking_id.id,
                    'move_id': move.id,
                    'location_id': move.location_id.id,
                    'location_dest_id': move.location_dest_id.id,
                    'product_id': move.product_id.id,
                    'product_uom_id': move.product_uom.id,
                    'qty_done': 1,
                    'lot_id': lot.id,
                })

        if not lines:
            raise UserError(_("No valid serial numbers were found to import."))

        self.env['stock.move.line'].create(lines)
        return {'type': 'ir.actions.act_window_close'}
