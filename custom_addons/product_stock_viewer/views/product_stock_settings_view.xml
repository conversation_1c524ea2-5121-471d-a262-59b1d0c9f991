<?xml version="1.0" encoding="UTF-8"?>
<!--
    Stock Display Settings Configuration
    Allows admin users to define which internal locations are shown
    in the product "Stock by Location" tab, along with a custom label.
-->
<odoo>
    <record id="view_stock_display_config_form" model="ir.ui.view">
        <field name="name">stock.display.config.form</field>
        <field name="model">stock.display.config</field>
        <field name="arch" type="xml">
            <form string="Stock Display Configuration">
                <header>
                    <button name="action_push_to_products" type="object" string="Push to Products" class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="allowed_company_ids" widget="many2many_tags"/>
                    </group>
                    <field name="line_ids">
                        <list>
                            <field name="sequence"/>
                            <field name="custom_label"/>
                            <field name="location_id"/>
                        </list>
                    </field>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_stock_display_config_list" model="ir.ui.view">
        <field name="name">stock.display.config.list</field>
        <field name="model">stock.display.config</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
            </list>
        </field>
    </record>

    <record id="action_stock_display_config" model="ir.actions.act_window">
        <field name="name">Stock Display Settings</field>
        <field name="res_model">stock.display.config</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem id="menu_stock_viewer_root"
              name="Stock Tool"
              parent="stock.menu_stock_root"
              sequence="60"/>

    <menuitem id="menu_stock_display_config"
              name="Settings"
              parent="menu_stock_viewer_root"
              action="action_stock_display_config"
              sequence="10"/>
</odoo>