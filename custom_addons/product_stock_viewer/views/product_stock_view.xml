<?xml version="1.0" encoding="UTF-8"?>
<!--
    Product Stock Tab Extension
    Displays custom-labeled stock quantities per configured storage location.
-->
<odoo>
    <record id="view_product_template_form_stock_custom" model="ir.ui.view">
        <field name="name">product.template.form.stock.custom</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet/notebook/page[5]" position="after">
                <page name="stock_by_location" string="Stock">
                    <field name="stock_display_lines" readonly="1" nolabel="1">
                        <list>
                            <field name="custom_label" readonly="1"/>
                            <field name="location_id" readonly="1"/>
                            <field name="quantity" readonly="1"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>
</odoo>