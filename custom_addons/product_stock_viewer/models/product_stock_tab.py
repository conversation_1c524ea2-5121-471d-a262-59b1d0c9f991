from odoo import api, fields, models


class StockDisplayConfig(models.Model):
    _name = 'stock.display.config'
    _description = 'Stock Display Configuration'

    name = fields.Char(string='Configuration Name', required=True)
    line_ids = fields.One2many('stock.display.config.line', 'config_id', string='Stock Locations')
    allowed_company_ids = fields.Many2many('res.company', string='Companies Allowed to View Stock Tab')

    def action_push_to_products(self):
        products = self.env['product.template'].sudo().with_context(active_test=False).search([])
        quant_obj = self.env['stock.quant'].sudo()

        for product in products:
            variant_ids = product.product_variant_ids.ids
            for line in self.line_ids:
                quants = quant_obj.read_group(
                    domain=[
                        ('product_id', 'in', variant_ids),
                        ('location_id', '=', line.location_id.id)
                    ],
                    fields=['quantity:sum'],
                    groupby=[]
                )
                quantity = quants[0]['quantity'] if quants else 0.0

                for company in (self.allowed_company_ids or self.env['res.company'].search([])):
                    self.env['product.stock.display.cache'].create({
                        'product_tmpl_id': product.id,
                        'custom_label': line.custom_label,
                        'location_id': line.location_id.id,
                        'quantity': quantity,
                        'company_id': company.id
                    })


class StockDisplayConfigLine(models.Model):
    _name = 'stock.display.config.line'
    _description = 'Stock Display Config Line'

    config_id = fields.Many2one('stock.display.config', string='Configuration', ondelete='cascade')
    custom_label = fields.Char(string='Label', required=True)
    location_id = fields.Many2one(
        'stock.location',
        string='Stock Location',
        required=True,
        domain="[('usage', '=', 'internal')]",
        check_company=False
    )
    sequence = fields.Integer(default=10)


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    stock_display_lines = fields.One2many(
        comodel_name='product.stock.display.cache',
        inverse_name='product_tmpl_id',
        string='Stock Display Lines',
        domain=lambda self: [('company_id', '=', self.env.company.id)]
    )


class ProductStockDisplayCache(models.Model):
    _name = 'product.stock.display.cache'
    _description = 'Cached Stock Display per Location'

    product_tmpl_id = fields.Many2one('product.template', string='Product')
    custom_label = fields.Char(string='Label')
    location_id = fields.Many2one('stock.location', string='Location')
    quantity = fields.Float(string='Qty On Hand')
    company_id = fields.Many2one('res.company', string='Company', required=True, index=True)
