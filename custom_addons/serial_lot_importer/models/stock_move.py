from odoo import models, api, _
from odoo.exceptions import UserError
import base64
import pandas as pd
import io


class StockMove(models.Model):
    _inherit = "stock.move"

    @api.model
    def action_generate_lot_line_vals(self, context, mode, first_lot, count, lot_text=None, lots_file=None):
        # Fallback to default behavior if no file uploaded
        if not lots_file:
            return super().action_generate_lot_line_vals(context, mode, first_lot, count, lot_text)

        try:
            # Decode and read Excel
            decoded_file = base64.b64decode(lots_file.split(",")[1])
            df = pd.read_excel(io.BytesIO(decoded_file), header=None)
        except Exception as e:
            raise UserError(_("Failed to read Excel file: %s") % str(e))

        # Build list of dicts: [{"name": "serial1", "qty": 1.0}, ...]
        lots = []
        for index, row in df.iterrows():
            if pd.isna(row[0]):
                continue
            name = str(row[0]).strip()
            qty = float(row[1]) if len(row) > 1 and not pd.isna(row[1]) else 1.0
            lots.append({"name": name, "qty": qty})

        return lots
