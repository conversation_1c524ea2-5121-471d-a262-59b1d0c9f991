<odoo>
    <!-- Add the is_completed checkbox to the form -->
    <record id="view_purchase_order_form_inherit_custom" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit.custom.complete</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet/group" position="inside">
                <group>
                    <field name="is_completed"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Add a ribbon banner when completed (Odoo 18 compliant) -->
    <record id="view_purchase_order_form_inherit_ribbon" model="ir.ui.view">
        <field name="name">purchase.order.ribbon.completed</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <div name="ribbon" class="oe_ribbon" modifiers='{"invisible": [["is_completed", "=", false]]}'>
                    <span class="text-success">Completed</span>
                </div>
            </xpath>
        </field>
    </record>

    <!-- Add search filter buttons only -->
    <record id="purchase_order_filter_extend_completed" model="ir.ui.view">
        <field name="name">purchase.order.search.filter.completed</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.view_purchase_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <filter name="not_completed" string="Active" domain="[('is_completed','=',False)]"/>
                <filter name="completed" string="Completed" domain="[('is_completed','=',True)]"/>
            </xpath>
        </field>
    </record>

    <!-- Server Action to Mark POs as Completed -->
    <record id="action_mark_purchase_orders_completed" model="ir.actions.server">
        <field name="name">Mark as Completed</field>
        <field name="model_id" ref="purchase.model_purchase_order"/>
        <field name="binding_model_id" ref="purchase.model_purchase_order"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            action = records.action_mark_completed()
        </field>
    </record>
</odoo>
