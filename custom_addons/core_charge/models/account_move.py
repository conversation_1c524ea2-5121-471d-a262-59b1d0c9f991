from odoo import models, _
from odoo.exceptions import UserError


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    def unlink(self):
        for line in self:
            if (
                line.product_id
                and line.product_id.default_code
                and line.product_id.default_code.startswith('CLASS')
            ):
                raise UserError(_("You are not allowed to remove a Core Charge from an invoice."))
        return super().unlink()
