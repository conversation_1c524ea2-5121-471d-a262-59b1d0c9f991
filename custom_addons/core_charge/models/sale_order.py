from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Flag: this line IS a core charge line
    is_core_charge = fields.Boolean(default=False, string="Core Charge Line")
    # Link from a core charge line -> its parent product line
    core_parent_line_id = fields.Many2one(
        'sale.order.line',
        string='Core Parent Line',
        ondelete='cascade',
        index=True,
    )
    # Convenience inverse helper (not used for writes; read-only grouping)
    core_child_line_ids = fields.One2many(
        'sale.order.line',
        'core_parent_line_id',
        string='Linked Core Lines',
        readonly=True,
    )

    @api.model_create_multi
    def create(self, vals_list):
        lines = super().create(vals_list)
        # For each non-core line, ensure a dedicated core line exists (if product has a core class)
        for line in lines:
            if not line.is_core_charge:
                line._ensure_core_line()
        return lines

    def write(self, vals):
        res = super().write(vals)

        # If qty changed on a non-core line, sync ONLY its own linked core line
        if 'product_uom_qty' in vals:
            for line in self:
                if not line.is_core_charge:
                    core_line = line._find_core_line_for_parent()
                    if core_line:
                        # Avoid recursion: use context flag if you later add observers
                        core_line.with_context(skip_core_sync=True).write({
                            'product_uom_qty': line.product_uom_qty
                        })

        # If product changed on a non-core line, reevaluate core line (create/update/remove)
        if 'product_id' in vals:
            for line in self:
                if not line.is_core_charge:
                    line._ensure_core_line()

        return res

    def unlink(self):
        # Removing a non-core line should remove its OWN linked core line only
        for line in self:
            if not line.is_core_charge:
                core_line = line._find_core_line_for_parent()
                if core_line:
                    core_line.unlink()
        return super().unlink()

    # ----------------- helpers -----------------
    def _find_core_line_for_parent(self):
        """Return the dedicated core line for this parent (or empty recordset)."""
        self.ensure_one()
        core_product = self.product_id.product_tmpl_id.core_class_id
        if not core_product:
            return self.env['sale.order.line']
        return self.order_id.order_line.filtered(
            lambda l: l.is_core_charge
            and l.core_parent_line_id == self
            and l.product_id == core_product
        )[:1]

    def _ensure_core_line(self):
        """Create/update/remove the dedicated core line for THIS parent only."""
        self.ensure_one()
        if not self.order_id or not self.product_id:
            return

        core_product = self.product_id.product_tmpl_id.core_class_id

        if not core_product:
            # No core product configured: remove any old linked core line
            old = self._find_core_line_for_parent()
            if old:
                old.unlink()
            return

        # Find existing dedicated core line for THIS parent
        core_line = self._find_core_line_for_parent()
        if core_line:
            # Update its product (if changed) + qty + price
            core_line_vals = {}
            if core_line.product_id != core_product:
                core_line_vals['product_id'] = core_product.id
                core_line_vals['name'] = _('Core Charge: %s') % core_product.display_name
                core_line_vals['price_unit'] = core_product.lst_price
            # Always keep qty in sync with parent
            core_line_vals['product_uom_qty'] = self.product_uom_qty
            if core_line_vals:
                core_line.with_context(skip_core_sync=True).write(core_line_vals)
            return

        # Create a new dedicated core line for this parent
        self.order_id.order_line.create({
            'order_id': self.order_id.id,
            'product_id': core_product.id,
            'product_uom_qty': self.product_uom_qty,
            'price_unit': core_product.lst_price,
            'name': _('Core Charge: %s (for %s)') % (core_product.display_name, self.product_id.display_name),
            'is_core_charge': True,
            'core_parent_line_id': self.id,
        })


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Keep these attributes when duplicating/templating/etc.
    def _prepare_order_line_template_values(self, line):
        values = super()._prepare_order_line_template_values(line)
        values['is_core_charge'] = line.is_core_charge
        values['core_parent_line_id'] = line.core_parent_line_id.id
        return values
