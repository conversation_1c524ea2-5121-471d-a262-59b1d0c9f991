odoo.define('core_charge.cart_lock', function (require) {
    'use strict';

    const publicWidget = require('web.public.widget');

    publicWidget.registry.CoreChargeCartLock = publicWidget.Widget.extend({
        selector: '#cart_products',
        start() {
            this.$('.o_cart_product[data-is-core-charge="1"]').each(function () {
                const $line = $(this);

                // Disable quantity controls
                $line.find('.js_add_cart_json').addClass('disabled').css({
                    pointerEvents: 'none',
                    opacity: 0.4,
                });
                $line.find('input.js_quantity').prop('readonly', true).css({
                    pointerEvents: 'none',
                    opacity: 0.8,
                });

                // Disable remove
                $line.find('.js_delete_product').addClass('disabled').css({
                    pointerEvents: 'none',
                    opacity: 0.4,
                });

                // Lock icon / hint
                const $title = $line.find('h6');
                if (!$title.find('.fa-lock').length) {
                    $title.append(
                        ' <i class="fa fa-lock text-muted" title="Core Charge – Cannot be removed or changed"></i>'
                    );
                }
            });
        },
    });

    return publicWidget.registry.CoreChargeCartLock;
});
