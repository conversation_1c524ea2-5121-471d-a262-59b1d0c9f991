from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo import http
from odoo.http import request

class CoreChargeWebsiteSalePatch(WebsiteSale):

    @http.route(['/shop/cart/update_json'], type='json', auth="public", website=True)
    def cart_update_json(self, product_id, line_id=None, add_qty=0, set_qty=0, display=True):
        # Let Odoo do its standard cart behavior
        response = super().cart_update_json(product_id, line_id=line_id, add_qty=add_qty, set_qty=set_qty, display=display)

        order = request.website.sale_get_order()
        if not order:
            return response

        # Only act on the specific line being changed (if line_id is provided)
        target_line = None
        if line_id:
            target_line = request.env['sale.order.line'].sudo().browse(int(line_id))

        # When creating a new line, we may not have line_id. Fall back to last matching non-core line.
        if (not target_line) or (not target_line.exists()):
            candidates = order.order_line.filtered(lambda l: (not l.is_core_charge) and (l.product_id.id == int(product_id)))
            target_line = candidates[-1:] if candidates else None

        if not target_line or target_line.is_core_charge or not target_line.product_id:
            return response

        core_product = target_line.product_id.product_tmpl_id.core_class_id
        if not core_product:
            # remove any previous core line linked to this parent
            core_line = order.order_line.filtered(lambda l: l.is_core_charge and l.core_parent_line_id.id == target_line.id)
            if core_line:
                core_line.unlink()
            return response

        # Find the dedicated core line for this parent line only
        core_line = order.order_line.filtered(
            lambda l: l.is_core_charge and l.core_parent_line_id.id == target_line.id and l.product_id == core_product
        )[:1]

        if core_line:
            core_line.write({'product_uom_qty': target_line.product_uom_qty})
        else:
            order.order_line.create({
                'order_id': order.id,
                'product_id': core_product.id,
                'product_uom_qty': target_line.product_uom_qty,
                'price_unit': core_product.lst_price,
                'name': f'Core Charge: {core_product.display_name} (for {target_line.product_id.display_name})',
                'is_core_charge': True,
                'core_parent_line_id': target_line.id,
            })

        return response
