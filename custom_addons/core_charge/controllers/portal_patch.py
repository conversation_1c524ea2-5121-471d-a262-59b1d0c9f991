from odoo import http
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale

class WebsiteSaleCoreChargePatch(WebsiteSale):

    @http.route(['/my/orders/<int:order_id>/update_line_dict'], type='json', auth="public", website=True)
    def update_line_dict(self, order_id, line_id=None, **post):
        response = super().update_line_dict(order_id, line_id=line_id, **post)

        order = request.env['sale.order'].sudo().browse(order_id)
        updated_line = order.order_line.filtered(lambda l: l.id == line_id)
        if not updated_line or not updated_line.product_id or updated_line.is_core_charge:
            return response

        core_product = updated_line.product_id.product_tmpl_id.core_class_id
        if not core_product:
            # If there was a previous core line linked to this parent, remove it
            core_line = order.order_line.filtered(lambda l: l.is_core_charge and l.core_parent_line_id.id == updated_line.id)
            if core_line:
                core_line.unlink()
            return response

        # Find core line strictly linked to this parent
        core_line = order.order_line.filtered(
            lambda l: l.is_core_charge and l.core_parent_line_id.id == updated_line.id and l.product_id == core_product
        )[:1]

        if core_line:
            core_line.write({'product_uom_qty': updated_line.product_uom_qty})
        else:
            request.env['sale.order.line'].sudo().create({
                'order_id': order.id,
                'product_id': core_product.id,
                'product_uom_qty': updated_line.product_uom_qty,
                'price_unit': core_product.lst_price,
                'name': f'Core Charge: {core_product.display_name} (for {updated_line.product_id.display_name})',
                'is_core_charge': True,
                'core_parent_line_id': updated_line.id,
            })
        return response
