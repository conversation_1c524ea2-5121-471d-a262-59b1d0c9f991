from odoo import http
from odoo.http import request
import logging

_logger = logging.getLogger(__name__)

try:
    from odoo.addons.portal_sale.controllers.portal_sale import CustomerPortal
except ImportError:
    from odoo.addons.sale.controllers.portal import CustomerPortal

class CustomerPortalExtended(CustomerPortal):

    @http.route(['/my/orders/<int:order_id>/update_line_dict'], type='json', auth='public', website=True)
    def update_line_dict(self, order_id, line_id=None, **post):
        response = super().update_line_dict(order_id, line_id=line_id, **post)

        order = request.env['sale.order'].sudo().browse(order_id)
        line = order.order_line.filtered(lambda l: l.id == line_id)
        if not line or not line.product_id or line.is_core_charge:
            return response

        core_product = line.product_id.product_tmpl_id.core_class_id
        if not core_product:
            core_line = order.order_line.filtered(lambda l: l.is_core_charge and l.core_parent_line_id.id == line.id)
            if core_line:
                core_line.unlink()
            return response

        core_line = order.order_line.filtered(
            lambda l: l.is_core_charge and l.core_parent_line_id.id == line.id and l.product_id == core_product
        )[:1]

        if core_line:
            core_line.sudo().write({'product_uom_qty': line.product_uom_qty})
        else:
            request.env['sale.order.line'].sudo().create({
                'order_id': order.id,
                'product_id': core_product.id,
                'product_uom_qty': line.product_uom_qty,
                'price_unit': core_product.lst_price,
                'name': f'Core Charge: {core_product.display_name} (for {line.product_id.display_name})',
                'is_core_charge': True,
                'core_parent_line_id': line.id,
            })
        return response
