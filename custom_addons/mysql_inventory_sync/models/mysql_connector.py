from odoo import models, fields, api
from odoo.exceptions import UserError
import mysql.connector
import logging

_logger = logging.getLogger(__name__)

class MySQLConnector(models.Model):
    _name = 'mysql.sync.connector'
    _description = 'MySQL Database Connection'

    name = fields.Char(string='Connection Name', required=True)
    host = fields.Char(string='Host', required=True, default='localhost')
    port = fields.Integer(string='Port', required=True, default=3306)
    database = fields.Char(string='Database', required=True)
    user = fields.Char(string='User', required=True)
    password = fields.Char(string='Password', required=True, password=True)
    charset = fields.Selection([
        ('utf8mb4', 'UTF-8 (utf8mb4)'),
        ('latin1', 'Latin1 (latin1)'),
        ('utf8', 'UTF-8 (utf8)')
    ], string='Charset', default='latin1', required=True)

    selected_table = fields.Many2one('mysql.sync.table', string='Select Table')
    fetched_tables = fields.One2many('mysql.sync.table', 'connector_id', string='Fetched Tables')
    column_mappings = fields.One2many('mysql.sync.column.mapping', 'connector_id', string='Column Mappings')

    def _sync_reordering_rule(self, product, row):
        location_name = row.get('reorder_location')
        min_qty = row.get('reorder_min_qty')
        max_qty = row.get('reorder_max_qty')
        qty_multiple = row.get('reorder_qty_multiple', 1)

        if not (location_name and min_qty and max_qty):
            return

        location = self.env['stock.location'].search([('name', '=', location_name)], limit=1)
        if not location:
            _logger.warning(f"Skipping reorder rule for {product.default_code}: Location '{location_name}' not found.")
            return

        rule = self.env['stock.reorder.rule'].search([
            ('product_id', '=', product.id),
            ('location_id', '=', location.id)
        ], limit=1)

        rule_data = {
            'product_id': product.id,
            'location_id': location.id,
            'product_min_qty': float(min_qty),
            'product_max_qty': float(max_qty),
            'qty_multiple': float(qty_multiple),
        }

        if rule:
            rule.write(rule_data)
            _logger.info(f"Updated reordering rule for {product.default_code} at {location_name}")
        else:
            self.env['stock.reorder.rule'].create(rule_data)
            _logger.info(f"Created reordering rule for {product.default_code} at {location_name}")

    def test_connection(self):
        try:
            conn = self._get_connection()
            conn.close()
            return self._show_notification('Success', 'Connection to MySQL successful!', 'success')
        except mysql.connector.Error as err:
            _logger.error(f'Connection failed: {err}')
            raise UserError(f'Connection failed: {err}')

    def fetch_tables(self):
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            cursor.close()
            conn.close()

            self.fetched_tables.unlink()
            for table in tables:
                self.env['mysql.sync.table'].create({
                    'name': table,
                    'connector_id': self.id
                })

            return self._show_notification('Tables Retrieved', f'Available tables: {", ".join(tables)}', 'info')
        except mysql.connector.Error as err:
            _logger.error(f'Error fetching tables: {err}')
            raise UserError(f'Error fetching tables: {err}')

    def update_mysql_columns(self):
        if not self.selected_table:
            raise UserError('Please select a table first.')

        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute(f"SHOW COLUMNS FROM `{self.selected_table.name}`")
        columns = [col[0] for col in cursor.fetchall()]
        self.column_mappings.unlink()

        odoo_field_map = self._get_odoo_field_map()

        for col in columns:
            normalized_col = col.replace(" ", "").replace("_", "").lower()
            matched_field = odoo_field_map.get(normalized_col)
            self.env['mysql.sync.column.mapping'].create({
                'connector_id': self.id,
                'mysql_column': col,
                'odoo_field': matched_field
            })

        cursor.close()
        conn.close()
        return self._show_notification('Columns Updated', 'MySQL columns have been updated successfully.', 'success')

    def _get_odoo_field_map(self):
        model = self.env['ir.model'].search([('model', '=', 'product.template')], limit=1)
        fields_list = self.env['ir.model.fields'].search([('model_id', '=', model.id)])
        return {
            field.name.replace("_", "").lower(): field.name for field in fields_list
        }

    def sync_inventory(self, **kwargs):
        if not self.selected_table:
            raise UserError('Please select a table first.')
        if not self.column_mappings:
            raise UserError('No column mappings defined.')

        last_sync_time = kwargs.get('last_sync_time')

        try:
            conn = self._get_connection()
            cursor = conn.cursor(dictionary=True)
            batch_size = 200
            offset = 0
            synced_count = 0

            selected_columns = [mapping.mysql_column for mapping in self.column_mappings if mapping.mysql_column]
            query = f"SELECT {', '.join([f'`{col}`' for col in selected_columns])} FROM `{self.selected_table.name}` LIMIT %s OFFSET %s"

            while True:
                _logger.info(f"Fetching batch starting at offset {offset}")
                cursor.execute(query, (batch_size, offset))
                rows = cursor.fetchall()

                if not rows:
                    break

                for row in rows:
                    internal_ref = None
                    product_values = {}

                    for mapping in self.column_mappings:
                        field = mapping.odoo_field
                        mysql_col = mapping.mysql_column
                        if not field or mysql_col not in row:
                            continue

                        value = row[mysql_col]

                        if field == 'default_code':
                            internal_ref = value
                        elif field in ['oe_number_ids', 'engine_code_ids', 'original_part_ids']:
                            continue
                        elif field == 'categ_id':
                            product_values[field] = self._get_category_id(value)
                        elif field == 'tracking':
                            tracking_map = {
                                'no tracking': 'none',
                                'by lots': 'lot',
                                'by unique serial number': 'serial'
                            }
                            product_values[field] = tracking_map.get(str(value).strip().lower(), 'none')
                        elif field == 'type':
                            type_map = {
                                'storable product': 'product',
                                'consumable': 'consu',
                                'service': 'service',
                                'goods': 'product',
                                'combo': 'consu',
                                'product': 'product'
                            }
                            key = str(value).strip().lower()
                            product_values[field] = type_map.get(key, 'product')
                        elif field == 'invoice_policy':
                            invoice_map = {
                                'ordered quantities': 'order',
                                'delivered quantities': 'delivery'
                            }
                            key = str(value).strip().lower()
                            product_values[field] = invoice_map.get(key, 'order')
                        elif field == 'purchase_method':
                            purchase_map = {
                                'on ordered quantities': 'purchase',
                                'on received quantities': 'receive'
                            }
                            key = str(value).strip().lower()
                            product_values[field] = purchase_map.get(key, 'receive')
                        elif field == 'route_ids':
                            route_names = [r.strip() for r in str(value).split(',') if r.strip()]
                            route_ids = []
                            for name in route_names:
                                route = self.env['stock.route'].search([('name', '=', name)], limit=1)
                                if not route:
                                    route = self.env['stock.route'].create({'name': name})
                                route_ids.append(route.id)
                            product_values[field] = [(6, 0, route_ids)]
                        elif field == 'competitor_1_id':
                            partner = self.env['res.partner'].search([('name', '=', value)], limit=1)
                            if not partner:
                                partner = self.env['res.partner'].create({'name': value})
                            product_values[field] = partner.id
                        elif field == 'seller_ids':
                            vendor_name = str(value).strip()
                            vendor = self.env['res.partner'].search([('name', '=', vendor_name)], limit=1)
                            if not vendor:
                                vendor = self.env['res.partner'].create({'name': vendor_name})

                            currency_name = str(row.get('vendor_currency', 'CNY')).strip()
                            currency = self.env['res.currency'].search([('name', '=', currency_name)], limit=1)
                            if not currency:
                                raise UserError(f"Currency '{currency_name}' not found in Odoo.")

                            product_values.setdefault('seller_ids', []).append((0, 0, {
                                'partner_id': vendor.id,
                                'price': float(row.get('vendor_price', 0.0)),
                                'currency_id': currency.id,
                                'min_qty': float(row.get('vendor_quantity', 1)),
                                'delay': int(row.get('vendor_delivery_lead_time', 1)),
                            }))
                        else:
                            product_values[field] = value

                    if not internal_ref:
                        _logger.warning(f"Skipping row due to missing Internal Reference: {row}")
                        continue

                    product = self.env['product.template'].search([('default_code', '=', internal_ref)], limit=1)

                    if product:
                        product.write(product_values)
                        _logger.info(f"Updated product: {internal_ref}")
                    else:
                        _logger.info(f"No product found for: {internal_ref}, skipping create.")
                        continue

                    for mapping in self.column_mappings:
                        field = mapping.odoo_field
                        value = row.get(mapping.mysql_column)
                        if field == 'oe_number_ids':
                            self._set_metadata_field(product, 'product.oe.number', 'oe_number_ids', value)
                        elif field == 'engine_code_ids':
                            self._set_metadata_field(product, 'product.engine.code', 'engine_code_ids', value)
                        elif field == 'original_part_ids':
                            self._set_metadata_field(product, 'product.original.part', 'original_part_ids', value)

                    self._sync_reordering_rule(product, row)

                    synced_count += 1

                    if synced_count % 500 == 0:
                        self.env.cr.commit()
                        _logger.info(f"Committed after {synced_count} records.")

                offset += batch_size

            cursor.close()
            conn.close()
            self.env.cr.commit()
            return self._show_notification('Sync Completed', f'{synced_count} products synced.', 'success')

        except mysql.connector.Error as err:
            _logger.error(f'Error syncing inventory: {err}')
            raise UserError(f'Error syncing inventory: {err}')

    def _set_metadata_field(self, product, model_name, field_name, value):
        if not value:
            return
        names = [v.strip() for v in value.split(',') if v.strip()]
        record_ids = []
        for name in names:
            rec = self.env[model_name].search([('name', '=', name)], limit=1)
            if not rec:
                rec = self.env[model_name].create({'name': name})
            record_ids.append(rec.id)
        if record_ids:
            product.write({field_name: [(6, 0, record_ids)]})

    def _get_category_id(self, category_name):
        if not category_name:
            return False
        category = self.env['product.category'].search([('name', '=', category_name)], limit=1)
        if not category:
            category = self.env['product.category'].create({'name': category_name})
        return category.id

    def _get_connection(self):
        try:
            return mysql.connector.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset
            )
        except mysql.connector.Error as err:
            _logger.error(f'Database connection failed: {err}')
            raise UserError(f'Database connection failed: {err}')

    def _show_notification(self, title, message, notification_type):
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': title,
                'message': message,
                'type': notification_type,
                'sticky': False,
            }
        }

class MySQLSyncTable(models.Model):
    _name = 'mysql.sync.table'
    _description = 'MySQL Table'

    name = fields.Char(string='Table Name', required=True)
    connector_id = fields.Many2one('mysql.sync.connector', string='Connector', ondelete='cascade')

class MySQLColumnMapping(models.Model):
    _name = 'mysql.sync.column.mapping'
    _description = 'MySQL Column to Odoo Field Mapping'

    connector_id = fields.Many2one('mysql.sync.connector', string='Connector', required=True, ondelete='cascade')
    mysql_column = fields.Char(string='MySQL Column', required=True)
    odoo_field = fields.Selection(selection='_get_odoo_fields', string='Odoo Field')

    @api.model
    def _get_odoo_fields(self):
        model = self.env['ir.model'].search([('model', '=', 'product.template')], limit=1)
        fields_list = self.env['ir.model.fields'].search([('model_id', '=', model.id)])
        return [(f.name, f.field_description) for f in fields_list if f.ttype != 'one2many'] + [
            ('seller_ids', 'Vendors (Purchase Tab)')
        ]
