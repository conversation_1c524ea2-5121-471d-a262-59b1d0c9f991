<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Scheduled Action: Auto Sync MySQL Inventory -->
    <record id="ir_cron_mysql_sync" model="ir.cron">
        <field name="name">Auto Sync MySQL Inventory</field>
        <field name="model_id" ref="model_mysql_sync_connector"/>
        <field name="state">code</field>
        <field name="code">model.sync_inventory(last_sync_time=model.env['ir.config_parameter'].sudo().get_param('mysql.last_sync'))</field>
        <field name="interval_number">5</field>
        <field name="interval_type">minutes</field>
        <field name="active" eval="True"/>
    </record>
</odoo>
