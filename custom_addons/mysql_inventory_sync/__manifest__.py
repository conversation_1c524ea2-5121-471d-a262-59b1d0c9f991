{
    'name': 'MySQL Product Inventory Sync',
    'version': '2.0',
    'category': 'Inventory',
    'summary': 'Sync products from a MySQL database to Odoo inventory automatically',
    'author': '<PERSON><PERSON>',
    'depends': ['base', 'stock'],
    'data': [
        'security/ir.model.access.csv',
        'views/mysql_sync_views.xml',
        'data/mysql_sync_cron_jobs.xml',
    ],
    'external_dependencies': {
        'python': ['mysql-connector-python', 'cryptography'],
    },
    'installable': True,
    'application': True,
}