<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Window Action: MySQL Connections -->
    <record id="action_mysql_connector" model="ir.actions.act_window">
        <field name="name">MySQL Connections</field>
        <field name="res_model">mysql.sync.connector</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- Menu in Technical -->
    <menuitem id="menu_mysql_connector_root" name="MySQL Sync" parent="base.menu_administration" sequence="10"/>
    <menuitem id="menu_mysql_connector_management" name="Connections" parent="menu_mysql_connector_root" action="action_mysql_connector"/>

    <!-- Form View for MySQL Connector -->
    <record id="view_mysql_connector_form" model="ir.ui.view">
        <field name="name">mysql.sync.connector.form</field>
        <field name="model">mysql.sync.connector</field>
        <field name="arch" type="xml">
            <form string="MySQL Connection">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="host"/>
                        <field name="port"/>
                        <field name="database"/>
                        <field name="user"/>
                        <field name="password" password="True"/>
                    </group>

                    <group string="Table Selection">
                        <button name="fetch_tables" type="object" string="Fetch Tables" class="btn-secondary"/>
                        <field name="selected_table"/>
                        <button name="update_mysql_columns" type="object" string="Update MySQL Columns" class="btn-info"/>
                    </group>

                    <group string="Column Mappings">
                        <field name="column_mappings" context="{'default_connector_id': id}" mode="list,form">
                            <list editable="bottom">
                                <field name="mysql_column" required="1" context="{'default_connector_id': parent.id}"/>
                                <field name="odoo_field" required="1"/>
                            </list>
                        </field>
                    </group>

                    <group col="4">
                        <button name="test_connection" type="object" string="Test Connection" class="btn-primary"/>
                        <button name="sync_inventory" type="object" string="Sync Inventory" class="btn-success" confirm="Are you sure you want to sync the inventory?"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List View for Column Mappings -->
    <record id="view_mysql_column_mapping_list" model="ir.ui.view">
        <field name="name">mysql.sync.column.mapping.list</field>
        <field name="model">mysql.sync.column.mapping</field>
        <field name="arch" type="xml">
            <list editable="bottom">
                <field name="mysql_column" required="1" context="{'default_connector_id': parent.id}"/>
                <field name="odoo_field" required="1"/>
            </list>
        </field>
    </record>

    <!-- Form View for Column Mappings -->
    <record id="view_mysql_column_mapping_form" model="ir.ui.view">
        <field name="name">mysql.sync.column.mapping.form</field>
        <field name="model">mysql.sync.column.mapping</field>
        <field name="arch" type="xml">
            <form string="Column Mapping">
                <sheet>
                    <group>
                        <field name="mysql_column" required="1" context="{'default_connector_id': parent.id}"/>
                        <field name="odoo_field" required="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
