from odoo import models, fields
import qrcode
import base64
from io import BytesIO

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    qr_image = fields.Char(compute='_compute_qr_image', store=False)

    def _compute_qr_image(self):
        for rec in self:
            if rec.name:
                qr = qrcode.QRCode(version=1, box_size=10, border=1)
                qr.add_data(rec.name)
                qr.make(fit=True)
                img = qr.make_image(fill='black', back_color='white')
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                rec.qr_image = base64.b64encode(buffer.getvalue()).decode()
            else:
                rec.qr_image = ''