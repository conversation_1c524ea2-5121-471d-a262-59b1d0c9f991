from odoo import models, fields, api
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class SerialAssignmentWizard(models.TransientModel):
    _name = 'serial.assignment.wizard'
    _description = 'Assign Serial to Existing Quant'

    scanned_location_barcode = fields.Char(string="Scan Location Barcode")
    scanned_product_barcode = fields.Char(string="Scan Product Barcode")
    serial_queue = fields.Text(string="Scanned Serials")  # One serial per line

    location_id = fields.Many2one('stock.location', readonly=True)
    product_id = fields.Many2one('product.product', readonly=True)
    available_quantity = fields.Integer(string="Available Untracked Quantity", readonly=True)
    scanned_count = fields.Integer(string="Serials Scanned", compute="_compute_scanned_count", store=True)

    @api.depends('serial_queue')
    def _compute_scanned_count(self):
        for rec in self:
            rec.scanned_count = len(list(filter(None, (rec.serial_queue or "").splitlines())))

    @api.onchange('scanned_location_barcode')
    def _onchange_location_barcode(self):
        self.location_id = False
        if self.scanned_location_barcode:
            loc = self.env['stock.location'].search([
                ('barcode', '=', self.scanned_location_barcode.strip())
            ], limit=1)
            if loc:
                self.location_id = loc

    @api.onchange('scanned_product_barcode')
    def _onchange_product_barcode(self):
        self.product_id = False
        self.available_quantity = 0

        if self.scanned_product_barcode and self.location_id:
            prod = self.env['product.product'].search([
                ('barcode', '=', self.scanned_product_barcode.strip()),
            ], limit=1)

            if prod:
                if prod.tracking in ['none', 'lot']:
                    prod.tracking = 'serial'
                    _logger.info(f"✅ Tracking updated to 'serial' for product: {prod.display_name}")

                self.product_id = prod

                quant_untracked = self.env['stock.quant'].search([
                    ('product_id', '=', prod.id),
                    ('location_id', '=', self.location_id.id),
                    ('lot_id', '=', False),
                    ('quantity', '>', 0)
                ])
                self.available_quantity = sum(quant_untracked.mapped('quantity'))

    @api.onchange('serial_queue')
    def _onchange_serial_queue(self):
        if self.available_quantity:
            serials = list(filter(None, (self.serial_queue or "").splitlines()))
            if len(serials) > self.available_quantity:
                self.serial_queue = "\n".join(serials[:self.available_quantity])

    def action_reset_serials(self):
        self.serial_queue = False
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'serial.assignment.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_reset_product_scan(self):
        self.scanned_product_barcode = False
        self.serial_queue = False
        self.product_id = False
        self.available_quantity = 0
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'serial.assignment.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_assign_serial(self):
        self.ensure_one()

        location_barcode = (self.scanned_location_barcode or "").strip()
        product_barcode = (self.scanned_product_barcode or "").strip()

        # Deduplicate and sort serials for FIFO compliance
        serials = sorted(set(
            serial.strip() for serial in (self.serial_queue or "").splitlines() if serial.strip()
        ))

        if not location_barcode:
            raise UserError("Please scan a valid location barcode.")
        if not product_barcode:
            raise UserError("Please scan a valid product barcode.")
        if not serials:
            raise UserError("Please scan at least one serial number.")

        location = self.env['stock.location'].search([('barcode', '=', location_barcode)], limit=1)
        if not location:
            raise UserError("Scanned location barcode is invalid.")

        quant_product_ids = self.env['stock.quant'].search([
            ('location_id', '=', location.id),
            ('lot_id', '=', False),
            ('quantity', '>', 0)
        ]).mapped('product_id.id')

        product = self.env['product.product'].search([
            ('barcode', '=', product_barcode),
            ('id', 'in', quant_product_ids),
        ], limit=1)
        if not product:
            raise UserError("Scanned product is invalid or not in stock at this location.")

        assigned = 0
        for serial_cleaned in serials:
            quant = self.env['stock.quant'].search([
                ('product_id', '=', product.id),
                ('location_id', '=', location.id),
                ('lot_id', '=', False),
                ('quantity', '>=', 1),
            ], limit=1)
            if not quant:
                break  # No more stock to convert

            lot = self.env['stock.lot'].search([
                ('name', '=', serial_cleaned),
                ('product_id', '=', product.id),
            ], limit=1)
            if not lot:
                lot = self.env['stock.lot'].create({
                    'name': serial_cleaned,
                    'product_id': product.id,
                    'company_id': quant.company_id.id,
                })

            if not lot or not lot.id:
                raise UserError(f"❌ Failed to create or fetch serial '{serial_cleaned}'.")

            self.env['stock.quant']._update_available_quantity(
                product, location, -1,
                lot_id=False, package_id=False, owner_id=False
            )
            self.env['stock.quant']._update_available_quantity(
                product, location, 1,
                lot_id=lot, package_id=False, owner_id=False
            )

            assigned += 1

        self._cr.flush()

        self.serial_queue = False
        self.scanned_product_barcode = False
        self.product_id = product
        self.location_id = location
        self.available_quantity = max(self.available_quantity - assigned, 0)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Serials Assigned',
                'message': f"{assigned} serial number(s) successfully assigned in FIFO order.",
                'type': 'success',
                'sticky': False,
            }
        }