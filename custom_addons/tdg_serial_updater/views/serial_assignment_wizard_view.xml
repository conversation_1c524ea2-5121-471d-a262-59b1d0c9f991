<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_serial_assignment_form" model="ir.ui.view">
        <field name="name">serial.assignment.wizard.form</field>
        <field name="model">serial.assignment.wizard</field>
        <field name="arch" type="xml">
            <!-- 👇 Added class for JS selector -->
            <form string="Assign Serial" class="serial-wizard-autofocus">
                <group>
                    <field name="scanned_location_barcode"/>
                    <field name="location_id" readonly="1"/>

                    <field name="scanned_product_barcode"/>
                    <field name="product_id" readonly="1"/>

                    <field name="available_quantity"/>
                    <field name="scanned_count" readonly="1"/>
                </group>

                <group string="Scanned Serials" col="1">
                    <field name="serial_queue" widget="text" placeholder="Scan one serial per line..."/>
                </group>

                <footer>
                    <button name="action_assign_serial" type="object" class="btn-primary" string="Assign Serial"/>
                    <button name="action_reset_product_scan" type="object" string="Scan New Product" class="btn-secondary"/>
                    <button name="action_reset_serials" type="object" class="btn-secondary" string="Reset Scans"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_serial_assignment_wizard" model="ir.actions.act_window">
        <field name="name">Assign Serial</field>
        <field name="res_model">serial.assignment.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_serial_assign_root" name="Assign Serials"/>
    <menuitem id="menu_serial_assign_action" name="Manual Serial Assignment"
              parent="menu_serial_assign_root"
              action="action_serial_assignment_wizard"/>
</odoo>
