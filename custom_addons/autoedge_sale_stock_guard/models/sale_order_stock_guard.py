# -*- coding: utf-8 -*-
from odoo import models, _, api
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

# Your instance uses: consu=Goods, service=Service, combo=Combo
BLOCKABLE_TYPES = ("consu", "combo")  # <- treat Goods & Combo as stock-relevant; service passes


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # ---- stock snapshot strictly at the SO warehouse's stock location ----
    def _ae_raw_stock_numbers(self, line):
        order = line.order_id
        product = line.product_id
        wh = order.warehouse_id
        loc = wh.lot_stock_id if wh else False

        if not product:
            return {'qty_available': 0.0, 'outgoing_qty': 0.0, 'free_qty': 0.0, 'virtual_available': 0.0}

        pctx = product.with_context(location=loc.id) if loc else product

        qty_available = float(getattr(pctx, 'qty_available', 0.0) or 0.0)
        outgoing_qty = float(getattr(pctx, 'outgoing_qty', 0.0) or 0.0)
        native_free = getattr(pctx, 'free_qty', None)
        free_qty = float(native_free if native_free is not None else (qty_available - outgoing_qty))
        virtual_available = float(getattr(pctx, 'virtual_available', 0.0) or 0.0)

        # log raw numbers so we know exactly which location was used
        _logger.warning(
            "[StockGuard-Raw] Product '%s'(id=%s) | WH '%s' | Location '%s' | "
            "SOH=%0.4f | Outgoing=%0.4f | Free=%0.4f | Forecast=%0.4f",
            product.display_name, product.id,
            wh.name if wh else "N/A",
            loc.display_name if loc else "N/A",
            qty_available, outgoing_qty, free_qty, virtual_available
        )

        return {
            'qty_available': max(qty_available, 0.0),
            'outgoing_qty': max(outgoing_qty, 0.0),
            'free_qty': max(free_qty, 0.0),
            'virtual_available': max(virtual_available, 0.0),
        }

    # convert snapshot to sale line UoM (no rounding up)
    def _ae_snapshot_uom(self, line, snap):
        product = line.product_id
        if product and line.product_uom and product.uom_id and line.product_uom != product.uom_id:
            conv = product.uom_id._compute_quantity
            return {
                'qty_available': conv(snap['qty_available'], line.product_uom, round=False),
                'outgoing_qty': conv(snap['outgoing_qty'], line.product_uom, round=False),
                'free_qty': conv(snap['free_qty'], line.product_uom, round=False),
                'virtual_available': conv(snap['virtual_available'], line.product_uom, round=False),
            }
        return snap

    def _ae_enforce_stock_guard(self):
        for order in self:
            # evaluate all real lines; exclude only sections/notes
            lines = order.order_line.filtered(lambda l: not l.display_type and l.product_id)
            _logger.warning("[StockGuard] SO %s: evaluating %d lines", order.name, len(lines))

            shortages = []
            for line in lines:
                p = line.product_id
                ptype = p.type if p else "n/a"

                snap = self._ae_snapshot_uom(line, self._ae_raw_stock_numbers(line))
                required = float(line.product_uom_qty or 0.0)

                if p and ptype in BLOCKABLE_TYPES:
                    if required > (snap['free_qty'] + 1e-6):
                        shortages.append((line, snap, required))
                        decision, reason = "BLOCK", "required > free"
                    else:
                        decision, reason = "PASS", ""
                else:
                    decision, reason = "PASS", f"ptype={ptype} (not blockable)"

                _logger.warning(
                    "[StockGuard-Line] SO %s | '%s' [%s] | Req=%0.2f %s | "
                    "SOH=%0.2f | Free=%0.2f | Out=%0.2f | Forecast=%0.2f | %s %s",
                    order.name,
                    p.display_name if p else "NO PRODUCT", ptype,
                    required, line.product_uom.name,
                    snap['qty_available'], snap['free_qty'], snap['outgoing_qty'], snap['virtual_available'],
                    decision, f"({reason})" if reason else ""
                )

            if shortages:
                rows = []
                for line, snap, required in shortages:
                    rows.append(
                        _("- %(product)s: need %(req).2f %(uom)s, free %(free).2f "
                          "(SOH=%(soh).2f, Out=%(out)s) in %(wh)s") % {
                            'product': line.product_id.display_name,
                            'req': required,
                            'uom': line.product_uom.name,
                            'free': snap['free_qty'],
                            'soh': snap['qty_available'],
                            'out': snap['outgoing_qty'],
                            'wh': order.warehouse_id.name,
                        }
                    )
                raise UserError(_(
                    "You cannot confirm this order because **on-hand stock is insufficient** "
                    "(forecast & incoming ignored):\n\n%s"
                ) % "\n".join(rows))

    def action_confirm(self):
        _logger.warning("[StockGuard] action_confirm ENTER for %s", ", ".join(self.mapped('name')))
        self._ae_enforce_stock_guard()
        res = super().action_confirm()
        _logger.warning("[StockGuard] action_confirm EXIT OK for %s", ", ".join(self.mapped('name')))
        return res

    def write(self, vals):
        if 'state' in vals and vals.get('state') == 'sale':
            _logger.warning("[StockGuard] write(state='sale') ENTER for %s", ", ".join(self.mapped('name')))
            self._ae_enforce_stock_guard()
            _logger.warning("[StockGuard] write(state='sale') PASSED guard for %s", ", ".join(self.mapped('name')))
        return super().write(vals)
