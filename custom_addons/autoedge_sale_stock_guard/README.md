# AutoEdge • Block SO Confirm on Insufficient On-Hand Stock

Blocks confirmation of Sale Orders when **real, free on-hand stock** is insufficient
for any storable product line. It **ignores forecasted/incoming** quantities by design.

- Scope: per **SO warehouse** (`warehouse` context).
- Uses `free_qty` (on hand minus reserved); falls back to `qty_available - outgoing_qty`.
- Converts correctly to the sale line UoM without rounding up.
- Skips services/notes/sections.

## Dependencies
- `sale_stock`

## Install
Standard app install. No settings, no views, no access rules.

## Behavior
- If any line requires more than currently free on-hand, `action_confirm()` raises a `UserError` listing all problematic lines.
