<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- TDC: CSS overrides -->
  <template id="external_layout_tdc_minimal_css"
            inherit_id="ae_commercial_docs_custom.external_layout_tdc_minimal">
    <xpath expr="//div[contains(@class, 'header')]" position="after">
      <style type="text/css"><![CDATA[
        /* Hide any Discount column if a template still exposes discount hooks */
        th[name="th_discount"], td[name="td_discount"] { display: none !important; }

        /* Hide native meta row under the header (Sale + Invoice use this id) */
        .tdc_body #informations, .article #informations { display: none !important; }
      ]]></style>
    </xpath>
  </template>

  <!-- TDS: same overrides -->
  <template id="external_layout_tds_minimal_css"
            inherit_id="ae_commercial_docs_custom.external_layout_tds_minimal">
    <xpath expr="//div[contains(@class, 'header')]" position="after">
      <style type="text/css"><![CDATA[
        th[name="th_discount"], td[name="td_discount"] { display: none !important; }
        .tdc_body #informations, .article #informations { display: none !important; }
      ]]></style>
    </xpath>
  </template>

</odoo>
