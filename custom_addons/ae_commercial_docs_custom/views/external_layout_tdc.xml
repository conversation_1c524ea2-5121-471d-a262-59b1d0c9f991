<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <template id="external_layout_tdc_minimal"
            name="External Layout – TDC Minimal (Header exact)"
            t-name="ae_commercial_docs_custom.external_layout_tdc_minimal">

    <t t-set="doc" t-value="doc or o or (docs and docs[0]) or None"/>
    <t t-set="company"
       t-value="res_company
                or (o and o.company_id)
                or (doc and doc._fields.get('company_id') and doc.company_id)
                or env.company"/>

    <!-- ========== HEADER ========== -->
    <div t-attf-class="header o_company_#{company.id}_layout" t-att-style="report_header_style">
      <div class="row" style="margin-top:0;">
        <!-- Left: Logo + company details -->
        <div class="col-6" style="font-size:12px; line-height:1.4;">
          <div class="o_tdc_topbar" style="display:flex; align-items:flex-start;">
            <img t-if="company.logo"
                 t-att-src="image_data_uri(company.logo)"
                 alt="Logo"
                 style="width:200px; height:auto;"/>
          </div>
          <div style="margin-top:8px;">
            <div t-if="company._fields.get('company_details')" t-field="company.company_details"/>
          </div>
        </div>

        <!-- Right: Customer details -->
        <div class="col-6 text-end" style="font-size:12px; line-height:1.4;">
          <t t-set="p"
             t-value="doc and (doc._fields.get('commercial_partner_id') and doc.commercial_partner_id)
                      or (doc and doc._fields.get('partner_id') and doc.partner_id)
                      or (o and o._fields.get('partner_id') and o.partner_id)
                      or None"/>
          <div style="font-weight:700;"><t t-esc="(p and (p.commercial_company_name or p.name)) or ''"/></div>
          <div t-if="p and p.company_registry" style="margin-top:6px;">Company Reg: <t t-esc="p.company_registry"/></div>
          <div t-if="p and p.vat" style="margin-top:2px;">
            <t t-esc="(p.country_id and p.country_id.vat_label) or 'VAT Reg'"/>:
            <t t-esc="p.vat"/>
          </div>
          <div style="margin-top:6px;">
            <div t-if="p and p.street"><t t-esc="p.street"/></div>
            <div t-if="p and p.street2"><t t-esc="p.street2"/></div>
            <div t-if="p and (p.city or p.state_id or p.zip)">
              <t t-esc="p.city or ''"/> <t t-esc="p.state_id and p.state_id.name or ''"/> <t t-esc="p.zip or ''"/>
            </div>
            <div t-if="p and p.country_id"><t t-esc="p.country_id.name"/></div>
          </div>
          <div style="margin-top:6px;">
            <t t-if="p and p.phone">Tel: <t t-esc="p.phone"/></t>
            <t t-if="p and p.email"><t t-raw="'&#160;&#8226;&#160;'"/><t t-esc="p.email"/></t>
          </div>
        </div>
      </div>
    </div>

    <!-- ========== BODY WRAPPER ========== -->
    <t t-set="layout_background_url"
       t-value="(company.layout_background and company.layout_background != 'Blank')
                and ('data:image/png;base64,%s' % company.layout_background_image) or ''"/>
    <t t-set="article_classes"
       t-value="'article o_report_layout_bold o_table_bold o_report_layout_company_%s' % company.id
                + ((company.layout_background and company.layout_background != 'Blank') and ' o_report_layout_background' or '')"/>
    <t t-set="article_style"
       t-value="layout_background_url and ('background-image: url(%s);' % layout_background_url) or ''"/>

    <div t-att-class="article_classes"
         t-att-style="article_style"
         t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id"
         t-att-data-oe-lang="o and o.env.context.get('lang')">

      <div class="o_tdc_header_spacer" style="height:48px;"/>

      <!-- ===== Info band (BLUE) ===== -->
      <t t-set="is_inv"  t-value="doc and doc._name == 'account.move'"/>
      <t t-set="is_so"   t-value="doc and doc._name == 'sale.order'"/>
      <t t-set="is_pick" t-value="doc and doc._name == 'stock.picking'"/>

      <t t-set="ref_label"  t-value="(is_inv or is_so) and 'Customer Reference' or 'Reference'"/>
      <t t-set="date_label" t-value="is_inv and 'Invoice Date' or is_so and 'Order Date' or is_pick and 'Scheduled Date' or 'Date'"/>
      <t t-set="extra_label" t-value="is_so and 'Expiration' or 'Source Document'"/>

      <t t-set="cust_ref"
         t-value="(doc and doc._fields.get('client_order_ref') and doc.client_order_ref)
                  or (doc and doc._fields.get('ref') and doc.ref)
                  or (doc and doc._fields.get('payment_reference') and doc.payment_reference)
                  or (doc and doc._fields.get('name') and doc.name)
                  or ''"/>

      <t t-set="doc_date"
         t-value="(doc and doc._fields.get('invoice_date') and doc.invoice_date)
                  or (doc and doc._fields.get('date_order') and doc.date_order)
                  or (doc and doc._fields.get('scheduled_date') and doc.scheduled_date)
                  or (doc and doc._fields.get('date_done') and doc.date_done)
                  or False"/>

      <!-- Salesperson (name) -->
      <t t-set="salesperson"
         t-value="(doc and doc._fields.get('invoice_user_id') and doc.invoice_user_id and doc.invoice_user_id.name)
                  or (doc and doc._fields.get('user_id') and doc.user_id and doc.user_id.name)
                  or (doc and doc._fields.get('create_uid') and doc.create_uid and doc.create_uid.name)
                  or ''"/>
      <!-- Salesperson (record) and email for footer -->
      <t t-set="salesperson_user"
         t-value="(doc and doc._fields.get('invoice_user_id') and doc.invoice_user_id)
                  or (doc and doc._fields.get('user_id') and doc.user_id)
                  or (doc and doc._fields.get('create_uid') and doc.create_uid)
                  or False"/>
      <t t-set="salesperson_email"
         t-value="(salesperson_user and salesperson_user._fields.get('partner_id') and salesperson_user.partner_id and salesperson_user.partner_id.email) or ''"/>

      <div class="o_blue_band"
           style="background:#262a5a; color:#fff; border-radius:6px; padding:10px 12px; margin-top:16px; margin-bottom:16px;">
        <div class="row">
          <div class="col-3">
            <div style="font-size:10px; text-transform:uppercase; opacity:0.8;"><t t-esc="ref_label"/></div>
            <div style="font-size:13px; font-weight:700;"><t t-esc="cust_ref or '-'"/></div>
          </div>
          <div class="col-3">
            <div style="font-size:10px; text-transform:uppercase; opacity:0.8;"><t t-esc="date_label"/></div>
            <div style="font-size:13px; font-weight:700;">
              <span t-if="doc_date" t-esc="doc_date" t-options="{'widget':'date'}"/>
              <t t-if="not doc_date">-</t>
            </div>
          </div>
          <div class="col-3">
            <div style="font-size:10px; text-transform:uppercase; opacity:0.8;">
              <t t-esc="is_inv and 'Account Manager' or is_so and 'Salesperson' or 'Responsible'"/>
            </div>
            <div style="font-size:13px; font-weight:700;"><t t-esc="salesperson or '-'"/></div>
          </div>
          <div class="col-3">
            <div style="font-size:10px; text-transform:uppercase; opacity:0.8;"><t t-esc="extra_label"/></div>
            <div style="font-size:13px; font-weight:700;">
              <span t-if="is_so and doc and doc._fields.get('validity_date') and doc.validity_date"
                    t-esc="doc.validity_date" t-options="{'widget':'date'}"/>
              <t t-if="is_so and (not (doc and doc._fields.get('validity_date') and doc.validity_date))">-</t>
              <t t-if="not is_so">
                <t t-esc="(doc and doc._fields.get('invoice_origin') and doc.invoice_origin)
                          or (doc and doc._fields.get('origin') and doc.origin)
                          or '-'"/>
              </t>
            </div>
          </div>
        </div>
      </div>

      <!-- ===== Title + Barcode ===== -->
      <t t-set="doc_model" t-value="(doc and doc._name) or (o and o._name) or ''"/>
      <t t-set="doc_title" t-value="'Document'"/>

      <t t-if="doc_model == 'sale.order'">
        <t t-set="doc_title" t-value="(doc.state in ('draft','sent')) and 'Quotation' or 'Sales Order'"/>
      </t>
      <t t-elif="doc_model == 'account.move'">
        <t t-if="doc.move_type == 'out_invoice'">
          <t t-set="doc_title" t-value="(doc.state == 'draft') and 'Draft Invoice' or (doc.state == 'cancel' and 'Cancelled Invoice' or 'Invoice')"/>
        </t>
        <t t-elif="doc.move_type == 'out_refund'">
          <t t-set="doc_title" t-value="(doc.state == 'draft') and 'Draft Credit Note' or (doc.state == 'cancel' and 'Cancelled Credit Note' or 'Credit Note')"/>
        </t>
        <t t-elif="doc.move_type == 'in_invoice'"><t t-set="doc_title" t-value="'Vendor Bill'"/></t>
        <t t-elif="doc.move_type == 'in_refund'"><t t-set="doc_title" t-value="'Vendor Credit Note'"/></t>
        <t t-elif="doc.move_type == 'out_receipt'"><t t-set="doc_title" t-value="'Receipt'"/></t>
      </t>
      <t t-elif="doc_model == 'purchase.order'">
        <t t-set="doc_title" t-value="(doc.state in ('draft','sent')) and 'Request for Quotation' or 'Purchase Order'"/>
      </t>
      <t t-elif="doc_model == 'stock.picking'">
        <t t-set="doc_title"
           t-value="(doc.picking_type_id and doc.picking_type_id.code == 'outgoing' and 'Delivery Slip')
                    or (doc.picking_type_id and doc.picking_type_id.code == 'incoming' and 'Incoming Shipment')
                    or 'Transfer'"/>
      </t>

      <t t-set="doc_code"
         t-value="(doc and doc._fields.get('name') and doc.name and doc.name != '/' and doc.name)
                  or (doc and doc._fields.get('payment_reference') and doc.payment_reference)
                  or (doc and doc._fields.get('ref') and doc.ref)
                  or (doc and doc._fields.get('invoice_origin') and doc.invoice_origin)
                  or (doc and doc._fields.get('origin') and doc.origin)
                  or (doc and doc.id and ('%s' % doc.id))
                  or ''"/>

      <t t-set="safe_code" t-value="doc_code and doc_code.replace(' ', '%20').replace('&amp;', '%26').replace('/', '%2F') or ''"/>
      <t t-set="barcode_url" t-value="'/report/barcode/Code128/' + safe_code + '?width=600&amp;height=100&amp;humanreadable=1'"/>

      <div class="row" style="align-items:flex-end; margin:6px 0 12px 0;">
        <div class="col-6">
          <img t-if="safe_code" t-att-src="barcode_url" alt="Barcode" style="height:40px;"/>
        </div>
        <div class="col-6 text-end">
          <h2 style="margin:0; font-weight:800; font-size:18px; color:#262a5a;">
            <t t-esc="doc_title"/>
            <span t-if="doc_code" style="margin-left:8px;" t-esc="doc_code"/>
          </h2>
        </div>
      </div>

      <!-- Body placeholder -->
      <div class="tdc_body">
        <t t-raw="0"/>
      </div>
    </div>

    <!-- ========== FOOTER (3 columns) ========== -->
    <div t-attf-class="footer o_company_#{company.id}_layout {{report_type != 'pdf' and 'mt-auto'}}">
      <div class="o_footer_content" style="border-top:2px solid #262a5a; padding-top:10px; margin-top:24px;">
        <div class="row" style="font-size:11px; line-height:1.5;">
          <!-- Left: Company contact details (with Company/VAT Reg, salesperson email) -->
          <div class="col-4">
            <div style="font-weight:700;"><t t-esc="company.name"/></div>
            <address class="mb-1" t-field="company.partner_id"
                     t-options="{'widget':'contact', 'fields':['address'], 'no_marker': True}"/>
            <div>Company Reg: 2011/146974/07</div>
            <div>VAT Reg: **********</div>
            <div t-if="company.partner_id.phone">Tel: <t t-esc="company.partner_id.phone"/></div>
            <div t-if="salesperson_email">Email: <t t-esc="salesperson_email"/></div>
            <div t-if="company.website">Web: <t t-esc="company.website"/></div>
          </div>

          <!-- Center: Banking details (TDC) -->
          <div class="col-4 text-center">
            <div style="font-weight:700; text-transform:uppercase; letter-spacing:.5px;">Banking Details</div>
            <div style="font-weight:700;">TURBO DIESEL COMPONENTS</div>
            <div>ABSA BANK</div>
            <div>KEMPTON PARK</div>
            <div>CHEQUE ACCOUNT</div>
            <div>Account No: **********</div>
            <div>Branch Code: 632005</div>
          </div>

          <!-- Right: blank -->
          <div class="col-4"><!-- intentionally blank --></div>
        </div>

        <!-- Page numbers (PDF only) -->
        <div class="row mt-2" t-if="report_type == 'pdf'">
          <div class="col-12 text-end text-muted">
            Page <span class="page"/> / <span class="topage"/>
          </div>
        </div>
      </div>
    </div>
  </template>
</odoo>
