<?xml version="1.0" encoding="utf-8"?>
<odoo>

  <!-- NEW records with fresh XML IDs -->
  <record id="report_layout_tdc_minimal_v2" model="report.layout">
    <field name="name">TDC Minimal</field>
    <field name="sequence">20</field>
    <field name="view_id" ref="ae_commercial_docs_custom.external_layout_tdc_minimal"/>
  </record>

  <record id="report_layout_tds_minimal_v2" model="report.layout">
    <field name="name">TDS Minimal</field>
    <field name="sequence">21</field>
    <field name="view_id" ref="ae_commercial_docs_custom.external_layout_tds_minimal"/>
  </record>

  <!-- Archive any older conflicting records so their chips disappear -->
  <record id="report_layout_tdc_minimal" model="report.layout">
    <field name="active" eval="False"/>
  </record>
  <record id="report_layout_tds_minimal" model="report.layout">
    <field name="active" eval="False"/>
  </record>

</odoo>
