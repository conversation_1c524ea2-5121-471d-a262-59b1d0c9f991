<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- ============================================================
       SALE ORDER / QUOTATION
       Replace the FIRST main lines table. Do not depend on 'o'.
       Iterate via 'docs' -> 'so'.
       ============================================================ -->
  <template id="sale_replace_lines_table_netunit"
            inherit_id="sale.report_saleorder_document"
            name="Sale: Replace lines table with Net Unit version (docs-safe)">
    <xpath expr="(//table[contains(@class,'o_main_table')])[1]" position="replace">
      <table class="table table-sm table-striped o_main_table">
        <thead>
          <tr>
            <th>Description</th>
            <th class="text-end">Qty</th>
            <th class="text-end">Unit Price (Net)</th>
            <th class="text-end">Taxes</th>
            <th class="text-end">Subtotal</th>
          </tr>
        </thead>
        <tbody>
          <!-- Always available in reports: 'docs' -->
          <t t-foreach="docs" t-as="so">
            <t t-foreach="so.order_line" t-as="l">
              <!-- Section -->
              <tr t-if="l.display_type == 'line_section'" class="o_line_section">
                <td colspan="5"><strong t-esc="l.name"/></td>
              </tr>
              <!-- Note -->
              <tr t-elif="l.display_type == 'line_note'" class="o_line_note">
                <td colspan="5"><em t-esc="l.name"/></td>
              </tr>
              <!-- Normal line -->
              <tr t-else="">
                <td>
                  <div><span t-esc="l.name"/></div>
                </td>
                <td class="text-end">
                  <span t-esc="l.product_uom_qty"/>
                  <span t-if="l.product_uom" t-esc="l.product_uom.name"/>
                </td>
                <td class="text-end">
                  <t t-set="net_unit" t-value="(l.price_unit or 0.0) * (1.0 - (l.discount or 0.0)/100.0)"/>
                  <span t-esc="net_unit"
                        t-options="{'widget':'monetary','display_currency': so.currency_id}"/>
                </td>
                <td class="text-end">
                  <span t-esc="', '.join(l.tax_id.mapped('name')) if l.tax_id else '-'"/>
                </td>
                <td class="text-end">
                  <span t-field="l.price_subtotal"
                        t-options="{'widget':'monetary','display_currency': so.currency_id}"/>
                </td>
              </tr>
            </t>
          </t>
        </tbody>
      </table>
    </xpath>
  </template>

  <!-- ============================================================
       INVOICE / CREDIT NOTE
       Replace the FIRST main lines table. Iterate via 'docs' -> 'inv'.
       ============================================================ -->
  <template id="invoice_replace_lines_table_netunit"
            inherit_id="account.report_invoice_document"
            name="Account: Replace lines table with Net Unit version (docs-safe)">
    <xpath expr="(//table[contains(@class,'o_main_table')])[1]" position="replace">
      <table class="table table-sm table-striped o_main_table">
        <thead>
          <tr>
            <th>Description</th>
            <th class="text-end">Qty</th>
            <th class="text-end">Unit Price (Net)</th>
            <th class="text-end">Taxes</th>
            <th class="text-end">Subtotal</th>
          </tr>
        </thead>
        <tbody>
          <t t-foreach="docs" t-as="inv">
            <t t-foreach="inv.invoice_line_ids" t-as="l">
              <!-- Section -->
              <tr t-if="l.display_type == 'line_section'" class="o_line_section">
                <td colspan="5"><strong t-esc="l.name"/></td>
              </tr>
              <!-- Note -->
              <tr t-elif="l.display_type == 'line_note'" class="o_line_note">
                <td colspan="5"><em t-esc="l.name"/></td>
              </tr>
              <!-- Normal line -->
              <tr t-else="">
                <td>
                  <div><span t-esc="l.name"/></div>
                </td>
                <td class="text-end">
                  <span t-esc="l.quantity"/>
                  <span t-if="l.product_uom_id" t-esc="l.product_uom_id.name"/>
                </td>
                <td class="text-end">
                  <t t-set="net_unit" t-value="(l.price_unit or 0.0) * (1.0 - (l.discount or 0.0)/100.0)"/>
                  <span t-esc="net_unit"
                        t-options="{'widget':'monetary','display_currency': inv.currency_id}"/>
                </td>
                <td class="text-end">
                  <span t-esc="', '.join(l.tax_ids.mapped('name')) if l.tax_ids else '-'"/>
                </td>
                <td class="text-end">
                  <span t-field="l.price_subtotal"
                        t-options="{'widget':'monetary','display_currency': inv.currency_id}"/>
                </td>
              </tr>
            </t>
          </t>
        </tbody>
      </table>
    </xpath>
  </template>

</odoo>
