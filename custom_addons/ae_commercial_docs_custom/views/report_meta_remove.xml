<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- SALE ORDER / QUOTATION
       Remove EVERYTHING that appears before the first main lines table.
       This nukes the native meta row no matter how it’s structured. -->
  <template id="sale_strip_before_main_table"
            inherit_id="sale.report_saleorder_document"
            name="Sale: strip blocks before first main table">
    <xpath expr="(//table[contains(@class,'o_main_table')])[1]/preceding-sibling::*"
           position="replace">
      <t/>
    </xpath>
  </template>

  <!-- INVOICE / CREDIT NOTE
       Same approach for account reports. -->
  <template id="account_strip_before_main_table"
            inherit_id="account.report_invoice_document"
            name="Account: strip blocks before first main table">
    <xpath expr="(//table[contains(@class,'o_main_table')])[1]/preceding-sibling::*"
           position="replace">
      <t/>
    </xpath>
  </template>

</odoo>
