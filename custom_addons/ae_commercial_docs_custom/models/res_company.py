# -*- coding: utf-8 -*-
from odoo import models


class ResCompany(models.Model):
    _inherit = 'res.company'

    def _get_report_layouts(self):
        """
        Expose our custom layouts as selectable badges in the Document Layout wizard.
        IMPORTANT: tuple format is (external_id_of_layout_template, Display Name).
        """
        layouts = super()._get_report_layouts()
        entries = [
            ('ae_commercial_docs_custom.external_layout_tdc_minimal', 'TDC Minimal'),
            ('ae_commercial_docs_custom.external_layout_tds_minimal', 'TDS Minimal'),
        ]
        for e in entries:
            if e not in layouts:
                layouts.append(e)
        return layouts

    def _get_report_layout_preview(self):
        """
        Map layout XML IDs to preview template XML IDs for the wizard.
        """
        previews = super()._get_report_layout_preview()
        previews['ae_commercial_docs_custom.external_layout_tdc_minimal'] = \
            'ae_commercial_docs_custom.external_layout_tdc_minimal_preview'
        previews['ae_commercial_docs_custom.external_layout_tds_minimal'] = \
            'ae_commercial_docs_custom.external_layout_tds_minimal_preview'
        return previews
