from odoo import models, fields

class CoreClass(models.Model):
    _name = 'core.class'
    _description = 'Core Charge Class'

    name = fields.Char(required=True)
    product_id = fields.Many2one(
        'product.product',
        string="Core Charge Product",
        required=True,
        domain=[('type', '=', 'service')]
    )
    description = fields.Text()

    rating_line_ids = fields.One2many(
        'core.class.rating.line',
        'core_class_id',
        string="Grading Rules"
    )

    criteria_line_ids = fields.One2many(
        'core.class.criteria.line',
        'core_class_id',
        string="Grading Criteria"
    )


class CoreClassRatingLine(models.Model):
    _name = 'core.class.rating.line'
    _description = 'Return Rating with Refund Rule'

    core_class_id = fields.Many2one(
        'core.class',
        string="Core Class",
        required=True,
        ondelete='cascade'
    )
    rating_name = fields.Char(string="Grading Name", required=True)
    refund_percent = fields.Float(string="Refund %", required=True)


class CoreClassCriteriaLine(models.Model):
    _name = 'core.class.criteria.line'
    _description = 'Core Grading Criteria'

    core_class_id = fields.Many2one(
        'core.class',
        string="Core Class",
        required=True,
        ondelete='cascade'
    )
    name = fields.Char(string="Grading Question", required=True)
    weight = fields.Float(string="Score %", required=True)
    is_positive = fields.Boolean(
        string="Is Positive?",
        default=True,
        help="If checked, 'Yes' adds to the score. If unchecked, 'Yes' subtracts from the score."
    )
