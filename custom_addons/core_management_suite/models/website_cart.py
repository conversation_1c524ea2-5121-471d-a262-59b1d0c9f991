from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo import http
from odoo.http import request
from odoo.tools.translate import _

class WebsiteSaleCoreCharge(WebsiteSale):

    @http.route(['/shop/cart/update_json'], type='json', auth="public", website=True)
    def cart_update_json(self, product_id, line_id=None, add_qty=0, set_qty=0, display=True):
        response = super().cart_update_json(
            product_id=product_id,
            line_id=line_id,
            add_qty=add_qty,
            set_qty=set_qty,
            display=display
        )

        order = request.website.sale_get_order()
        if not order:
            return response

        product = request.env['product.product'].sudo().browse(int(product_id))
        if not product.exists():
            return response

        base_line = order.order_line.filtered(
            lambda l: l.product_id.id == product.id and not l.is_core_line
        )
        if not base_line:
            # Base product was removed → remove core line
            tmpl = product.product_tmpl_id
            if tmpl.core_class_id:
                core_product = tmpl.core_class_id.product_id
                core_line = order.order_line.filtered(
                    lambda l: l.product_id.id == core_product.id and l.is_core_line
                )
                if core_line:
                    core_line.unlink()
            return response

        # Add or update core line
        tmpl = product.product_tmpl_id
        if not tmpl.core_class_id:
            return response

        core_product = tmpl.core_class_id.product_id
        core_line = order.order_line.filtered(
            lambda l: l.product_id.id == core_product.id and l.is_core_line
        )

        if core_line:
            core_line.write({
                'product_uom_qty': base_line[0].product_uom_qty,
                'discount': 0.0
            })
        else:
            order.order_line.create({
                'order_id': order.id,
                'product_id': core_product.id,
                'product_uom_qty': base_line[0].product_uom_qty,
                'price_unit': core_product.lst_price,
                'discount': 0.0,
                'is_core_line': True,
            })

        return response

    @http.route(['/shop/cart/update'], type='http', auth="public", website=True)
    def cart_update(self, product_id, line_id=None, add_qty=1, set_qty=0, **kw):
        if line_id:
            line = request.env['sale.order.line'].browse(int(line_id))
            if line.is_core_line:
                return request.redirect("/shop/cart")
        return super().cart_update(product_id, line_id=line_id, add_qty=add_qty, set_qty=set_qty, **kw)
