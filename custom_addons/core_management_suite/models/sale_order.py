from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    is_core_charge = fields.Boolean(default=False, string="Core Charge Line")

    @api.onchange('product_id')
    def _onchange_product_id_core_charge(self):
        if self.product_id and self.product_id.product_tmpl_id.core_class_id:
            core_product = self.product_id.product_tmpl_id.core_class_id.product_id

            self._warning_core_charge(core_product)

            if self.order_id:
                existing = self.order_id.order_line.filtered(lambda l: l.product_id == core_product)
                if not existing:
                    self.order_id.order_line += self.env['sale.order.line'].new({
                        'product_id': core_product.id,
                        'product_uom_qty': self.product_uom_qty,
                        'price_unit': core_product.lst_price,
                        'name': f'Core Charge: {core_product.display_name}',
                        'order_id': self.order_id.id,
                        'is_core_charge': True,
                        'discount': 0.0,
                    })

    @api.onchange('product_uom_qty')
    def _onchange_product_uom_qty_core_sync(self):
        if self.product_id and self.product_id.product_tmpl_id.core_class_id and self.order_id:
            core_product = self.product_id.product_tmpl_id.core_class_id.product_id
            core_line = self.order_id.order_line.filtered(lambda l: l.product_id == core_product)
            if core_line:
                core_line.product_uom_qty = self.product_uom_qty

    def _warning_core_charge(self, core_product):
        return {
            'warning': {
                'title': _("Core Charge Notice"),
                'message': _(
                    "The selected product has a compulsory Core Charge: %s.\n\n"
                    "This has been automatically added to the quotation."
                ) % core_product.display_name,
            }
        }

    def unlink(self):
        for line in self:
            if line.is_core_charge:
                raise UserError(_("You are not allowed to remove a Core Charge from the quotation or sales order."))

            core_class = line.product_id.product_tmpl_id.core_class_id
            if core_class and core_class.product_id:
                core_line = self.order_id.order_line.filtered(
                    lambda l: l.product_id == core_class.product_id and l.is_core_charge
                )
                if core_line:
                    core_line.unlink()
        return super().unlink()

    def write(self, vals):
        result = super().write(vals)
        for line in self:
            if not line.is_core_charge and 'product_uom_qty' in vals:
                line.order_id._update_core_charge(line.product_id, vals['product_uom_qty'])
        return result


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.onchange('order_line')
    def _onchange_order_line_core_class_protection(self):
        core_products_needed = []

        for line in self.order_line:
            core_class = line.product_id.product_tmpl_id.core_class_id
            if core_class and core_class.product_id:
                core_products_needed.append((line, core_class.product_id))

        for base_line, core_product in core_products_needed:
            core_line = self.order_line.filtered(lambda l: l.product_id == core_product)
            if not core_line:
                self.order_line += self.env['sale.order.line'].new({
                    'product_id': core_product.id,
                    'product_uom_qty': base_line.product_uom_qty,
                    'price_unit': core_product.lst_price,
                    'name': f'Core Charge: {core_product.display_name}',
                    'is_core_charge': True,
                    'discount': 0.0,
                })
            else:
                core_line.product_uom_qty = base_line.product_uom_qty

    def _cart_update(self, product_id=None, add_qty=0, set_qty=0, **kwargs):
        result = super()._cart_update(product_id=product_id, add_qty=add_qty, set_qty=set_qty, **kwargs)

        if not product_id or not result.get('line_id'):
            return result

        line = self.order_line.filtered(lambda l: l.id == result['line_id'])
        if not line:
            return result

        tmpl = line.product_id.product_tmpl_id
        if not tmpl or not tmpl.core_class_id or not tmpl.core_class_id.product_id:
            return result

        core_product = tmpl.core_class_id.product_id
        core_line = self.order_line.filtered(lambda l: l.product_id == core_product)

        if core_line:
            core_line.product_uom_qty = line.product_uom_qty
        else:
            self.order_line += self.env['sale.order.line'].new({
                'product_id': core_product.id,
                'product_uom_qty': line.product_uom_qty,
                'price_unit': core_product.lst_price,
                'name': f'Core Charge: {core_product.display_name}',
                'is_core_charge': True,
                'discount': 0.0,
            })

        return result

    def _update_core_charge(self, product, qty):
        core_class = product.product_tmpl_id.core_class_id
        if not core_class or not core_class.product_id:
            return

        core_product = core_class.product_id
        core_line = self.order_line.filtered(lambda l: l.product_id == core_product)
        if core_line:
            core_line.write({'product_uom_qty': qty})

    def write(self, vals):
        core_lines_to_remove = self.env['sale.order.line']
        if 'order_line' in vals:
            removed_ids = [cmd[1] for cmd in vals['order_line'] if cmd[0] == 2]
            if removed_ids:
                removed_lines = self.env['sale.order.line'].sudo().browse(removed_ids)
                for line in removed_lines:
                    if not line.is_core_charge and line.product_id.product_tmpl_id.core_class_id:
                        core_product = line.product_id.product_tmpl_id.core_class_id.product_id
                        core_line = self.env['sale.order.line'].search([
                            ('order_id', '=', line.order_id.id),
                            ('product_id', '=', core_product.id),
                            ('is_core_charge', '=', True)
                        ])
                        if core_line:
                            core_lines_to_remove |= core_line

        result = super().write(vals)

        if core_lines_to_remove:
            core_lines_to_remove.unlink()

        return result

    def _prepare_order_line_template_values(self, line):
        values = super()._prepare_order_line_template_values(line)
        values['is_core_charge'] = line.is_core_charge
        return values
