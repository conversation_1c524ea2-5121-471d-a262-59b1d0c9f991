from odoo import models, fields, api
from odoo.exceptions import UserError

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    is_core_line = fields.Boolean(
        string="Is Core Charge Line",
        default=False,
        readonly=True,
        help="Automatically added core charge line. Cannot be deleted manually."
    )

    @api.model_create_multi
    def create(self, vals_list):
        lines = super().create(vals_list)
        for line in lines:
            move = line.move_id
            if (
                move.move_type in ['out_invoice', 'out_refund']
                and line.product_id
                and not line.is_core_line
                and line.product_id.product_tmpl_id.core_class_id
            ):
                core_product = line.product_id.product_tmpl_id.core_class_id.product_id
                if core_product:
                    existing = move.invoice_line_ids.filtered(
                        lambda l: l.product_id.id == core_product.id and l.is_core_line
                    )
                    if not existing:
                        self.create({
                            'move_id': move.id,
                            'product_id': core_product.id,
                            'quantity': line.quantity,
                            'price_unit': core_product.lst_price,
                            'discount': 0.0,
                            'is_core_line': True,
                        })
        return lines

    def write(self, vals):
        for line in self:
            if line.is_core_line:
                # Force core charge lines to always have discount = 0
                vals['discount'] = 0.0
        res = super().write(vals)
        for line in self:
            move = line.move_id
            if (
                move.move_type in ['out_invoice', 'out_refund']
                and not line.is_core_line
                and 'quantity' in vals
                and line.product_id.product_tmpl_id.core_class_id
            ):
                core_product = line.product_id.product_tmpl_id.core_class_id.product_id
                core_line = move.invoice_line_ids.filtered(
                    lambda l: l.product_id.id == core_product.id and l.is_core_line
                )
                if core_line:
                    core_line.write({'quantity': line.quantity})
        return res

    def unlink(self):
        core_lines_to_remove = self.env['account.move.line']
        for line in self:
            move = line.move_id
            if line.is_core_line:
                raise UserError("You cannot delete a core charge line manually.")
            if (
                move.move_type in ['out_invoice', 'out_refund']
                and line.product_id
                and line.product_id.product_tmpl_id.core_class_id
            ):
                core_product = line.product_tmpl_id.core_class_id.product_id
                core_line = move.invoice_line_ids.filtered(
                    lambda l: l.product_id.id == core_product.id and l.is_core_line
                )
                if core_line:
                    core_lines_to_remove |= core_line
        res = super().unlink()
        if core_lines_to_remove:
            core_lines_to_remove.unlink()
        return res

class PortalAccountMoveLine(models.Model):
    _inherit = 'account.move'

    def write(self, vals):
        if self.env.context.get('website_sale_signature'):
            for move in self:
                for line in move.invoice_line_ids:
                    if line.is_core_line:
                        if any(k in vals for k in ['product_id', 'quantity', 'discount']):
                            raise UserError("Core charge lines cannot be modified from the portal.")
        return super().write(vals)
