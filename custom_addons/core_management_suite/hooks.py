from odoo import SUPERUSER_ID, api
from odoo.modules.module import get_module_resource
from odoo.tools.convert import convert_file

def load_delayed_views(env):
    module = 'core_management_suite'
    view_path = get_module_resource(module, 'views', 'core_class_views.xml')

    convert_file(
        env,           # ✅ pass full env, not env.cr
        module, 
        view_path,
        {},            # idref
        mode='init',
        noupdate=True
    )
