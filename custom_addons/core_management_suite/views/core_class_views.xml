<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Core Class Form View -->
    <record id="view_core_class_form" model="ir.ui.view">
        <field name="name">core.class.form</field>
        <field name="model">core.class</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="product_id"/>
                        <field name="description"/>
                    </group>
                    <notebook>
                        <page string="Grading Rules">
                            <field name="rating_line_ids">
                                <list string="Grading Lines" editable="bottom">
                                    <field name="rating_name"/>
                                    <field name="refund_percent"/>
                                </list>
                            </field>
                        </page>
                        <page string="Grading Criteria">
                            <field name="criteria_line_ids">
                                <list string="Grading Questions" editable="bottom">
                                    <field name="name"/>
                                    <field name="weight"/>
                                    <field name="is_positive"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Core Class List View -->
    <record id="view_core_class_list" model="ir.ui.view">
        <field name="name">core.class.list</field>
        <field name="model">core.class</field>
        <field name="arch" type="xml">
            <list string="Core Classes">
                <field name="name"/>
                <field name="product_id"/>
            </list>
        </field>
    </record>

    <!-- Core Class Action -->
    <record id="action_core_class" model="ir.actions.act_window">
        <field name="name">Core Classes</field>
        <field name="res_model">core.class</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
