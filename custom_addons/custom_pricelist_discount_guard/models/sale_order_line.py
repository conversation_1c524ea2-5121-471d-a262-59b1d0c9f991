from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    x_initial_discount = fields.Float(string="Initial Discount", copy=False, readonly=True)

    @api.onchange('product_id')
    def _onchange_product_and_set_initial_discount(self):
        for line in self:
            # Wait until Odoo itself fills in the discount
            if not line.x_initial_discount and line.discount > 0:
                line.x_initial_discount = line.discount

    @api.model
    def create(self, vals):
        # Final safety check for backend-created lines
        if not vals.get('x_initial_discount') and 'discount' in vals:
            vals['x_initial_discount'] = vals['discount']
        return super().create(vals)

    def write(self, vals):
        for line in self:
            if 'discount' in vals:
                base_discount = line.x_initial_discount or 0.0
                new_discount = vals['discount']
                max_allowed = base_discount + 15.0

                if new_discount > max_allowed:
                    raise UserError(_(
                        "You are not allowed to increase the discount by more than 15%%.\n"
                        "Original: %.2f%% | Attempted: %.2f%% | Limit: %.2f%%"
                    ) % (base_discount, new_discount, max_allowed))

        return super().write(vals)

    @api.onchange('discount')
    def _onchange_discount_check(self):
        for line in self:
            base_discount = line.x_initial_discount or 0.0
            new_discount = line.discount
            max_allowed = base_discount + 15.0

            if new_discount > max_allowed:
                line.discount = base_discount
                raise UserError(_(
                    "You are not allowed to increase the discount by more than 15%%.\n"
                    "Original: %.2f%% | Attempted: %.2f%% | Limit: %.2f%%"
                ) % (base_discount, new_discount, max_allowed))
