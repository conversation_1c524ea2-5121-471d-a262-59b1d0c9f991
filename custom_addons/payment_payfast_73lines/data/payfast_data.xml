<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Payment Method Setup -->
    <record id="account_payment_method_payfast" model="account.payment.method">
        <field name="name">PayFast</field>
        <field name="code">payfast_73lines</field>
        <field name="payment_type">inbound</field>
    </record>

    <record id="payment_method_payfast" model="payment.method">
        <field name="name">PayFast</field>
        <field name="code">payfast_73lines</field>
        <field name="sequence">25</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment_payfast_73lines/static/src/img/payfast_73lines_icon.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
    </record>

    <!-- PayFast for Turbo Diesel Components (ID 3) -->
    <record id="provider_payfast_tdc" model="payment.provider">
        <field name="name">PayFast (TDC)</field>
        <field name="code">payfast_73lines</field>
        <field name="state">disabled</field>
        <field name="company_id" eval="3"/>
        <field name="module_id" ref="base.module_payment_payfast_73lines"/>
        <field name="image_128" type="base64" file="payment_payfast_73lines/static/src/img/payfast_73lines_icon.png"/>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="payfast_merchant_id">dummy</field>
        <field name="payfast_secret">dummy</field>
        <field name="payfast_passphrase">dummy</field>
        <field name="payment_method_ids"
               eval="[Command.set([ref('payment_payfast_73lines.payment_method_payfast')])]"/>
    </record>

    <!-- PayFast for Turbo Diesel Services (ID 7) -->
    <record id="provider_payfast_tds" model="payment.provider">
        <field name="name">PayFast (TDS)</field>
        <field name="code">payfast_73lines</field>
        <field name="state">disabled</field>
        <field name="company_id" eval="7"/>
        <field name="module_id" ref="base.module_payment_payfast_73lines"/>
        <field name="image_128" type="base64" file="payment_payfast_73lines/static/src/img/payfast_73lines_icon.png"/>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="payfast_merchant_id">dummy</field>
        <field name="payfast_secret">dummy</field>
        <field name="payfast_passphrase">dummy</field>
        <field name="payment_method_ids"
               eval="[Command.set([ref('payment_payfast_73lines.payment_method_payfast')])]"/>
    </record>

    <!-- PayFast for Turbo Diesel Group (ID 8) -->
    <record id="provider_payfast_tdg" model="payment.provider">
        <field name="name">PayFast (TDG)</field>
        <field name="code">payfast_73lines</field>
        <field name="state">disabled</field>
        <field name="company_id" eval="8"/>
        <field name="module_id" ref="base.module_payment_payfast_73lines"/>
        <field name="image_128" type="base64" file="payment_payfast_73lines/static/src/img/payfast_73lines_icon.png"/>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="payfast_merchant_id">dummy</field>
        <field name="payfast_secret">dummy</field>
        <field name="payfast_passphrase">dummy</field>
        <field name="payment_method_ids"
               eval="[Command.set([ref('payment_payfast_73lines.payment_method_payfast')])]"/>
    </record>

</odoo>
