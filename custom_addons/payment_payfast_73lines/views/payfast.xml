<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<template id="redirect_form">
		<form method="post" t-att-action="api_url">
			<input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

			<!-- <input type="hidden" name="data_set" t-att-data-action-url="tx_url" data-remove-me=""/> -->
			<input type="hidden" name="merchant_id" t-att-value="merchant_id" />
			<input type="hidden" name="merchant_key" t-att-value="merchant_key" />
			<input type="hidden" name="return_url" t-att-value="return_url" />
			<input type="hidden" name="cancel_url" t-att-value="cancel_url" />
			<input type="hidden" name="notify_url" t-att-value="notify_url" />
			<input type="hidden" name="name_first" t-att-value="name_first" />
			<input type="hidden" name="name_last" t-att-value="name_last" />
			<input type="hidden" name="email_address" t-att-value="email_address" />
			<input type="hidden" name="m_payment_id" t-att-value="m_payment_id" />
			<input type="hidden" name="amount" t-att-value="amount" />
			<input type="hidden" name="item_name" t-att-value="item_name" />
			<input type="hidden" name="item_description" t-att-value="item_description" />
			<input type="hidden" name="custom_int1" t-att-value="custom_int1" />
			<input type="hidden" name="custom_str1" t-att-value="custom_str1" />

			<!-- submit -->
			<button type="submit" width="100px" t-att-class="submit_class">
				<img t-if="not submit_txt" src="/payment_payfast_73lines/static/src/img/payfast_73lines_icon.png" />
				<span t-if="submit_txt">
					<t t-esc="submit_txt" />
					<span class="fa fa-long-arrow-right" />
				</span>
			</button>

		</form>
	</template>
</odoo>
