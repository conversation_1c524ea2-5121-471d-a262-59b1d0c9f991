from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import date
import logging

_logger = logging.getLogger(__name__)


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    pricing_lookup_pricelist_id = fields.Many2one(
        'product.pricelist',
        string="Pricelist"
    )
    pricing_lookup_quantity = fields.Float(string="Quantity", default=1.0)

    full_retail_price = fields.Float(
        string="Full Retail Price",
        compute="_compute_full_retail_price",
        store=False
    )
    final_price = fields.Float(
        string="Final Price",
        compute="_compute_final_price",
        store=False
    )
    discount_percent = fields.Float(
        string="Discount %",
        compute="_compute_discount_percent",
        store=False
    )

    currency_symbol = fields.Char(string="Retail Price Currency", compute="_compute_currency_symbol", store=False)
    final_currency_symbol = fields.Char(string="Final Price Currency", compute="_compute_final_currency_symbol", store=False)

    full_retail_display = fields.Char(string="Full Retail Price", compute="_compute_full_retail_display", store=False)
    final_price_display = fields.Char(string="Final Price", compute="_compute_final_price_display", store=False)

    available_pricelist_ids = fields.Many2many(
        'product.pricelist',
        compute="_compute_available_pricelist_ids",
        store=False
    )

    def _compute_available_pricelist_ids(self):
        settings = self.env['pricing.settings'].sudo().search([('company_id', '=', self.env.company.id)], limit=1)
        for product in self:
            product.available_pricelist_ids = settings.pricelist_ids

    def _compute_currency_symbol(self):
        settings = self.env['pricing.settings'].sudo().search([('company_id', '=', self.env.company.id)], limit=1)
        symbol = settings.default_pricelist_id.currency_id.symbol if settings and settings.default_pricelist_id and settings.default_pricelist_id.currency_id else ''
        for product in self:
            product.currency_symbol = symbol

    def _compute_final_currency_symbol(self):
        for product in self:
            symbol = product.pricing_lookup_pricelist_id.currency_id.symbol if product.pricing_lookup_pricelist_id and product.pricing_lookup_pricelist_id.currency_id else ''
            product.final_currency_symbol = symbol

    def _compute_full_retail_display(self):
        for rec in self:
            if rec.currency_symbol and rec.full_retail_price:
                rec.full_retail_display = f"{rec.currency_symbol} {rec.full_retail_price:.2f}"
            else:
                rec.full_retail_display = f"{rec.full_retail_price:.2f}" if rec.full_retail_price else ''

    def _compute_final_price_display(self):
        for rec in self:
            if rec.final_currency_symbol and rec.final_price:
                rec.final_price_display = f"{rec.final_currency_symbol} {rec.final_price:.2f}"
            else:
                rec.final_price_display = f"{rec.final_price:.2f}" if rec.final_price else ''

    @api.depends('standard_price')
    def _compute_full_retail_price(self):
        settings = self.env['pricing.settings'].sudo().search([('company_id', '=', self.env.company.id)], limit=1)
        for product in self:
            try:
                if product.standard_price and settings and settings.default_pricelist_id:
                    pricelist = settings.default_pricelist_id
                    price = pricelist.with_context(quantity=1)._get_product_price(
                        product, 1.0, self.env.user.partner_id
                    )
                    product.full_retail_price = price
                else:
                    product.full_retail_price = 0.0
            except Exception as e:
                _logger.error(f"Error computing full retail price for product {product.id}: {e}")
                product.full_retail_price = 0.0

    @api.depends('pricing_lookup_pricelist_id', 'pricing_lookup_quantity')
    def _compute_discount_percent(self):
        for product in self:
            try:
                if product.pricing_lookup_pricelist_id and product.pricing_lookup_quantity:
                    qty = product.pricing_lookup_quantity
                    pricelist = product.pricing_lookup_pricelist_id
                    applied_discount = 0.0

                    for item in pricelist.item_ids.filtered(lambda r: (
                        r.applied_on in ['3_global', '2_product_category', '1_product'] and
                        (r.min_quantity or 0.0) <= qty and
                        (not r.product_tmpl_id or r.product_tmpl_id.id == product.id)
                    )):
                        if item.compute_price == 'percentage':
                            applied_discount = item.percent_price
                        elif item.compute_price == 'fixed':
                            full_price = product.full_retail_price
                            if full_price:
                                applied_discount = 100 * (1 - (item.fixed_price / full_price))
                        break

                    product.discount_percent = round(applied_discount, 2)
                else:
                    product.discount_percent = 0.0
            except Exception as e:
                _logger.error(f"Error extracting discount from pricelist rule for product {product.id}: {e}")
                product.discount_percent = 0.0

    @api.depends('pricing_lookup_pricelist_id', 'pricing_lookup_quantity', 'full_retail_price', 'discount_percent')
    def _compute_final_price(self):
        for product in self:
            try:
                if product.pricing_lookup_pricelist_id and product.full_retail_price:
                    discount = product.discount_percent or 0.0
                    discounted_cny = product.full_retail_price * (1 - (discount / 100))

                    from_currency = self.env['pricing.settings'].sudo().search([('company_id', '=', self.env.company.id)], limit=1).default_pricelist_id.currency_id
                    to_currency = product.pricing_lookup_pricelist_id.currency_id
                    today = date.today()
                    company = self.env.company

                    if from_currency != to_currency:
                        converted_price = from_currency.with_company(company).with_context(date=today)._convert(
                            discounted_cny, to_currency, company, today
                        )
                    else:
                        converted_price = discounted_cny

                    product.final_price = converted_price
                else:
                    product.final_price = 0.0
            except Exception as e:
                _logger.error(f"Error computing final price for product {product.id}: {e}")
                product.final_price = 0.0

    @api.onchange('pricing_lookup_pricelist_id', 'pricing_lookup_quantity')
    def _onchange_live_price_update(self):
        self._compute_discount_percent()
        self._compute_final_price()
        self._compute_final_price_display()

    def action_reset_lookup_filters(self):
        for product in self:
            product.pricing_lookup_pricelist_id = False
            product.pricing_lookup_quantity = 1.0


class PricingSettings(models.Model):
    _name = 'pricing.settings'
    _description = 'Pricing Tool Configuration'

    name = fields.Char(string="Name", default=lambda self: self.env.company.name)
    company_id = fields.Many2one('res.company', required=True, default=lambda self: self.env.company)
    default_pricelist_id = fields.Many2one('product.pricelist', string="Default Pricelist")
    pricelist_ids = fields.Many2many('product.pricelist', string="Available Pricelists")
