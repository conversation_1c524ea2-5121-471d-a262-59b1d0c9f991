<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- Settings Form View -->
  <record id="view_pricing_settings_form" model="ir.ui.view">
    <field name="name">pricing.settings.form</field>
    <field name="model">pricing.settings</field>
    <field name="arch" type="xml">
      <form string="Pricing Tool Settings">
        <sheet>
          <group>
            <field name="name"/>
            <field name="company_id" readonly="1"/>
            <field name="default_pricelist_id"/>
            <field name="pricelist_ids" widget="many2many_tags"/>
          </group>
        </sheet>
      </form>
    </field>
  </record>

  <!-- Action and Menu -->
  <record id="action_pricing_settings" model="ir.actions.act_window">
    <field name="name">Pricing Tool Settings</field>
    <field name="res_model">pricing.settings</field>
    <field name="view_mode">list,form</field>
    <field name="help" type="html">
      <p>
        Define available pricelists and a default one per company. These will be shown in the product pricing tab for manual lookup.
      </p>
    </field>
  </record>

  <menuitem id="menu_pricing_settings_root" name="Pricing Tool" parent="stock.menu_stock_root" sequence="60"/>
  <menuitem id="menu_pricing_settings" name="Settings" parent="menu_pricing_settings_root" action="action_pricing_settings"/>

</odoo>
