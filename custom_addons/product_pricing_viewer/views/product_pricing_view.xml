<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- Inherit Product Template Form View -->
  <record id="view_product_form_with_pricelist_tab" model="ir.ui.view">
    <field name="name">product.template.form.inherit.pricelist.tab</field>
    <field name="model">product.template</field>
    <field name="inherit_id" ref="product.product_template_form_view"/>
    <field name="arch" type="xml">
      <xpath expr="//sheet/notebook/page[last()]" position="after">
        <page string="Pricing">

          <group string="Full Retail Price">
            <field name="full_retail_display" readonly="1" style="font-weight: bold; font-size: 16px;"/>
          </group>

          <group string="Price Lookup">
            <field name="pricing_lookup_pricelist_id" domain="[('id', 'in', available_pricelist_ids)]"/>
            <field name="pricing_lookup_quantity"/>
            <field name="discount_percent" readonly="1"/>
          </group>

          <group string="Final Price">
            <field name="final_price_display" readonly="1" style="font-weight: bold; font-size: 16px;"/>
          </group>

          <button name="action_reset_lookup_filters" string="Reset Filter" type="object" class="btn-secondary"/>

        </page>
      </xpath>
    </field>
  </record>

</odoo>
