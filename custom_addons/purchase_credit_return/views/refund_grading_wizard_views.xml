<odoo>
    <record id="view_refund_grading_wizard_form" model="ir.ui.view">
        <field name="name">refund.grading.wizard.form</field>
        <field name="model">refund.grading.wizard</field>
        <field name="arch" type="xml">
            <form string="Refund Grading">
                <sheet>
                    <group>
                        <field name="category_name" readonly="1"/>
                        <field name="final_score" readonly="1"/>
                        <field name="final_grade" readonly="1"/>
                    </group>

                    <notebook>
                        <page string="Grading Criteria">
                            <field name="answer_ids">
                                <list editable="bottom" create="false" delete="false">
                                    <field name="criteria_id" invisible="1"/>
                                    <field name="question" readonly="1"/>
                                    <field name="answer"/>
                                    <field name="weight" readonly="1"/>
                                </list>
                            </field>
                            <footer>
                                <button name="action_add_next_question"
                                        string="Next Question"
                                        type="object"
                                        class="btn-secondary"
                                        icon="fa-arrow-down"/>
                            </footer>
                        </page>
                    </notebook>

                    <footer>
                        <button name="action_save_and_grade"
                                type="object"
                                string="Save and Grade"
                                class="btn-secondary"
                                icon="fa-star"/>
                        <button name="action_save_and_close"
                                type="object"
                                string="Save and Close"
                                class="btn-primary"
                                icon="fa-check"/>
                        <button string="Cancel"
                                class="btn-secondary"
                                special="cancel"
                                icon="fa-times"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
