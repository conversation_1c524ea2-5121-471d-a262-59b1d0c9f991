<odoo>
    <!-- Main Form View -->
    <record id="view_purchase_credit_refund_form" model="ir.ui.view">
        <field name="name">purchase.credit.refund.form</field>
        <field name="model">purchase.credit.refund</field>
        <field name="arch" type="xml">
            <form string="Credit Refund">
                <sheet>
                    <div class="oe_title">
                        <label for="partner_id" class="oe_edit_only"/>
                        <h1><field name="partner_id"/></h1>
                    </div>

                    <group>
                        <group>
                            <field name="refund_date"/>
                            <field name="invoice_id"/>
                        </group>
                        <group>
                            <field name="currency_id"/>
                            <field name="user_id"/>
                        </group>
                    </group>

                    <field name="refund_status" widget="badge" class="mt16"
                           options="{'class': {
                               'draft': 'o_status_gray',
                               'submitted': 'o_status_info',
                               'graded': 'o_status_primary',
                               'approved': 'o_status_success',
                               'declined': 'o_status_danger',
                               'refunded': 'o_status_warning'
                           }}"/>

                    <notebook>
                        <page string="Refund Lines">
                            <field name="refund_line_ids">
                                <list editable="bottom">
                                    <field name="is_selected" widget="boolean_toggle"/>
                                    <field name="product_id"/>
                                    <field name="serial_numbers"/>
                                    <field name="quantity"/>
                                    <field name="price_unit"/>
                                    <field name="discount"/>
                                    <field name="tax_amount" readonly="1"/>
                                    <field name="subtotal" readonly="1"/>
                                    <field name="total" readonly="1"/>
                                    <field name="grading_score" readonly="1"/>
                                    <field name="final_grade" readonly="1"/>
                                    <field name="grading_result"/>
                                    <field name="is_override" groups="purchase_credit_return.group_refund_manager,purchase_credit_return.group_refund_admin"/>
                                    <field name="override_percentage" groups="purchase_credit_return.group_refund_manager,purchase_credit_return.group_refund_admin"/>
                                    <field name="refund_amount" readonly="1"/>
                                    <button name="action_start_grading" type="object" string="Grading" icon="fa-star"/>
                                </list>
                            </field>
                        </page>

                        <page string="Return Stock">
                            <group>
                                <field name="route_id" required="1"/>
                            </group>
                            <group>
                                <button name="action_prefill_return_lines"
                                        string="Prefill Return Lines"
                                        type="object"
                                        class="btn-secondary"/>
                                <button name="action_generate_return_picking"
                                        string="Create Stock Return"
                                        type="object"
                                        class="btn-primary"/>
                            </group>
                            <field name="return_stock_line_ids">
                                <list editable="bottom">
                                    <field name="product_id"/>
                                    <field name="serial_number"/>
                                    <field name="quantity"/>
                                    <field name="location_dest"/>
                                </list>
                            </field>
                        </page>

                        <page string="Totals">
                            <group>
                                <field name="refund_amount" readonly="1"/>
                                <field name="refund_tax_amount" readonly="1"/>
                                <field name="total_incl_vat" readonly="1"/>
                                <field name="refund_method"/>
                            </group>
                            <group>
                                <button name="action_create_accounting_refund"
                                        string="Create Accounting Refund"
                                        type="object"
                                        class="btn-primary"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Refund Line Modal Form -->
    <record id="view_purchase_credit_refund_line_form_inline" model="ir.ui.view">
        <field name="name">purchase.credit.refund.line.form.inline</field>
        <field name="model">purchase.credit.refund.line</field>
        <field name="arch" type="xml">
            <form string="Refund Line">
                <group>
                    <field name="is_selected"/>
                    <field name="product_id"/>
                    <field name="serial_numbers"/>
                    <field name="quantity"/>
                    <field name="price_unit"/>
                    <field name="discount"/>
                    <field name="tax_amount" readonly="1"/>
                    <field name="subtotal" readonly="1"/>
                    <field name="total" readonly="1"/>
                    <field name="grading_score" readonly="1"/>
                    <field name="final_grade" readonly="1"/>
                    <field name="grading_result"/>
                    <field name="refund_amount" readonly="1"/>
                </group>
                <group string="Override (Manager/Admin Only)"
                       groups="purchase_credit_return.group_refund_manager,purchase_credit_return.group_refund_admin">
                    <field name="is_override"/>
                    <field name="override_percentage" invisible="1"/>
                </group>
                <group string="Customer Return Info">
                    <field name="return_reason" readonly="1"/>
                    <field name="return_comment" readonly="1"/>
                    <field name="return_image_ids" widget="many2many_binary" readonly="1"/>
                </group>
                <footer>
                    <button name="action_start_grading" type="object" string="Grading" icon="fa-star"/>
                    <button name="open_return_info_wizard" type="object" string="Return Details" icon="fa-pencil"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Return Info Wizard Form -->
    <record id="view_return_info_wizard_form" model="ir.ui.view">
        <field name="name">refund.return.info.wizard.form</field>
        <field name="model">refund.return.info.wizard</field>
        <field name="arch" type="xml">
            <form string="Return Information">
                <group>
                    <field name="return_reason"/>
                    <field name="return_comment"/>
                    <field name="return_image_ids" widget="many2many_binary"/>
                    <field name="decision" required="1"/>
                </group>
                <footer>
                    <button name="action_submit_decision"
                            string="Notify Customer"
                            type="object"
                            icon="fa-envelope"
                            class="btn-secondary"/>
                    <button name="action_save_return_info"
                            string="Save"
                            type="object"
                            class="btn-primary"/>
                    <button string="Cancel" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- List View with Status Badges -->
    <record id="view_purchase_credit_refund_list" model="ir.ui.view">
        <field name="name">purchase.credit.refund.list</field>
        <field name="model">purchase.credit.refund</field>
        <field name="arch" type="xml">
            <list string="Credit Refunds">
                <field name="name"/>
                <field name="partner_id"/>
                <field name="invoice_id"/>
                <field name="refund_amount"/>
                <field name="currency_id"/>
                <field name="refund_status" widget="badge"
                       options="{'class': {
                           'draft': 'o_status_gray',
                           'submitted': 'o_status_info',
                           'graded': 'o_status_primary',
                           'approved': 'o_status_success',
                           'declined': 'o_status_danger',
                           'refunded': 'o_status_warning'
                       }}"/>
                <field name="user_id"/>
            </list>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_purchase_credit_refund_kanban" model="ir.ui.view">
        <field name="name">purchase.credit.refund.kanban</field>
        <field name="model">purchase.credit.refund</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_small_column" default_group_by="refund_status">
                <field name="partner_id"/>
                <field name="refund_status"/>
                <field name="refund_amount"/>
                <field name="currency_id"/>
                <field name="user_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card o_kanban_global_click">
                            <div class="o_kanban_details">
                                <strong><field name="name"/></strong><br/>
                                <span><field name="partner_id"/></span><br/>
                                <span><field name="refund_amount"/> <field name="currency_id"/></span><br/>
                                <span><field name="user_id"/></span>
                            </div>
                            <div class="oe_kanban_footer">
                                <span class="badge" t-att-class="{
                                    'o_status_gray': record.refund_status.raw_value === 'draft',
                                    'o_status_info': record.refund_status.raw_value === 'submitted',
                                    'o_status_primary': record.refund_status.raw_value === 'graded',
                                    'o_status_success': record.refund_status.raw_value === 'approved',
                                    'o_status_danger': record.refund_status.raw_value === 'declined',
                                    'o_status_warning': record.refund_status.raw_value === 'refunded'
                                }">
                                    <field name="refund_status"/>
                                </span>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
</odoo>
