<odoo>
    <!-- Scan Wizard Form View -->
    <record id="purchase_credit_return.view_credit_refund_scan_wizard_form" model="ir.ui.view">
        <field name="name">credit.refund.scan.wizard.form</field>
        <field name="model">purchase.credit.refund.scan.wizard</field>
        <field name="arch" type="xml">
            <form string="Scan Invoice or Serial">
                <sheet>
                    <group>
                        <field name="scan_code"
                               placeholder="Scan Invoice Number or Serial Number"
                               options="{'no_open': True}"
                               autofocus="autofocus"/>
                    </group>
                    <group>
                        <div class="o_form_label text-muted">Hint:</div>
                        <div class="text-muted">
                            Scan an invoice barcode or a serial number to auto-fill the credit return.
                        </div>
                    </group>
                    <footer>
                        <button name="action_scan"
                                type="object"
                                string="Scan and Create"
                                class="btn-primary"/>
                        <button name="action_manual"
                                type="object"
                                string="Manual Create"
                                class="btn-secondary"/>
                        <button string="Cancel" special="cancel" class="btn-link"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
