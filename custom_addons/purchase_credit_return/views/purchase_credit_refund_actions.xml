<odoo>
    <data>
        <!-- Credit Refund Main View -->
        <record id="action_purchase_credit_refunds" model="ir.actions.act_window">
            <field name="name">Credit Refunds</field>
            <field name="res_model">purchase.credit.refund</field>
            <field name="view_mode">kanban,list,form</field> <!-- updated to include kanban + correct 'list' for v18 -->
            <field name="context">{}</field>
        </record>

        <!-- Scan Wizard -->
        <record id="action_override_new_credit_refund" model="ir.actions.act_window">
            <field name="name">Scan Credit Refund</field>
            <field name="res_model">purchase.credit.refund.scan.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="purchase_credit_return.view_credit_refund_scan_wizard_form"/>
            <field name="target">new</field>
        </record>

        <!-- List View Button: New -->
        <record id="manual_new_button_credit_refund" model="ir.actions.act_window">
            <field name="name">New</field>
            <field name="res_model">purchase.credit.refund.scan.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="purchase_credit_return.view_credit_refund_scan_wizard_form"/>
            <field name="target">new</field>
            <field name="binding_model_id" ref="purchase_credit_return.model_purchase_credit_refund"/>
            <field name="binding_view_types">list</field> <!-- corrected to list for v18 -->
        </record>
    </data>
</odoo>
