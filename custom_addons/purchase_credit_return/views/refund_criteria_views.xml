<odoo>
    <!-- Form View -->
    <record id="view_refund_criteria_form" model="ir.ui.view">
        <field name="name">purchase.refund.criteria.form</field>
        <field name="model">purchase.refund.criteria</field>
        <field name="arch" type="xml">
            <form string="Refund Criterion">
                <sheet>
                    <group>
                        <field name="question"/>
                        <field name="weight"/>
                        <field name="sequence"/>
                        <field name="credit_item_id"/>
                        <field name="company_id" readonly="1"/>
                    </group>
                    <group string="Decision Tree Flow">
                        <field name="next_if_yes_id"/>
                        <field name="next_if_no_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List View -->
    <record id="view_refund_criteria_list" model="ir.ui.view">
        <field name="name">purchase.refund.criteria.list</field>
        <field name="model">purchase.refund.criteria</field>
        <field name="arch" type="xml">
            <list string="Refund Criteria">
                <field name="sequence"/>
                <field name="question"/>
                <field name="weight"/>
                <field name="credit_item_id"/>
                <field name="next_if_yes_id"/>
                <field name="next_if_no_id"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="action_refund_criteria" model="ir.actions.act_window">
        <field name="name">Refund Criteria</field>
        <field name="res_model">purchase.refund.criteria</field>
        <field name="view_mode">list,form</field>
        <field name="context">{}</field>
    </record>
</odoo>
