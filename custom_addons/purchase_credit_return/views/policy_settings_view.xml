<odoo>
    <!-- List (Odoo 18) View -->
    <record id="view_credit_policy_list" model="ir.ui.view">
        <field name="name">credit.policy.settings.list</field>
        <field name="model">credit.policy.settings</field>
        <field name="arch" type="xml">
            <list string="Sales and Refund Policies">
                <field name="id"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_credit_policy_form" model="ir.ui.view">
        <field name="name">credit.policy.settings.form</field>
        <field name="model">credit.policy.settings</field>
        <field name="arch" type="xml">
            <form string="Sales and Refund Policies">
                <sheet>
                    <group>
						<field name="is_active"/>
                        <field name="sales_policy" widget="html"/>
                        <field name="refund_policy" widget="html"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Window Action -->
    <record id="action_credit_policy_settings" model="ir.actions.act_window">
        <field name="name">Sales and Refund Policies</field>
        <field name="res_model">credit.policy.settings</field>
        <field name="view_mode">list,form</field> <!-- ✅ list first -->
        <field name="target">current</field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_credit_policy_settings"
              name="Sales and Refund Policies"
              parent="purchase_credit_return.menu_credit_setup"
              action="purchase_credit_return.action_credit_policy_settings"
              sequence="40"/>
</odoo>
