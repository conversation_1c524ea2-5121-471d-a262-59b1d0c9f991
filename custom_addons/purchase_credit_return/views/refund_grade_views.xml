<odoo>
    <!-- Inline Form View for Refund Criteria -->
    <record id="view_refund_criteria_form_inline" model="ir.ui.view">
        <field name="name">purchase.refund.criteria.form.inline</field>
        <field name="model">purchase.refund.criteria</field>
        <field name="arch" type="xml">
            <form string="Refund Criteria" create="true" edit="true" delete="true">
                <group>
                    <field name="question" required="1"/>
                    <field name="weight" string="Weight (%)" required="1"/>
                    <field name="next_if_yes_id"/>
                    <field name="next_if_no_id"/>
                </group>
            </form>
        </field>
    </record>

    <!-- Form View for Grading Line -->
    <record id="view_purchase_credit_refund_grading_form" model="ir.ui.view">
        <field name="name">purchase.credit.refund.grading.form</field>
        <field name="model">purchase.credit.refund.grading</field>
        <field name="arch" type="xml">
            <form string="Grading Line">
                <sheet>
                    <group>
                        <field name="refund_id"/>
                        <field name="product_id" required="1"/>
                        <field name="serial_number" required="1"/>
                    </group>
                    <group string="Grading Result">
                        <field name="final_score" readonly="1"/>
                        <field name="final_grade" readonly="1"/>
                    </group>
                    <footer>
                        <button name="start_grading"
                                string="Start Grading"
                                type="object"
                                class="btn-primary"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List View for Grading Lines -->
    <record id="view_purchase_credit_refund_grading_list" model="ir.ui.view">
        <field name="name">purchase.credit.refund.grading.list</field>
        <field name="model">purchase.credit.refund.grading</field>
        <field name="arch" type="xml">
            <list string="Grading Lines">
                <field name="refund_id"/>
                <field name="product_id"/>
                <field name="serial_number"/>
                <field name="final_score"/>
                <field name="final_grade"/>
            </list>
        </field>
    </record>
</odoo>
