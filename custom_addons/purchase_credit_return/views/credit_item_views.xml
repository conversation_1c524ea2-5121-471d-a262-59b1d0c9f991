<odoo>
    <!-- Credit Item Form View -->
    <record id="view_credit_item_form" model="ir.ui.view">
        <field name="name">purchase.credit.item.form</field>
        <field name="model">purchase.credit.item</field>
        <field name="arch" type="xml">
            <form string="Credit Item">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="product_categ_ids" widget="many2many_tags"/>
                    </group>
                    <notebook>
                        <page string="Refund Grades">
                            <group>
                                <field name="refund_grade_ids">
                                    <list editable="bottom">
                                        <field name="name"/>
                                        <field name="refund_percentage"/>
                                        <field name="notes"/>
                                    </list>
                                </field>
                            </group>
                        </page>

                        <page string="Refund Criteria Flow">
                            <group>
                                <field name="refund_criteria_ids">
                                    <list editable="bottom">
                                        <field name="question"/>
                                        <field name="weight"/>
                                        <field name="next_if_yes_id"/>
                                        <field name="next_if_no_id"/>
                                    </list>
                                    <form string="Refund Question">
                                        <sheet>
                                            <group>
                                                <field name="question"/>
                                                <field name="weight"/>
                                                <field name="sequence"/>
                                                <field name="credit_item_id"/>
                                                <field name="company_id" readonly="1"/>
                                            </group>
                                            <group string="Next Questions">
                                                <field name="next_if_yes_id"/>
                                                <field name="next_if_no_id"/>
                                            </group>
                                        </sheet>
                                    </form>
                                </field>
                            </group>
                            <label for="initial_question_id"/>
                            <field name="initial_question_id"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Credit Item List View -->
    <record id="view_credit_item_list" model="ir.ui.view">
        <field name="name">purchase.credit.item.list</field>
        <field name="model">purchase.credit.item</field>
        <field name="arch" type="xml">
            <list string="Credit Items">
                <field name="name"/>
                <field name="product_categ_ids" widget="many2many_tags"/>
            </list>
        </field>
    </record>

    <!-- Credit Item Action -->
    <record id="action_credit_items" model="ir.actions.act_window">
        <field name="name">Credit Items</field>
        <field name="res_model">purchase.credit.item</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">Create your first Credit Item</p>
        </field>
    </record>
</odoo>
