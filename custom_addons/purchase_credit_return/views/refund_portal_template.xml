<odoo>
    <template id="portal_refund_request_form" name="Refund Request Form">
        <t t-call="website.layout">
            <div class="container mt-4 mb-5">

                <!-- Top Section: Two-Column Layout -->
                <div class="row border rounded shadow-sm p-4 mb-5 bg-white">
                    <!-- LEFT COLUMN: Form -->
                    <div class="col-md-6">
                        <h2 class="mb-4">Return for Credit Request</h2>
                        <form method="POST" t-attf-action="/credit-refund-request">
                            <t t-if="error">
                                <div class="alert alert-danger mt-2"><t t-esc="error"/></div>
                            </t>
                            <div class="form-group mb-3">
                                <label for="invoice_number" class="form-label fw-bold">Invoice Number</label>
                                <div class="input-group">
                                    <input type="text" name="invoice_number" id="invoice_number" class="form-control" required="required"/>
                                    <button type="submit" class="btn btn-primary">Search</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- RIGHT COLUMN: Instructions -->
                    <div class="col-md-6">
                        <h5 class="fw-bold">How to Submit a Return for Credit</h5>
                        <ul>
                            <li>Start by entering your <strong>invoice number</strong> (found on your purchase invoice).</li>
                            <li>After locating your invoice, select the items you wish to return.</li>
                            <li>You’ll be asked to provide:
                                <ul>
                                    <li>The reason for return</li>
                                    <li>Optional comments</li>
                                    <li>Photos of the product (optional)</li>
                                </ul>
                            </li>
                            <li>Finally, select your preferred refund method (EFT, Credit, PayFast) and submit.</li>
                        </ul>
                        <p class="mt-3">We’ll review your request and notify you once inspection is complete.</p>
                    </div>
                </div>

                <!-- INVOICE RESULTS -->
                <t t-if="invoice">
                    <div class="border p-3 rounded bg-light mb-4">
                        <h5>Customer Information</h5>
                        <div class="row">
                            <div class="col-md-4"><strong>Customer:</strong> <t t-esc="invoice.partner_id.name"/></div>
                            <div class="col-md-4"><strong>Email:</strong> <t t-esc="invoice.partner_id.email or '-'"/></div>
                            <div class="col-md-4"><strong>Phone:</strong> <t t-esc="invoice.partner_id.phone or '-'"/></div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4"><strong>Invoice Number:</strong> <t t-esc="invoice.name"/></div>
                            <div class="col-md-4"><strong>Invoice Date:</strong> <t t-esc="invoice.invoice_date"/></div>
                            <div class="col-md-4"><strong>Request Date:</strong> <t t-esc="request_date"/></div>
                        </div>
                    </div>

                    <!-- REFUND FORM -->
                    <form method="POST" enctype="multipart/form-data" t-attf-action="/submit-refund-request">
                        <input type="hidden" name="invoice_id" t-att-value="invoice.id"/>

                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>✓</th>
                                    <th>Product</th>
                                    <th>Serial</th>
                                    <th>Qty</th>
                                    <th>Unit Price</th>
                                    <th>Discount (%)</th>
                                    <th>Subtotal Ex VAT</th>
                                    <th>Return Reason</th>
                                    <th>Comments</th>
                                    <th>Images</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="invoice_lines" t-as="line_info">
                                    <tr>
                                        <td><input type="checkbox" class="refund-checkbox" t-att-name="'line_%d' % line_info['line'].id"/></td>
                                        <td><t t-esc="line_info['line'].product_id.display_name"/></td>
                                        <td><t t-esc="line_info['serial_number'] or '-'"/></td>
                                        <td><t t-esc="line_info['line'].quantity"/></td>
                                        <td><t t-esc="line_info['line'].price_unit"/></td>
                                        <td><t t-esc="line_info['line'].discount"/></td>
                                        <td><t t-esc="line_info['line'].price_subtotal"/></td>
                                        <td>
                                            <select class="form-control refund-reason" t-att-name="'reason_%d' % line_info['line'].id">
                                                <option value="">Select Reason</option>
                                                <option value="reason_1">Reason 1</option>
                                                <option value="reason_2">Reason 2</option>
                                                <option value="reason_3">Reason 3</option>
                                                <option value="reason_4">Reason 4</option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control" placeholder="Optional comments" t-att-name="'comment_%d' % line_info['line'].id"/></td>
                                        <td><input type="file" class="form-control" multiple="multiple" accept="image/*" t-att-name="'images_%d' % line_info['line'].id"/></td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>

                        <div class="form-group mt-4">
                            <label for="refund_method">Preferred Refund Method</label>
                            <select name="refund_method" class="form-control" required="required">
                                <option value="">-- Select --</option>
                                <option value="eft">EFT</option>
                                <option value="payfast">PayFast</option>
                                <option value="credit">Credit to Account</option>
                            </select>
                        </div>

                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-success">Submit Refund Request</button>
                        </div>

                        <script type="text/javascript">
                            <![CDATA[
                            document.addEventListener('DOMContentLoaded', function () {
                                const form = document.querySelector('form');
                                const checkboxes = form.querySelectorAll('.refund-checkbox');

                                form.addEventListener('submit', function (e) {
                                    let hasError = false;
                                    checkboxes.forEach(cb => {
                                        const lineId = cb.name.split('_')[1];
                                        const reasonSelect = form.querySelector(`[name=reason_${lineId}]`);
                                        if (cb.checked) {
                                            if (reasonSelect && !reasonSelect.value) {
                                                reasonSelect.classList.add('is-invalid');
                                                hasError = true;
                                            } else {
                                                reasonSelect.classList.remove('is-invalid');
                                            }
                                        } else {
                                            if (reasonSelect) {
                                                reasonSelect.classList.remove('is-invalid');
                                            }
                                        }
                                    });

                                    if (hasError) {
                                        e.preventDefault();
                                        alert("Please select a return reason for each selected product.");
                                    }
                                });
                            });
                            ]]>
                        </script>
                    </form>
                </t>

                <!-- Policy Section -->
                <div class="row mt-5 mb-5">
                    <t t-if="sales_policy or refund_policy">
                        <div class="col-md-6">
                            <t t-if="sales_policy">
                                <h5>Sales Policy</h5>
                                <div t-out="sales_policy"/>
                            </t>
                        </div>
                        <div class="col-md-6">
                            <t t-if="refund_policy">
                                <h5>Refund Policy</h5>
                                <div t-out="refund_policy"/>
                            </t>
                        </div>
                    </t>
                </div>

            </div>
        </t>
    </template>
</odoo>
