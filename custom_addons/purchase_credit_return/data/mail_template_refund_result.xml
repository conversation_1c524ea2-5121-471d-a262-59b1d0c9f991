<odoo>
  <data noupdate="0">
    <record id="mail_template_refund_result_notification" model="mail.template">
      <field name="name">Refund Inspection Result Notification</field>
      <field name="model_id" ref="purchase_credit_return.model_purchase_credit_refund"/>
      <field name="subject">Refund {{ object.name }}: Inspection Completed</field>
      <field name="email_from">${object.user_id.email_formatted or user.email_formatted}</field>
      <field name="email_to">${object.partner_id.email}</field>
      <field name="body_html">
        <![CDATA[
        <p>Dear ${object.partner_id.name},</p>

        <p>Your refund request <strong>${object.name}</strong> has been inspected and processed.</p>

        <p><strong>Status:</strong> ${object.refund_status.capitalize()}<br/>
        <strong>Refund Amount:</strong> ${format_amount(object.total_incl_vat, object.currency_id)}<br/>
        <strong>Refund Method:</strong> ${dict(object._fields['refund_method'].selection).get(object.refund_method, 'N/A')}</p>

        <p><strong>Grading Summary:</strong></p>
        <ul>
        % for line in object.refund_line_ids.filtered(lambda l: l.is_selected):
            <li><b>${line.product_id.display_name} (${line.serial_numbers or 'No Serial'})</b>:<br/>
            Grade: ${line.final_grade or 'N/A'}<br/>
            Score: ${line.grading_score or 'N/A'}%<br/>
            Refund Amount: ${format_amount(line.refund_amount, object.currency_id)}</li>
        % endfor
        </ul>

        <p>All grading was done using our structured question-based system. If you'd like to review your specific answers, feel free to contact us.</p>

        <p>Thank you for your patience during the process.<br/>
        - The ${object.company_id.name} Team</p>
        ]]>
      </field>
      <field name="auto_delete" eval="False"/>
    </record>
  </data>
</odoo>
