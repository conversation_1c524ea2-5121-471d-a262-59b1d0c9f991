<odoo>
    <!-- Refund Reviewed / Approved Template -->
    <record id="email_template_notify_reviewed" model="mail.template">
        <field name="name">Refund Submission Approved</field>
        <field name="model_id" ref="purchase_credit_return.model_purchase_credit_refund"/>
        <field name="subject">Your Refund Submission Has Been Reviewed</field>
        <field name="email_from">${(user.email_formatted or '')}</field>
        <field name="email_to">${(object.partner_id.email or '')}</field>
        <field name="auto_delete" eval="False"/>
        <field name="body_html">
            <![CDATA[
            <p>Hello ${object.partner_id.name},</p>

            <p>We have reviewed your refund request for the product(s) returned under reference <strong>${object.name}</strong>.</p>

            <p>
                <strong>Status:</strong> Approved<br/>
                <strong>Refund Amount:</strong> ${format_amount(object.total_incl_vat, object.currency_id)}
            </p>

            <t t-if="ctx.get('comment')">
                <p><strong>Note from our team:</strong></p>
                <blockquote style="border-left: 3px solid #ddd; margin: 10px 0; padding-left: 10px;">
                    ${ctx.get('comment')}
                </blockquote>
            </t>

            <p>Thank you for your patience. Your refund will be processed shortly.</p>

            <p>Best regards,<br/>${user.company_id.name} Team</p>
            ]]>
        </field>
    </record>

    <!-- Refund Rejected Template -->
    <record id="email_template_notify_rejected" model="mail.template">
        <field name="name">Refund Submission Rejected</field>
        <field name="model_id" ref="purchase_credit_return.model_purchase_credit_refund"/>
        <field name="subject">Your Refund Submission Has Been Reviewed</field>
        <field name="email_from">${(user.email_formatted or '')}</field>
        <field name="email_to">${(object.partner_id.email or '')}</field>
        <field name="auto_delete" eval="False"/>
        <field name="body_html">
            <![CDATA[
            <p>Hello ${object.partner_id.name},</p>

            <p>We have reviewed your refund request for the product(s) returned under reference <strong>${object.name}</strong>.</p>

            <p>
                <strong>Status:</strong> Declined
            </p>

            <t t-if="ctx.get('comment')">
                <p><strong>Note from our team:</strong></p>
                <blockquote style="border-left: 3px solid #ddd; margin: 10px 0; padding-left: 10px;">
                    ${ctx.get('comment')}
                </blockquote>
            </t>

            <p>If you have any questions or would like to contest this outcome, please contact our support team.</p>

            <p>Kind regards,<br/>${user.company_id.name} Team</p>
            ]]>
        </field>
    </record>
</odoo>
