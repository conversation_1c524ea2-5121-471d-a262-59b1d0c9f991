<odoo>
    <!-- Existing rule kept -->
    <record id="model_purchase_credit_item_rule" model="ir.rule">
        <field name="name">Credit Items Access</field>
        <field name="model_id" ref="model_purchase_credit_item"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- Custom Refund Role: User -->
    <record id="group_refund_user" model="res.groups">
        <field name="name">Refund User</field>
        <field name="category_id" ref="base.module_category_purchases"/>
    </record>

    <!-- Custom Refund Role: Manager -->
    <record id="group_refund_manager" model="res.groups">
        <field name="name">Refund Manager</field>
        <field name="category_id" ref="base.module_category_purchases"/>
        <field name="implied_ids" eval="[(4, ref('purchase_credit_return.group_refund_user'))]"/>
    </record>

    <!-- Custom Refund Role: Administrator -->
    <record id="group_refund_admin" model="res.groups">
        <field name="name">Refund Administrator</field>
        <field name="category_id" ref="base.module_category_purchases"/>
        <field name="implied_ids" eval="[
            (4, ref('purchase_credit_return.group_refund_manager')),
            (4, ref('purchase_credit_return.group_refund_user'))
        ]"/>
    </record>
</odoo>
