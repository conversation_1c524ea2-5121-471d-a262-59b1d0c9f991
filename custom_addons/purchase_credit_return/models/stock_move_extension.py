from odoo import models, fields

class StockMove(models.Model):
    _inherit = 'stock.move'

    x_purchase_credit_refund_id = fields.Many2one(
        'purchase.credit.refund',
        string="Purchase Credit Refund",
        ondelete='set null'
    )
    source_location_id = fields.Many2one(
        'stock.location',
        string="Source Location",
        domain=[('usage', 'in', ['internal', 'transit'])]
    )
    destination_location_id = fields.Many2one(
        'stock.location',
        string="Destination Location",
        domain=[('usage', 'in', ['internal', 'transit'])]
    )
