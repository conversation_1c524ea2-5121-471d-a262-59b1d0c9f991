import logging
from odoo import models, fields, api
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class RefundGradingWizard(models.TransientModel):
    _name = 'refund.grading.wizard'
    _description = 'Refund Grading Wizard'

    refund_grading_id = fields.Many2one(
        'purchase.credit.refund.grading',
        string="Grading Line",
        required=True
    )

    credit_item_id = fields.Many2one(
        'purchase.credit.item',
        string="Credit Item",
        store=True,
        readonly=True
    )

    category_name = fields.Char(
        string="Product Category",
        compute='_compute_category_name'
    )

    answer_ids = fields.One2many(
        'refund.grading.wizard.criteria',
        'wizard_id',
        string="Answers"
    )

    final_score = fields.Float(string="Score (%)")
    final_grade = fields.Char(string="Final Grade")

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        grading_id = self.env.context.get('default_refund_grading_id')
        grading = self.env['purchase.credit.refund.grading'].browse(grading_id)

        if not grading or not grading.exists():
            raise UserError("Grading line is invalid.")

        product = grading.product_id
        if not product or not product.product_tmpl_id.categ_id:
            raise UserError("Product has no category assigned.")

        product_category = product.product_tmpl_id.categ_id

        credit_item = self.env['purchase.credit.item'].search([
            ('product_categ_ids', 'in', [product_category.id])
        ], limit=1)

        if not credit_item:
            raise UserError("No grading configuration found for this product's category.")

        res['refund_grading_id'] = grading.id
        res['credit_item_id'] = credit_item.id

        if grading.answer_ids:
            res['answer_ids'] = [
                (0, 0, {
                    'criteria_id': ans.criteria_id.id,
                    'answer': ans.answer,
                }) for ans in grading.answer_ids
            ]
            res['final_score'] = grading.final_score
            res['final_grade'] = grading.final_grade
        else:
            first = credit_item.initial_question_id
            if first:
                res['answer_ids'] = [(0, 0, {
                    'criteria_id': first.id
                })]

        return res

    @api.depends('credit_item_id')
    def _compute_category_name(self):
        for wizard in self:
            if wizard.credit_item_id and wizard.credit_item_id.product_categ_ids:
                wizard.category_name = ', '.join(wizard.credit_item_id.product_categ_ids.mapped('name'))
            else:
                wizard.category_name = ''

    def _traverse_tree_and_score(self):
        score = 0.0
        traversed = set()
        max_score = 100.0

        def traverse(criteria, answers):
            nonlocal score
            if not criteria or criteria.id in traversed:
                return

            traversed.add(criteria.id)
            answer = next((a.answer for a in answers if a.criteria_id == criteria), 'no')

            if answer == 'yes':
                if criteria.weight == 100.0:
                    score = 100.0
                    return
                score += criteria.weight
                traverse(criteria.next_if_yes_id, answers)
            else:
                traverse(criteria.next_if_no_id, answers)

        if self.answer_ids:
            start = self.answer_ids[0].criteria_id
            traverse(start, self.answer_ids)

        return min(score, max_score)

    def _compute_score_and_grade(self):
        percentage = self._traverse_tree_and_score()

        refund_grades = self.credit_item_id.refund_grade_ids.sorted(
            key=lambda g: g.refund_percentage,
            reverse=True
        )

        grade = refund_grades[-1].name if refund_grades else ''
        for g in refund_grades:
            if percentage >= g.refund_percentage:
                grade = g.name
                break

        return percentage, grade

    def _save_answers_to_grading(self, percentage, grade):
        self.final_score = percentage
        self.final_grade = grade
        self.refund_grading_id.final_score = percentage
        self.refund_grading_id.final_grade = grade

        self.refund_grading_id.answer_ids.unlink()
        for ans in self.answer_ids:
            self.env['purchase.credit.refund.grading.answer'].create({
                'grading_id': self.refund_grading_id.id,
                'criteria_id': ans.criteria_id.id,
                'answer': ans.answer,
            })

    def action_save_and_grade(self):
        self.ensure_one()
        percentage, grade = self._compute_score_and_grade()
        self._save_answers_to_grading(percentage, grade)

        refund = self.refund_grading_id.refund_id
        if refund:
            refund.write({'refund_status': 'graded'})

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'refund.grading.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': dict(self.env.context),
        }

    def action_save_and_close(self):
        self.ensure_one()
        percentage, grade = self._compute_score_and_grade()
        self._save_answers_to_grading(percentage, grade)
        return {'type': 'ir.actions.act_window_close'}

    def action_add_next_question(self):
        self.ensure_one()
        for answer in self.answer_ids:
            if not answer.answer:
                continue
            next_crit = answer.criteria_id.next_if_yes_id if answer.answer == 'yes' else answer.criteria_id.next_if_no_id
            if next_crit and next_crit not in self.answer_ids.mapped('criteria_id'):
                self.answer_ids += self.env['refund.grading.wizard.criteria'].new({
                    'wizard_id': self.id,
                    'criteria_id': next_crit.id
                })
                break

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'refund.grading.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': dict(self.env.context),
        }
