from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class PurchaseCreditRefundLine(models.Model):
    _name = 'purchase.credit.refund.line'
    _description = 'Credit Refund Line'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    refund_id = fields.Many2one('purchase.credit.refund', ondelete='cascade', tracking=True)
    product_id = fields.Many2one('product.product', required=True, tracking=True)
    serial_numbers = fields.Char(tracking=True)
    quantity = fields.Float(tracking=True)
    price_unit = fields.Float(tracking=True)
    discount = fields.Float(tracking=True)
    is_selected = fields.Boolean(default=False, tracking=True)
    currency_id = fields.Many2one('res.currency', related='refund_id.currency_id', store=True)

    is_override = fields.Boolean(string="Manager Override", default=False)
    override_percentage = fields.Float(string="Override %", help="Set custom refund percentage (up to 100%)")

    is_override_visible = fields.Boolean(
        compute="_compute_is_override_visible",
        string="Show Override %"
    )

    refund = fields.Boolean(string="Refund?", default=False, tracking=True)

    subtotal = fields.Monetary(compute="_compute_totals", store=True, currency_field="currency_id")
    tax_amount = fields.Monetary(compute="_compute_totals", store=True, currency_field="currency_id")
    total = fields.Monetary(compute="_compute_totals", store=True, currency_field="currency_id")

    refund_amount = fields.Monetary(
        string="Refund Amount",
        compute="_compute_refund_amount",
        store=True,
        currency_field="currency_id"
    )

    grading_id = fields.Many2one(
        'purchase.credit.refund.grading',
        compute="_compute_grading_link",
        store=True
    )

    grading_score = fields.Float(
        string="Score (%)",
        compute="_compute_grading_fields",
        store=True
    )

    final_grade = fields.Char(
        string="Final Grade",
        compute="_compute_grading_fields",
        store=True
    )

    grading_result = fields.Selection([
        ('approve', 'Approve'),
        ('decline', 'Decline')
    ], string="Grading Result", tracking=True)

    return_reason = fields.Selection([
        ('reason_1', 'Reason 1'),
        ('reason_2', 'Reason 2'),
        ('reason_3', 'Reason 3'),
        ('reason_4', 'Reason 4')
    ], string="Return Reason", tracking=True)

    return_comment = fields.Text(string="Customer Comment", tracking=True)

    return_image_ids = fields.Many2many(
        'ir.attachment',
        'refund_line_return_image_rel',
        'refund_line_id',
        'attachment_id',
        string='Return Images'
    )

    @api.depends('is_override')
    def _compute_is_override_visible(self):
        for line in self:
            line.is_override_visible = bool(line.is_override)

    @api.constrains('override_percentage')
    def _check_override_permission_and_range(self):
        for line in self:
            if line.is_override:
                if not self.env.user.has_group('purchase_credit_return.group_refund_manager'):
                    raise UserError(_("Only managers can perform override."))
                if not (0.0 <= line.override_percentage <= 100.0):
                    raise UserError(_("Override percentage must be between 0 and 100."))

    def open_return_info_wizard(self):
        self.ensure_one()
        return {
            'name': 'Return Information',
            'type': 'ir.actions.act_window',
            'res_model': 'refund.return.info.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_refund_line_id': self.id,
                'default_return_reason': self.return_reason,
                'default_return_comment': self.return_comment,
                'default_return_image_ids': [(6, 0, self.return_image_ids.ids)],
            }
        }

    @api.depends('price_unit', 'quantity', 'discount', 'refund_id.company_id')
    def _compute_totals(self):
        for line in self:
            base = line.price_unit * line.quantity * (1 - line.discount / 100.0)
            tax_obj = line.env['account.tax'].search([
                ('type_tax_use', '=', 'sale'),
                ('company_id', '=', line.refund_id.company_id.id),
                ('amount_type', '=', 'percent'),
                ('amount', '>', 0)
            ], limit=1)
            tax_rate = (tax_obj.amount / 100.0) if tax_obj else 0.0
            line.subtotal = round(base, 2)
            line.tax_amount = round(base * tax_rate, 2)
            line.total = round(base + line.tax_amount, 2)

    @api.depends('is_override', 'override_percentage', 'final_grade', 'subtotal', 'grading_id.credit_item_id')
    def _compute_refund_amount(self):
        for line in self:
            refund_pct = 0.0
            if line.is_override:
                refund_pct = line.override_percentage or 0.0
            elif line.final_grade and line.grading_id and line.grading_id.credit_item_id:
                # credit_item_id is already resolved using child_of in the grading model
                refund_grade = self.env['purchase.refund.grade'].search([
                    ('name', '=', line.final_grade),
                    ('credit_item_id', '=', line.grading_id.credit_item_id.id)
                ], limit=1)
                refund_pct = refund_grade.refund_percentage if refund_grade else 0.0

            line.refund_amount = round(line.subtotal * (refund_pct / 100.0), 2) if refund_pct else 0.0

    @api.depends('refund_id.refund_line_ids')
    def _compute_grading_link(self):
        for line in self:
            line.grading_id = False
            if line.refund_id and line.product_id and line.serial_numbers:
                grading = self.env['purchase.credit.refund.grading'].search([
                    ('refund_id', '=', line.refund_id.id),
                    ('product_id', '=', line.product_id.id),
                    ('serial_number', '=', line.serial_numbers)
                ], limit=1)
                line.grading_id = grading

    @api.depends('grading_id.final_score', 'grading_id.final_grade')
    def _compute_grading_fields(self):
        for line in self:
            grading = line.grading_id
            line.grading_score = grading.final_score or 0.0 if grading else 0.0
            line.final_grade = grading.final_grade or '' if grading else ''

    def action_start_grading(self):
        self.ensure_one()
        grading_model = self.env['purchase.credit.refund.grading']
        grading = self.grading_id
        if grading and grading.exists():
            return grading.start_grading()
        grading = grading_model.create({
            'refund_id': self.refund_id.id,
            'product_id': self.product_id.id,
            'serial_number': self.serial_numbers,
            'line_ref_id': self.id,
        })
        self.grading_id = grading
        return grading.start_grading()

    def write(self, vals):
        result = super().write(vals)

        if 'grading_result' in vals:
            for line in self:
                if line.refund_id:
                    line.refund_id._compute_totals()
                    line.refund_id.invalidate_recordset(['refund_amount', 'refund_tax_amount', 'total_incl_vat'])

                    statuses = line.refund_id.refund_line_ids.mapped('grading_result')
                    if statuses and all(status == 'approve' for status in statuses if status):
                        line.refund_id.write({'refund_status': 'approved'})
                    elif statuses and all(status == 'decline' for status in statuses if status):
                        line.refund_id.write({'refund_status': 'declined'})
                    elif any(status in ('approve', 'decline') for status in statuses if status):
                        line.refund_id.write({'refund_status': 'graded'})
                    else:
                        line.refund_id.write({'refund_status': 'submitted'})

        if any(key in vals for key in ('final_grade', 'grading_result', 'is_override', 'override_percentage')):
            for line in self:
                line._compute_refund_amount()
                if line.refund_id:
                    line.refund_id._compute_totals()
                    line.refund_id.invalidate_recordset(['refund_amount', 'refund_tax_amount', 'total_incl_vat'])

        return result
