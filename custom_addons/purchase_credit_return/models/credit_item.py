from odoo import models, fields


class CreditItem(models.Model):
    _name = 'purchase.credit.item'
    _description = 'Credit Item for Purchase Refunds'

    name = fields.Char(required=True)

    product_categ_ids = fields.Many2many(
        'product.category',
        'credit_item_product_categ_rel',
        'credit_item_id',
        'categ_id',
        string='Applicable Categories',
        help='Select all product categories that this credit item applies to.'
    )

    refund_grade_ids = fields.One2many(
        'purchase.refund.grade',
        'credit_item_id',
        string='Refund Grades'
    )

    refund_criteria_ids = fields.One2many(
        'purchase.refund.criteria',
        'credit_item_id',
        string='Refund Criteria'
    )

    initial_question_id = fields.Many2one(
        'purchase.refund.criteria',
        string='Initial Question',
        domain="[('credit_item_id', '=', id)]",
        help='Starting question for the grading decision tree.'
    )
