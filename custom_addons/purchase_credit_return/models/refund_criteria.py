from odoo import models, fields


class RefundCriteria(models.Model):
    _name = 'purchase.refund.criteria'
    _description = 'Refund Criteria (Decision Flow)'
    _rec_name = 'question'
    _order = 'sequence, id'

    question = fields.Char(
        string='Criteria Question',
        required=True,
        help="This is the actual yes/no question asked during refund grading."
    )

    weight = fields.Float(
        string='Weight (%)',
        default=0.0,
        help="The score this question contributes to the total grading if it is reached in the flow. "
             "If this question is answered YES and no further questions follow, this weight applies directly."
    )

    sequence = fields.Integer(
        string="Sequence",
        default=10,
        help="Used only for organizing questions in the backend list view. Not used in decision flow."
    )

    credit_item_id = fields.Many2one(
        'purchase.credit.item',
        string='Credit Item',
        required=True,
        ondelete='cascade',
        index=True,
        help="The Credit Item this criterion belongs to. Each item has its own decision tree."
    )

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        readonly=True,
        store=True
    )

    next_if_yes_id = fields.Many2one(
        'purchase.refund.criteria',
        string="Next Question if YES",
        help="The next question to ask if this one is answered YES. Leave empty to end flow."
    )

    next_if_no_id = fields.Many2one(
        'purchase.refund.criteria',
        string="Next Question if NO",
        help="The next question to ask if this one is answered NO. Leave empty to end flow."
    )
