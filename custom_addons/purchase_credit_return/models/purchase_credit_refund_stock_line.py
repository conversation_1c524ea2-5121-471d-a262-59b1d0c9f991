from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import html_escape
import logging

_logger = logging.getLogger(__name__)


class PurchaseCreditRefundStockLine(models.Model):
    _name = 'purchase.credit.refund.stock.line'
    _description = 'Refund Return Stock Line'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    refund_id = fields.Many2one('purchase.credit.refund', ondelete='cascade', tracking=True)
    product_id = fields.Many2one('product.product', required=True, tracking=True)
    serial_number = fields.Char(tracking=True)
    quantity = fields.Float(default=1.0, tracking=True)
    location_dest = fields.Many2one('stock.location', string="Destination", required=True, tracking=True)

    name = fields.Char(string="Description", compute='_compute_name', store=True)

    @api.depends('product_id', 'serial_number')
    def _compute_name(self):
        for line in self:
            line.name = f"{line.product_id.display_name or ''} - {line.serial_number or ''}"
