from odoo import models, fields


class RefundGradingAnswer(models.Model):
    _name = 'purchase.credit.refund.grading.answer'
    _description = 'Refund Grading Answer'
    _order = 'criteria_id'

    grading_id = fields.Many2one(
        'purchase.credit.refund.grading',
        string='Grading Line',
        required=True,
        ondelete='cascade',
    )

    criteria_id = fields.Many2one(
        'purchase.refund.criteria',
        string='Criteria',
        required=True,
    )

    question = fields.Char(
        string='Question',
        related='criteria_id.question',
        readonly=True
    )

    weight = fields.Float(
        string='Weight (%)',
        related='criteria_id.weight',
        readonly=True
    )

    answer = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string='Answer',
        required=True,
        default='no'
    )
