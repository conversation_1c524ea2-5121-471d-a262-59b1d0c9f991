from odoo import models, fields

class RefundGradingWizardCriteria(models.TransientModel):
    _name = 'refund.grading.wizard.criteria'
    _description = 'Grading Wizard Criteria Answer'

    wizard_id = fields.Many2one(
        'refund.grading.wizard',
        string="Wizard",
        required=True,
        ondelete='cascade'
    )

    criteria_id = fields.Many2one(
        'purchase.refund.criteria',
        string="Criteria",
        required=True
    )

    question = fields.Char(
        string="Question",
        related='criteria_id.question',
        readonly=True
    )

    weight = fields.Float(
        string="Weight (%)",
        related='criteria_id.weight',
        readonly=True
    )

    answer = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string="Answer",
        required=False
    )
