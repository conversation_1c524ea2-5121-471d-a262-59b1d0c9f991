from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import html_escape
import logging

_logger = logging.getLogger(__name__)

class PurchaseCreditRefund(models.Model):
    _name = 'purchase.credit.refund'
    _description = 'Purchase Credit Refund'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string="Reference", required=True, copy=False, readonly=True, default=lambda self: _('New'))
    refund_date = fields.Date(string="Date", default=fields.Date.today, tracking=True)
    partner_id = fields.Many2one('res.partner', string="Customer/Contact", required=True, tracking=True)
    company_id = fields.Many2one('res.company', default=lambda self: self.env.company, readonly=True)
    currency_id = fields.Many2one('res.currency', default=lambda self: self.env.company.currency_id, readonly=True)
    invoice_id = fields.Many2one('account.move', domain="[('move_type', '=', 'out_invoice'), ('state', '=', 'posted')]", string="Original Invoice")
    serial_number = fields.Char(string="Serial Number")

    refund_method = fields.Selection([
        ('payfast', 'PayFast'),
        ('eft', 'EFT'),
        ('credit', 'Credit to account')
    ], string="Refund Method")

    refund_status = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('graded', 'Graded'),
        ('approved', 'Approved'),
        ('declined', 'Declined'),
        ('refunded', 'Refunded'),
    ], string="Status", compute="_compute_refund_status", store=True, tracking=True)

    refund_amount = fields.Monetary(string="Refund Amount", compute="_compute_totals", currency_field="currency_id", store=True)
    refund_tax_amount = fields.Monetary(string="VAT", compute="_compute_totals", currency_field="currency_id", store=True)
    total_incl_vat = fields.Monetary(string="Total Incl. VAT", compute="_compute_totals", currency_field="currency_id", store=True)

    user_id = fields.Many2one('res.users', default=lambda self: self.env.user)
    refund_line_ids = fields.One2many('purchase.credit.refund.line', 'refund_id')
    refund_line_info_ids = fields.One2many('purchase.credit.refund.line', 'refund_id', string="Return Info Lines", readonly=True)
    refund_stock_move_ids = fields.One2many('stock.move', 'x_purchase_credit_refund_id')
    return_stock_line_ids = fields.One2many('purchase.credit.refund.stock.line', 'refund_id')

    route_id = fields.Many2one(
        'stock.route',
        string="Return Route",
        domain="[('name', 'ilike', 'Return to')]",
        default=lambda self: self._default_return_route()
    )

    def _default_return_route(self):
        return self.env['stock.route'].search([
            ('name', 'ilike', 'RETURN TO STOCK'),
            ('company_id', '=', self.env.company.id)
        ], limit=1)

    @api.depends('refund_line_ids.grading_result', 'refund_line_ids.is_selected')
    def _compute_refund_status(self):
        for rec in self:
            selected_lines = rec.refund_line_ids.filtered(lambda l: l.is_selected)
            results = list(filter(None, selected_lines.mapped('grading_result')))

            if not selected_lines:
                rec.refund_status = 'draft'
            elif results and all(r == 'approve' for r in results):
                rec.refund_status = 'approved'
            elif results and all(r == 'decline' for r in results):
                rec.refund_status = 'declined'
            elif results and any(r in ('approve', 'decline') for r in results):
                rec.refund_status = 'graded'
            else:
                rec.refund_status = 'submitted'

    def write(self, vals):
        for rec in self:
            old_status = rec.refund_status

        res = super().write(vals)

        for rec in self:
            if rec.refund_status != old_status:
                rec.message_post(body=_("Refund status changed to <b>%s</b>." % rec.refund_status.capitalize()))
                if rec.refund_status in ['approved', 'declined']:
                    rec._send_result_email()

        return res

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('purchase.credit.refund') or _('New')
        return super().create(vals)

    def action_prefill_return_lines(self):
        for rec in self:
            rec.return_stock_line_ids = [(5, 0, 0)]
            for line in rec.refund_line_ids.filtered(lambda l: l.is_selected and l.grading_result == 'approve'):
                original_move = self.env['stock.move.line'].search([
                    ('lot_id.name', '=', line.serial_numbers),
                    ('product_id', '=', line.product_id.id),
                    ('company_id', '=', rec.company_id.id),
                ], limit=1)

                if not original_move:
                    raise UserError(f"No original stock move found for {line.product_id.display_name} ({line.serial_numbers})")

                location_dest = original_move.location_id.id
                if rec.route_id and 'QC' in rec.route_id.name.upper():
                    qc_location = self.env['stock.location'].search([
                        ('name', 'ilike', 'QC LOCATION'),
                        ('usage', '=', 'internal'),
                        ('company_id', '=', rec.company_id.id)
                    ], limit=1)
                    if not qc_location:
                        raise UserError("QC LOCATION not found.")
                    location_dest = qc_location.id

                self.env['purchase.credit.refund.stock.line'].create({
                    'refund_id': rec.id,
                    'product_id': line.product_id.id,
                    'serial_number': line.serial_numbers,
                    'quantity': line.quantity,
                    'location_dest': location_dest,
                })

    def action_generate_return_picking(self):
        for rec in self:
            if not rec.route_id:
                raise UserError("Please select a return route before creating stock return.")

            push_rule = rec.route_id.rule_ids.filtered(
                lambda r: r.company_id == rec.company_id and r.action == 'push' and r.location_src_id and r.location_dest_id
            )
            if not push_rule:
                raise UserError("No valid push rule with both source and destination locations found for the selected route.")

            push_rule = push_rule[0]

            picking_type = self.env['stock.picking.type'].search([
                ('default_location_src_id', '=', push_rule.location_src_id.id),
                ('company_id', '=', rec.company_id.id),
            ], limit=1)
            if not picking_type:
                raise UserError("No picking type found using source location and company from the route.")

            picking = self.env['stock.picking'].create({
                'partner_id': rec.partner_id.id,
                'picking_type_id': picking_type.id,
                'location_id': push_rule.location_src_id.id,
                'location_dest_id': rec.return_stock_line_ids[:1].location_dest.id if rec.return_stock_line_ids else push_rule.location_dest_id.id,
                'origin': f"Credit Refund {rec.name or rec.id}",
                'company_id': rec.company_id.id,
            })

            for line in rec.return_stock_line_ids:
                move = self.env['stock.move'].create({
                    'name': line.product_id.display_name,
                    'product_id': line.product_id.id,
                    'product_uom_qty': line.quantity,
                    'product_uom': line.product_id.uom_id.id,
                    'picking_id': picking.id,
                    'location_id': push_rule.location_src_id.id,
                    'location_dest_id': line.location_dest.id,
                    'company_id': rec.company_id.id,
                    'x_purchase_credit_refund_id': rec.id,
                })

                if line.product_id.tracking == 'serial' and line.serial_number:
                    lot = self.env['stock.lot'].search([
                        ('name', '=', line.serial_number),
                        ('product_id', '=', line.product_id.id),
                        ('company_id', '=', rec.company_id.id),
                    ], limit=1)
                    if not lot:
                        raise UserError(f"Lot '{line.serial_number}' not found for product {line.product_id.display_name}.")

                    self.env['stock.move.line'].create({
                        'move_id': move.id,
                        'product_id': line.product_id.id,
                        'product_uom_id': line.product_id.uom_id.id,
                        'qty_done': 1.0,
                        'lot_id': lot.id,
                        'location_id': push_rule.location_src_id.id,
                        'location_dest_id': line.location_dest.id,
                    })

            rec.refund_stock_move_ids = [(4, m.id) for m in picking.move_ids_without_package]
            rec.message_post(body=f"Return picking created: <a href='/web#id={picking.id}&model=stock.picking&view_type=form'>{picking.name}</a>")

            return {
                'type': 'ir.actions.act_window',
                'name': 'Return Picking',
                'res_model': 'stock.picking',
                'res_id': picking.id,
                'view_mode': 'form',
                'target': 'current',
            }

    @api.depends('refund_line_ids.refund_amount', 'refund_line_ids.is_selected', 'refund_line_ids.grading_result')
    def _compute_totals(self):
        for rec in self:
            lines = rec.refund_line_ids.filtered(lambda l: l.is_selected and l.grading_result == 'approve')
            refund_amount = sum(lines.mapped('refund_amount'))

            tax_obj = rec.env['account.tax'].search([
                ('type_tax_use', '=', 'sale'),
                ('company_id', '=', rec.company_id.id),
                ('amount_type', '=', 'percent'),
                ('amount', '>', 0)
            ], limit=1)

            vat_rate = (tax_obj.amount / 100.0) if tax_obj else 0.0
            refund_tax = round(refund_amount * vat_rate, 2)
            total_refund = round(refund_amount + refund_tax, 2)

            rec.refund_amount = refund_amount
            rec.refund_tax_amount = refund_tax
            rec.total_incl_vat = total_refund

    def action_create_accounting_refund(self):
        for rec in self:
            if not rec.invoice_id or not rec.refund_amount:
                raise UserError("Refund must be graded and approved before posting.")
            if not rec.refund_method:
                raise UserError("Please select a refund method before proceeding.")

            income_account = self.env['account.account'].with_company(rec.company_id).search([
                ('account_type', '=', 'income')
            ], limit=1)
            if not income_account:
                raise UserError("No income account found for this company. Please configure an income account.")

            refund_lines = rec.refund_line_ids.filtered(lambda l: l.is_selected and l.grading_result == 'approve')
            if not refund_lines:
                raise UserError("No refund lines marked for refund.")

            invoice_line_vals = []
            for line in refund_lines:
                invoice_line_vals.append((0, 0, {
                    'name': f"Refund: {line.product_id.display_name or 'Product'} ({line.serial_numbers or 'No serial'})",
                    'quantity': line.quantity or 1,
                    'price_unit': line.refund_amount,
                    'account_id': income_account.id,
                    'product_id': line.product_id.id,
                }))

            refund_move = self.env['account.move'].create({
                'move_type': 'out_refund',
                'partner_id': rec.partner_id.id,
                'invoice_origin': rec.invoice_id.name,
                'invoice_date': fields.Date.today(),
                'currency_id': rec.currency_id.id,
                'company_id': rec.company_id.id,
                'x_refund_method': rec.refund_method,
                'invoice_line_ids': invoice_line_vals
            })

            # Prepare link and clean display name
            action = self.env.ref('account.action_move_out_refund_type')
            url = f"/web#id={refund_move.id}&model=account.move&view_type=form&action={action.id}"
            safe_name = html_escape(refund_move.name or 'Credit Note')

            message = f"""
                <p><b>Draft Credit Note Created</b></p>
                <p>Reference: <a href="{html_escape(url)}" target="_blank">{safe_name}</a></p>
                <a type="button" class="btn btn-primary" href="{html_escape(url)}" target="_blank">View Credit Note</a>
            """

            rec.message_post(
                body=message,
                subject=_("Credit Note Created: %s" % refund_move.name),
                subtype_xmlid="mail.mt_note"
            )

            return {
                'type': 'ir.actions.act_window',
                'name': 'Credit Note Created',
                'res_model': 'account.move',
                'res_id': refund_move.id,
                'view_mode': 'form',
                'target': 'new',
                'context': {'form_view_initial_mode': 'edit'}
            }

    @api.onchange('invoice_id')
    def _onchange_invoice_id(self):
        if self.invoice_id:
            invoice = self.invoice_id.with_company(self.company_id)
            self.partner_id = invoice.partner_id
            self._populate_from_invoice(invoice)

    def _populate_from_invoice(self, invoice):
        _logger.info("[RefundGen] Starting refund line population for invoice: %s", invoice.name)
        self.refund_line_ids = [(5, 0, 0)]

        tax_obj = self.env['account.tax'].search([
            ('type_tax_use', '=', 'sale'),
            ('company_id', '=', self.company_id.id),
            ('amount_type', '=', 'percent'),
            ('amount', '>', 0)
        ], limit=1)
        tax_rate = (tax_obj.amount / 100.0) if tax_obj else 0.0
        _logger.info("[RefundGen] Using tax rate: %s%%", round(tax_rate * 100, 2))

        serials_by_product = {}
        move_lines = self.env['stock.move.line'].search([
            ('move_id.sale_line_id.invoice_lines.move_id', '=', invoice.id),
            ('company_id', '=', self.company_id.id),
            ('lot_id', '!=', False),
        ])
        for ml in move_lines:
            if ml.product_id and ml.lot_id:
                serials_by_product.setdefault(ml.product_id.id, set()).add(ml.lot_id.name)

        lines_to_create = []
        for line in invoice.invoice_line_ids:
            if not line.product_id or line.display_type in ('line_note', 'line_section'):
                continue

            serial_list = sorted(serials_by_product.get(line.product_id.id, []))

            if serial_list and len(serial_list) == int(line.quantity):
                for serial in serial_list:
                    lines_to_create.append((0, 0, {
                        'product_id': line.product_id.id,
                        'serial_numbers': serial,
                        'quantity': 1,
                        'price_unit': line.price_unit,
                        'discount': line.discount,
                        'is_selected': False,
                    }))
            else:
                serials = ', '.join(serial_list)
                lines_to_create.append((0, 0, {
                    'product_id': line.product_id.id,
                    'serial_numbers': serials,
                    'quantity': line.quantity,
                    'price_unit': line.price_unit,
                    'discount': line.discount,
                    'is_selected': False,
                }))

        self.refund_line_ids = lines_to_create
        _logger.info("[RefundGen] Completed refund line generation. Total lines: %s", len(lines_to_create))

    def _send_result_email(self):
        template = self.env.ref('purchase_credit_return.mail_template_refund_result_notification', raise_if_not_found=False)
        for rec in self:
            if not rec.partner_id.email:
                _logger.warning("Refund %s has no email on partner %s", rec.name, rec.partner_id.name)
                continue
            if rec.refund_status not in ['approved', 'declined']:
                _logger.info("Refund %s not in final state; skipping email", rec.name)
                continue
            if not template:
                _logger.warning("Refund template not found; skipping email for %s", rec.name)
                continue

            # Send the actual email
            template.with_context(lang=rec.partner_id.lang).send_mail(rec.id, force_send=True)

            # Render the HTML version of the email to show in the chatter
            rendered_email = template._render_field('body_html', [rec.id])[rec.id]
            rec.message_post(
                body=rendered_email,
                subject=_("Customer Notification: Refund %s" % rec.name),
                subtype_xmlid="mail.mt_note",
            )
