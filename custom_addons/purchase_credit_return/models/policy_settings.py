from odoo import models, fields, api, _

class CreditPolicySettings(models.Model):
    _name = 'credit.policy.settings'
    _description = 'Sales & Refund Policy'
    _rec_name = 'id'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    sales_policy = fields.Html(string="Sales Policy", sanitize=False)
    refund_policy = fields.Html(string="Refund Policy", sanitize=False)
    is_active = fields.Boolean(string="Use This Policy", default=False)

    @api.model
    def create(self, vals):
        if vals.get('is_active'):
            self.search([('is_active', '=', True)]).write({'is_active': False})
        return super().create(vals)

    def write(self, vals):
        if vals.get('is_active'):
            self.search([('is_active', '=', True)]).write({'is_active': False})
        return super().write(vals)
