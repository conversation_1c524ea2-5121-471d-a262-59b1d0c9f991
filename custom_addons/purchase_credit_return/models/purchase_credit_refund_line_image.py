from odoo import models, fields, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class PurchaseCreditRefundLineImage(models.Model):
    _name = 'purchase.credit.refund.line.image'
    _description = 'Refund Line Images'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    refund_line_id = fields.Many2one(
        'purchase.credit.refund.line',
        required=True,
        ondelete='cascade',
        string="Refund Line",
        tracking=True
    )

    image = fields.Binary(
        string="Image",
        required=True,
        tracking=True
    )

    image_name = fields.Char(
        string="Filename",
        tracking=True
    )
