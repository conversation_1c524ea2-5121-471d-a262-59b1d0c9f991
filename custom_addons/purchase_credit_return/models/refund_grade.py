from odoo import models, fields


class RefundGrade(models.Model):
    _name = 'purchase.refund.grade'
    _description = 'Refund Grade'
    _order = 'refund_percentage desc, name'

    name = fields.Char(
        string='Grade',
        required=True,
        help="Letter or label representing the refund grade (e.g. A, B, C, etc.)."
    )

    refund_percentage = fields.Float(
        string='Refund %',
        required=True,
        help="The percentage of the product value to refund when this grade is applied."
    )

    notes = fields.Text(
        string='Notes',
        help="Internal notes or explanation for this grade (e.g. why this percentage is applied)."
    )

    credit_item_id = fields.Many2one(
        'purchase.credit.item',
        string='Credit Item',
        required=True,
        ondelete='cascade',
        index=True,
        help="The Credit Item this grade applies to. Each item can have its own grade logic."
    )

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        readonly=True,
        store=True
    )
