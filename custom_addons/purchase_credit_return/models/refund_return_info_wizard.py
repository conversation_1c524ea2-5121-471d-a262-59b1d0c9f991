from odoo import models, fields, api, _
from odoo.exceptions import UserError

class RefundReturnInfoWizard(models.TransientModel):
    _name = 'refund.return.info.wizard'
    _description = 'Refund Return Info Wizard'

    refund_line_id = fields.Many2one(
        'purchase.credit.refund.line',
        string='Refund Line',
        required=True,
        readonly=True
    )

    return_reason = fields.Selection([
        ('reason_1', 'Wrong item delivered'),
        ('reason_2', 'Damaged item'),
        ('reason_3', 'Warranty return'),
        ('reason_4', 'Other'),
    ], string='Return Reason')

    return_comment = fields.Text(string='Return Comment')

    return_image_ids = fields.Many2many(
        'ir.attachment',
        'wizard_return_image_rel',
        'wizard_id',
        'attachment_id',
        string='Return Images'
    )

    decision = fields.Selection([
        ('approve', 'Approve'),
        ('reject', 'Reject'),
    ], string='Decision', required=True)

    def action_save_return_info(self):
        self.ensure_one()
        if self.refund_line_id:
            self.refund_line_id.write({
                'return_reason': self.return_reason,
                'return_comment': self.return_comment,
                'return_image_ids': [(6, 0, self.return_image_ids.ids)]
            })

    def action_submit_decision(self):
        self.ensure_one()
        if not self.refund_line_id or not self.refund_line_id.refund_id:
            raise UserError("No linked refund record found.")

        refund = self.refund_line_id.refund_id
        partner = refund.partner_id

        if not partner.email:
            raise UserError("Customer has no email address.")

        # Determine which template to use
        if self.decision == 'approve':
            template_id = 'purchase_credit_return.email_template_notify_reviewed'
        elif self.decision == 'reject':
            template_id = 'purchase_credit_return.email_template_notify_rejected'
        else:
            raise UserError("Please select whether the return is approved or rejected.")

        template = self.env.ref(template_id, raise_if_not_found=False)
        if not template:
            raise UserError("Email template not found.")

        # Validate and sanitize language
        installed_langs = self.env['res.lang'].get_installed()
        lang = partner.lang if partner.lang in installed_langs else 'en_US'

        # Send the appropriate email
        template.with_context(
            lang=lang,
            comment=self.return_comment or ''
        ).send_mail(refund.id, force_send=True)

        refund.message_post(body=f"Customer notified: return {self.decision.upper()} via Return Info Wizard.")

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Customer Notified'),
                'message': _('The customer has been notified by email.'),
                'type': 'success'
            }
        }
