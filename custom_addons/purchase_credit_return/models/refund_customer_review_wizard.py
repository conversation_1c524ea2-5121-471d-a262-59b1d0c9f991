from odoo import models, fields, api, _
from odoo.exceptions import UserError


class RefundCustomerReviewWizard(models.TransientModel):
    _name = 'refund.customer.review.wizard'
    _description = 'Customer Refund Review Decision'

    refund_id = fields.Many2one(
        'purchase.credit.refund',
        required=True,
        readonly=True,
        default=lambda self: self.env.context.get('active_id')
    )
    decision = fields.Selection([
        ('approve', 'Approve Refund Request'),
        ('reject', 'Reject Refund Request')
    ], string="Decision", required=True)
    comment = fields.Text(string="Optional Comment")

    def action_submit_decision(self):
        self.ensure_one()
        if not self.refund_id:
            raise UserError("No refund record found.")

        template_id = ''
        if self.decision == 'approve':
            template_id = 'purchase_credit_return.email_template_notify_reviewed'
        elif self.decision == 'reject':
            template_id = 'purchase_credit_return.email_template_notify_rejected'

        template = self.env.ref(template_id, raise_if_not_found=False)
        if not template:
            raise UserError("Email template not found.")

        if not self.refund_id.partner_id.email:
            raise UserError("No email address found for this customer.")

        # Send the actual email
        template.with_context(comment=self.comment).send_mail(self.refund_id.id, force_send=True)

        # Render HTML to post in the chatter
        rendered_email = template._render_field('body_html', [self.refund_id.id])[self.refund_id.id]
        self.refund_id.message_post(
            body=rendered_email,
            subject=_("Customer Notification: Refund Review %s" % self.refund_id.name),
            subtype_xmlid="mail.mt_note",
        )

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Customer Notified'),
                'message': _('The customer has been notified about the refund review decision.'),
                'type': 'success'
            }
        }
