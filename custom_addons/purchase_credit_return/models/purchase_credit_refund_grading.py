import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class PurchaseCreditRefundGrading(models.Model):
    _name = 'purchase.credit.refund.grading'
    _description = 'Refund Grading Line'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    refund_id = fields.Many2one(
        'purchase.credit.refund',
        string="Refund",
        ondelete="cascade",
        required=True,
        tracking=True,
    )

    line_ref_id = fields.Many2one(
        'purchase.credit.refund.line',
        string="Refund Line",
        ondelete="cascade",
        tracking=True,
    )

    product_id = fields.Many2one(
        'product.product',
        string="Product",
        required=True,
        tracking=True,
    )

    serial_number = fields.Char(string="Serial Number", tracking=True)
    quantity = fields.Float(string="Quantity", default=1.0, tracking=True)

    currency_id = fields.Many2one(
        related='refund_id.currency_id',
        store=True,
        readonly=True
    )

    refund_amount = fields.Monetary(
        string="Refund Amount",
        compute="_compute_refund_amount",
        store=True,
        currency_field="currency_id"
    )

    final_score = fields.Float(string="Score (%)", readonly=True, tracking=True)

    final_grade = fields.Selection(
        selection=lambda self: self._get_grade_selection(),
        string="Final Grade",
        readonly=True,
        tracking=True
    )

    graded_by = fields.Many2one(
        'res.users',
        string="Graded By",
        readonly=True,
        tracking=True
    )

    credit_item_id = fields.Many2one(
        'purchase.credit.item',
        string="Credit Item",
        compute='_compute_credit_item',
        store=True,
        readonly=True,
        tracking=True
    )

    answer_ids = fields.One2many(
        'purchase.credit.refund.grading.answer',
        'grading_id',
        string="Saved Answers"
    )

    @api.depends('product_id')
    def _compute_credit_item(self):
        for rec in self:
            rec.credit_item_id = self.env['purchase.credit.item'].search([
                ('product_categ_id', 'child_of', rec.product_id.categ_id.id)
            ], limit=1)
        
    @api.depends('final_grade', 'refund_id.invoice_id', 'product_id', 'quantity')
    def _compute_refund_amount(self):
        for line in self:
            line.refund_amount = 0.0

            if not (line.final_grade and line.refund_id and line.refund_id.invoice_id):
                # Clear grading result if grade was unset or incomplete
                if line.line_ref_id:
                    line.line_ref_id.grading_result = False
                continue

            invoice_line = self.env['account.move.line'].search([
                ('move_id', '=', line.refund_id.invoice_id.id),
                ('product_id', '=', line.product_id.id),
            ], limit=1)

            if not invoice_line:
                continue

            unit_price = invoice_line.price_unit * (1 - invoice_line.discount / 100.0)
            base = unit_price * line.quantity

            refund_grade = self.env['purchase.refund.grade'].search([
                ('name', '=', line.final_grade),
                ('credit_item_id', '=', line.credit_item_id.id)
            ], limit=1)

            refund_pct = refund_grade.refund_percentage if refund_grade else 0.0
            refund_amt = round((refund_pct / 100.0) * base, 2)
            line.refund_amount = refund_amt

            # Propagate to refund line
            if line.line_ref_id:
                line.line_ref_id.refund_amount = refund_amt
                line.line_ref_id.grading_score = line.final_score
                line.line_ref_id.final_grade = line.final_grade

                if line.final_grade and line.final_grade.lower() in ['approve', 'decline']:
                    line.line_ref_id.grading_result = line.final_grade.lower()
                else:
                    line.line_ref_id.grading_result = False

            # Recompute parent totals
            if line.refund_id:
                line.refund_id._compute_totals()
                line.refund_id.invalidate_recordset(['refund_amount', 'refund_tax_amount', 'total_incl_vat'])

                # Update status if not yet graded
                if line.refund_id.refund_status in ['draft', 'submitted']:
                    line.refund_id.write({'refund_status': 'graded'})

                # Set who graded it
                line.graded_by = self.env.user

                # Post log message
                line.refund_id.message_post(body=_(
                    "Grading completed for product <b>%s</b> (SN: %s).<br/>"
                    "Status updated to <b>Graded</b> by <b>%s</b>."
                ) % (
                    line.product_id.display_name,
                    line.serial_number or 'N/A',
                    self.env.user.name
                ))

                _logger.info(
                    "Refund %s graded by %s for product %s",
                    line.refund_id.name,
                    self.env.user.login,
                    line.product_id.name
                )

    def _get_grade_selection(self):
        return [(r.name, r.name) for r in self.env['purchase.refund.grade'].search([])]

    def start_grading(self):
        self.ensure_one()
        _logger.info("Starting grading wizard for product: %s, serial: %s", self.product_id.display_name, self.serial_number)

        wizard_view = self.env.ref('purchase_credit_return.view_refund_grading_wizard_form', raise_if_not_found=False)
        if not wizard_view:
            raise UserError("The grading wizard view could not be found. Please reinstall the module.")

        return {
            'type': 'ir.actions.act_window',
            'name': 'Grade Item',
            'res_model': 'refund.grading.wizard',
            'view_mode': 'form',
            'view_id': wizard_view.id,
            'target': 'new',
            'context': {
                'default_refund_grading_id': self.id
            }
        }
