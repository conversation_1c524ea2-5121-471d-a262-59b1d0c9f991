from odoo import models, fields, api
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class CreditRefundScanWizard(models.TransientModel):
    _name = 'purchase.credit.refund.scan.wizard'
    _description = 'Scan Invoice or Serial'

    scan_code = fields.Char(string="Scan Invoice or Serial")

    def action_scan(self):
        code = (self.scan_code or '').strip()
        if not code:
            raise UserError("Please scan or enter an invoice number or serial number.")

        _logger.info("[ScanWizard] Scanning input: %s", code)
        refund_model = self.env['purchase.credit.refund']

        # === Try 1: Scan matches invoice directly ===
        invoice = self.env['account.move'].search([
            ('move_type', '=', 'out_invoice'),
            ('state', '=', 'posted'),
            ('name', '=', code)
        ], limit=1)

        if invoice:
            serials = invoice.invoice_line_ids.mapped(
                'sale_line_ids.move_ids.move_line_ids.lot_id.name'
            )
            serials = list(set(filter(None, serials)))
            _logger.info("[ScanWizard] Serial numbers from invoice %s: %s", invoice.name, serials)

            refund = refund_model.create({
                'invoice_id': invoice.id,
                'partner_id': invoice.partner_id.id if invoice.partner_id else False,
                'currency_id': invoice.currency_id.id if invoice.currency_id else self.env.company.currency_id.id,
                'serial_number': ', '.join(serials),
            })

            return {
                'type': 'ir.actions.act_window',
                'res_model': 'purchase.credit.refund',
                'res_id': refund.id,
                'view_mode': 'form',
                'target': 'current',
            }

        # === Try 2: Scan matches serial number ===
        lot = self.env['stock.lot'].search([('name', '=', code)], limit=1)
        if lot:
            move_line = self.env['stock.move.line'].search([
                ('lot_id', '=', lot.id),
                ('state', '=', 'done'),
            ], order='date desc', limit=1)

            if move_line and move_line.picking_id:
                origin = move_line.picking_id.origin
                _logger.info("[ScanWizard] Serial %s linked to delivery with origin: %s", code, origin)

                invoice = self.env['account.move'].search([
                    ('invoice_origin', '=', origin),
                    ('move_type', '=', 'out_invoice'),
                    ('state', '=', 'posted')
                ], limit=1)

                if not invoice:
                    raise UserError("No invoice found for the scanned serial number.")

                refund = refund_model.create({
                    'serial_number': lot.name,
                    'invoice_id': invoice.id,
                    'partner_id': invoice.partner_id.id if invoice.partner_id else False,
                    'currency_id': invoice.currency_id.id if invoice.currency_id else self.env.company.currency_id.id,
                })

                return {
                    'type': 'ir.actions.act_window',
                    'res_model': 'purchase.credit.refund',
                    'res_id': refund.id,
                    'view_mode': 'form',
                    'target': 'current',
                }

        _logger.warning("[ScanWizard] No matching invoice or serial found for code: %s", code)
        raise UserError("No matching invoice or serial number found.")

    def action_manual(self):
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.credit.refund',
            'view_mode': 'form',
            'target': 'current',
        }
