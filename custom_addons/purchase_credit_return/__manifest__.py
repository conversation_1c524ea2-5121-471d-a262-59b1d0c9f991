{
    'name': 'Purchase Credit Return',
    'version': '1.0',
    'author': '<PERSON><PERSON>',
    'category': 'Purchases',
    'summary': 'Manage credit returns with refund grades and criteria',
    'depends': [
        'purchase',
        'stock',
        'account',
        'mail',
        'website',
        'portal',
    ],
    'data': [
        # Security
        'security/security.xml',
        'security/ir.model.access.csv',

        # Sequences
        'data/ir_sequence.xml',

        # Credit Item Setup
        'views/credit_item_views.xml',
        'views/refund_grade_views.xml',
        'views/refund_criteria_views.xml',

        # Refund Processing
        'views/purchase_credit_refund_views.xml',
        'views/purchase_credit_refund_scan_wizard_views.xml',
        'views/refund_grading_wizard_views.xml',  

        # Accounting Extension
        'views/account_move_views.xml',

        # UI Actions and Menus
        'views/purchase_credit_refund_actions.xml',
        'views/menu.xml',

        # Policy System
        'views/policy_settings_view.xml',
        'views/policy_template_page.xml',

        # Mail Templates
        'data/email_templates.xml',
        'data/mail_template_refund_result.xml',

        # Portal/Public Templates
        'views/refund_portal_template.xml',
        'views/portal_refund_request_done.xml',
    ],
    'installable': True,
    'application': True,
    'license': 'LGPL-3',
}
