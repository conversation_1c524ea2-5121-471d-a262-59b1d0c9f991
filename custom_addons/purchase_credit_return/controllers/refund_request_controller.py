from odoo import http, fields
from odoo.http import request
from odoo.tools import format_date
import base64
import logging

_logger = logging.getLogger(__name__)


class RefundRequestController(http.Controller):

    @http.route(['/credit-refund-request'], type='http', auth='public', website=True, csrf=False)
    def credit_refund_request_form(self, **post):
        # Always fetch active policy to display
        policy = request.env['credit.policy.settings'].sudo().search([('is_active', '=', True)], limit=1)
        sales_policy = policy.sales_policy if policy else ''
        refund_policy = policy.refund_policy if policy else ''

        # If invoice number was submitted
        if 'invoice_number' in post:
            invoice = request.env['account.move'].sudo().search([
                ('move_type', '=', 'out_invoice'),
                ('state', '=', 'posted'),
                ('name', '=', post.get('invoice_number'))
            ], limit=1)

            if invoice:
                invoice_lines = []
                for line in invoice.invoice_line_ids.filtered(lambda l: l.product_id):
                    serial = request.env['stock.move.line'].sudo().search([
                        ('product_id', '=', line.product_id.id),
                        ('lot_id', '!=', False),
                        ('company_id', '=', invoice.company_id.id),
                        '|',
                        ('move_id.sale_line_id.invoice_lines.move_id', '=', invoice.id),
                        ('picking_id.origin', '=', invoice.invoice_origin),
                    ], order='id desc', limit=1)

                    serial_number = serial.lot_id.name if serial and serial.lot_id else ''
                    invoice_lines.append({
                        'line': line,
                        'serial_number': serial_number,
                        'escaped_name': (line.product_id.display_name or '').replace("'", "\\'")
                    })

                return request.render('purchase_credit_return.portal_refund_request_form', {
                    'invoice': invoice,
                    'invoice_lines': invoice_lines,
                    'request_date': fields.Date.today(),
                    'format_date': lambda d: format_date(request.env, d),
                    'sales_policy': sales_policy,
                    'refund_policy': refund_policy,
                })

            return request.render('purchase_credit_return.portal_refund_request_form', {
                'error': 'Invoice not found or not eligible.',
                'sales_policy': sales_policy,
                'refund_policy': refund_policy,
            })

        # Default empty form with policies
        return request.render('purchase_credit_return.portal_refund_request_form', {
            'sales_policy': sales_policy,
            'refund_policy': refund_policy,
        })

    @http.route(['/submit-refund-request'], type='http', auth='public', website=True, csrf=False)
    def submit_refund_request(self, **post):
        invoice_id = int(post.get('invoice_id', 0))
        invoice = request.env['account.move'].sudo().browse(invoice_id)

        if not invoice or invoice.state != 'posted' or invoice.move_type != 'out_invoice':
            return request.render('purchase_credit_return.portal_refund_request_form', {
                'error': 'Invalid invoice. Please try again.'
            })

        refund_lines = []
        image_map = {}
        index = 0

        for key in post:
            if key.startswith('line_') and post[key] == 'on':
                line_id = int(key.split('_')[1])
                line = request.env['account.move.line'].sudo().browse(line_id)
                product = line.product_id

                serial = request.env['stock.move.line'].sudo().search([
                    ('product_id', '=', product.id),
                    ('lot_id', '!=', False),
                    ('company_id', '=', invoice.company_id.id),
                    '|',
                    ('move_id.sale_line_id.invoice_lines.move_id', '=', invoice.id),
                    ('picking_id.origin', '=', invoice.invoice_origin),
                ], order='id desc', limit=1)

                serial_number = serial.lot_id.name if serial and serial.lot_id else ''

                image_buffers = []
                for image in request.httprequest.files.getlist(f'images_{line_id}'):
                    if image.filename:
                        image_buffers.append({
                            'name': image.filename,
                            'datas': base64.b64encode(image.read()).decode('utf-8'),
                            'res_model': 'purchase.credit.refund.line',
                        })

                image_map[index] = image_buffers

                refund_lines.append((0, 0, {
                    'product_id': product.id,
                    'serial_numbers': serial_number,
                    'quantity': line.quantity,
                    'price_unit': line.price_unit,
                    'discount': line.discount,
                    'is_selected': True,
                    'refund': True,
                    'return_reason': post.get(f'reason_{line_id}'),
                    'return_comment': post.get(f'comment_{line_id}'),
                }))
                index += 1

        refund_method = post.get('refund_method')

        refund = request.env['purchase.credit.refund'].sudo().create({
            'invoice_id': invoice.id,
            'partner_id': invoice.partner_id.id,
            'refund_date': fields.Date.today(),
            'refund_method': refund_method,
            'refund_status': 'submitted',
            'refund_line_ids': refund_lines
        })

        for i, line in enumerate(refund.refund_line_ids):
            if i in image_map and image_map[i]:
                attachments = []
                for attachment in image_map[i]:
                    attachment['res_id'] = line.id
                    att = request.env['ir.attachment'].sudo().create(attachment)
                    attachments.append(att.id)
                line.write({'return_image_ids': [(6, 0, attachments)]})

        return request.render('purchase_credit_return.portal_refund_request_done', {
            'refund': refund,
        })

    @http.route('/submit-refund-image', type='http', auth='public', methods=['POST'], csrf=False)
    def submit_refund_image(self, **post):
        line_id = int(post.get('line_id'))
        images = request.httprequest.files.getlist('images')

        for image in images:
            content = base64.b64encode(image.read()).decode('utf-8')
            request.env['ir.attachment'].sudo().create({
                'name': image.filename,
                'datas': content,
                'res_model': 'purchase.credit.refund.line',
                'res_id': line_id,
            })

        return "OK"
