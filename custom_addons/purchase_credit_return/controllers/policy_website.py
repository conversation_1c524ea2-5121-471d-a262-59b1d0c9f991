from odoo import http
from odoo.http import request

class CreditPolicyWebsite(http.Controller):

    @http.route(['/policy/sales', '/policy/refund'], type='http', auth='public', website=True)
    def show_policy(self, **kw):
        record = request.env['credit.policy.settings'].sudo().search([('is_active', '=', True)], limit=1)
        if not record:
            record = request.env['credit.policy.settings'].sudo().create({})
        return request.render('purchase_credit_return.policy_page', {
            'sales_policy': record.sales_policy,
            'refund_policy': record.refund_policy,
            'active_page': request.httprequest.path
        })
