<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_partner_form_inherit_sa_cn_pricelist" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.sa.cn.pricelist</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">

            <!-- 🔥 Remove default Salesperson and Pricelist fields -->
            <xpath expr="//page[@name='sales_purchases']//group[@name='sale']/field[@name='user_id']" position="replace"/>
            <xpath expr="//page[@name='sales_purchases']//group[@name='sale']/field[@name='property_product_pricelist']" position="replace"/>

            <!-- ✅ Insert your custom fields block -->
            <xpath expr="//page[@name='sales_purchases']//group[@name='sale']" position="after">
                <group string="Multi-Company Salespeople &amp; Pricing">
                    <group string="Multi-Company Salespeople">
                        <field name="salesperson_sa_id"/>
                        <field name="salesperson_cn_id"/>
                    </group>
                    <group string="Multi-Company Pricing">
                        <field name="pricelist_sa_id"/>
                        <field name="pricelist_cn_id"/>
                    </group>
                </group>
            </xpath>

        </field>
    </record>
</odoo>
