import logging
from odoo import models, api

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.model
    def create(self, vals):
        partner_id = vals.get('partner_id')
        _logger.info("💥 SaleOrder.create called. partner_id=%s", partner_id)

        if partner_id:
            partner = self.env['res.partner'].browse(partner_id)
            currency = self.env.company.currency_id.name.upper()
            _logger.info("💥 Currency is %s", currency)

            if currency == 'CNY':
                if partner.pricelist_cn_id:
                    _logger.info("✅ CN Pricelist found: %s", partner.pricelist_cn_id.name)
                    vals['pricelist_id'] = partner.pricelist_cn_id.id
                if partner.salesperson_cn_id:
                    _logger.info("✅ CN Salesperson found: %s", partner.salesperson_cn_id.name)
                    vals['user_id'] = partner.salesperson_cn_id.id
                else:
                    _logger.warning("⚠️ No CN Salesperson set on partner.")
            else:
                _logger.info("❌ Currency is not CNY — skipping CN logic.")

        return super().create(vals)

    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        if self.partner_id:
            currency = self.env.company.currency_id.name.upper()
            if currency == 'CNY':
                if self.partner_id.pricelist_cn_id:
                    self.pricelist_id = self.partner_id.pricelist_cn_id
                if self.partner_id.salesperson_cn_id:
                    self.user_id = self.partner_id.salesperson_cn_id
