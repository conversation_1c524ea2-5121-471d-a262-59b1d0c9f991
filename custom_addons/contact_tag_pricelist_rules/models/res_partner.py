from odoo import models, api
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # --- Helpers -------------------------------------------------------------
    @api.model
    def _get_tag_id(self, name):
        tag = self.env['res.partner.category'].sudo().search([('name', '=', name)], limit=1)
        return tag.id if tag else False

    @api.model
    def _get_pricelist_id(self, name):
        pricelist = self.env['product.pricelist'].sudo().search([('name', '=', name)], limit=1)
        return pricelist.id if pricelist else False

    @api.model
    def _get_industry_id(self, name):
        industry = self.env['res.partner.industry'].sudo().search([('name', '=', name)], limit=1)
        return industry.id if industry else False

    # --- Core updater --------------------------------------------------------
    def _update_tags_and_pricelist(self):
        # Labels for tags applied to partners
        tag_map = {
            'market_segment': {
                'auto': 'Automotive',
                'commercial': 'Commercial',
                'off_highway': 'Off-Highway',
                'components': 'Components',
                'internal': 'Internal',
                # NOTE: retail/wholesale are NOT market segments
            },
            'distribution_group_id': {
                'national_distributor': 'National Distributor',
                'wholesaler': 'Wholesaler',
                'retail': 'Retail',
                'workshop': 'Workshop',
                'fleet': 'Fleet',
                'turbo': 'Turbo',
                'diesel': 'Diesel',
                'electrical': 'Electrical',
                'auto_engineer': 'Auto Engineering',
                'internal_reman': 'Internal Reman',
                'external_repairs': 'External Repairs',
                # NOTE: Autozone/Alert removed from distribution groups
            },
            'customer_grading': {
                'a': 'A Grade',
                'b': 'B Grade',
                'c': 'C Grade',
                'd': 'D Grade',
                'e': 'E Grade',
                'cod': 'C.O.D',
                'competitor': 'Competitor',
                'autozone': 'Autozone',
                'alert': 'Alert',
            }
        }

        # Base group/grade mapping (A–E, COD, Competitor) — do NOT add autozone/alert here
        pricelist_map = {
            ('national_distributor', 'a'): 'A - National Distribution (35-50%)',
            ('national_distributor', 'b'): 'B - National Distribution (32.5-47.5%)',
            ('national_distributor', 'c'): 'C - National Distribution (30-45%)',
            ('national_distributor', 'd'): 'D - National Distribution (27.5-42.5%)',
            ('national_distributor', 'e'): 'E - National Distribution (25-40%)',
            ('national_distributor', 'cod'): 'Counter Sales (5-20%)',
            ('national_distributor', 'competitor'): 'Competitor (0%)',

            ('wholesaler', 'a'): 'A - Wholesale (35-50%)',
            ('wholesaler', 'b'): 'B - Wholesale (32.5-47.5%)',
            ('wholesaler', 'c'): 'C - Wholesale (30-45%)',
            ('wholesaler', 'd'): 'D - Wholesale (27.5-42.5%)',
            ('wholesaler', 'e'): 'E - Wholesale (25-40%)',
            ('wholesaler', 'cod'): 'Counter Sales (5-20%)',
            ('wholesaler', 'competitor'): 'Competitor (0%)',

            ('retail', 'a'): 'A - Retail (25-40%)',
            ('retail', 'b'): 'B - Retail (22.5-37.5%)',
            ('retail', 'c'): 'C - Retail (20-35%)',
            ('retail', 'd'): 'D - Retail (17.5-32.5%)',
            ('retail', 'e'): 'E - Retail (15-30%)',
            ('retail', 'cod'): 'Counter Sales (5-20%)',
            ('retail', 'competitor'): 'Competitor (0%)',

            ('workshop', 'a'): 'A - Workshop (25-40%)',
            ('workshop', 'b'): 'B - Workshop (22.5-37.5%)',
            ('workshop', 'c'): 'C - Workshop (20-35%)',
            ('workshop', 'd'): 'D - Workshop (17.5-32.5%)',
            ('workshop', 'e'): 'E - Workshop (15-30%)',
            ('workshop', 'cod'): 'Counter Sales (5-20%)',
            ('workshop', 'competitor'): 'Competitor (0%)',

            ('fleet', 'a'): 'A - Fleet (25-40%)',
            ('fleet', 'b'): 'B - Fleet (22.5-37.5%)',
            ('fleet', 'c'): 'C - Fleet (20-35%)',
            ('fleet', 'd'): 'D - Fleet (17.5-32.5%)',
            ('fleet', 'e'): 'E - Fleet (15-30%)',
            ('fleet', 'cod'): 'Counter Sales (5-20%)',
            ('fleet', 'competitor'): 'Competitor (0%)',

            ('turbo', 'a'): 'A - Turbo Only (35-50%)',
            ('turbo', 'b'): 'B - Turbo Only (32.5-47.5%)',
            ('turbo', 'c'): 'C - Turbo Only (30-45%)',
            ('turbo', 'd'): 'D - Turbo Only (27.5-42.5%)',
            ('turbo', 'e'): 'E - Turbo Only (25-40%)',
            ('turbo', 'cod'): 'Counter Sales (5-20%)',
            ('turbo', 'competitor'): 'Competitor (0%)',

            ('diesel', 'a'): 'A - Diesel Only (35-50%)',
            ('diesel', 'b'): 'B - Diesel Only (32.5-47.5%)',
            ('diesel', 'c'): 'C - Diesel Only (30-45%)',
            ('diesel', 'd'): 'D - Diesel Only (27.5-42.5%)',
            ('diesel', 'e'): 'E - Diesel Only (25-40%)',
            ('diesel', 'cod'): 'Counter Sales (5-20%)',
            ('diesel', 'competitor'): 'Competitor (0%)',

            ('electrical', 'a'): 'Electrical Only (35-50%)',
            ('electrical', 'b'): 'Electrical Only (35-50%)',
            ('electrical', 'c'): 'Electrical Only (35-50%)',
            ('electrical', 'd'): 'Electrical Only (35-50%)',
            ('electrical', 'e'): 'Electrical Only (35-50%)',
            ('electrical', 'cod'): 'Counter Sales (5-20%)',
            ('electrical', 'competitor'): 'Competitor (0%)',

            ('auto_engineer', 'a'): 'Auto Engineering (25-40%)',
            ('auto_engineer', 'b'): 'Auto Engineering (25-40%)',
            ('auto_engineer', 'c'): 'Auto Engineering (25-40%)',
            ('auto_engineer', 'd'): 'Auto Engineering (25-40%)',
            ('auto_engineer', 'e'): 'Auto Engineering (25-40%)',
            ('auto_engineer', 'cod'): 'Counter Sales (5-20%)',
            ('auto_engineer', 'competitor'): 'Competitor (0%)',

            ('internal_reman', 'a'): 'TDS Inhouse Pricing/Internal Reman Only',
            ('internal_reman', 'b'): 'TDS Inhouse Pricing/Internal Reman Only',
            ('internal_reman', 'c'): 'TDS Inhouse Pricing/Internal Reman Only',
            ('internal_reman', 'd'): 'TDS Inhouse Pricing/Internal Reman Only',
            ('internal_reman', 'e'): 'TDS Inhouse Pricing/Internal Reman Only',
            ('internal_reman', 'cod'): 'TDS Inhouse Pricing/Internal Reman Only',
            ('internal_reman', 'competitor'): 'TDS Inhouse Pricing/Internal Reman Only',

            ('external_repairs', 'a'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
            ('external_repairs', 'b'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
            ('external_repairs', 'c'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
            ('external_repairs', 'd'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
            ('external_repairs', 'e'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
            ('external_repairs', 'cod'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
            ('external_repairs', 'competitor'): 'TDS Inhouse Pricing/EXTERNAL Repairs Only',
        }

        # Industry (fix external_repairs)
        industry_map_by_group = {
            'national_distributor': 'NATIONAL DISTRIBUTORS',
            'wholesaler': 'WHOLESALER',
            'retail': 'RETAIL',
            'workshop': 'WORKSHOP',
            'fleet': 'FLEET',
            'turbo': 'TURBO ONLY',
            'diesel': 'DIESEL ONLY',
            'electrical': 'ELECTRICAL ONLY',
            'auto_engineer': 'AUTO ENGINEERING',
            'internal_reman': 'INTERNAL',
            'external_repairs': 'INTERNAL',
        }

        # Salesperson routing
        salesperson_map = {
            'national_distributor': 17,
            'wholesaler': 17,
            'retail': 17,
            'workshop': 41,
            'turbo': 21,
            'diesel': 21,
            'electrical': 21,
            'fleet': 41,
            'auto_engineer': 41,
            'internal_reman': 40,
            'external_repairs': 40,
        }

        # Validation rule sets
        vehicular_segments = {'auto', 'commercial', 'off_highway'}  # no retail/wholesale as segments
        invalid_groups_vehicular = {'turbo', 'diesel', 'electrical', 'auto_engineer'}
        invalid_groups_components = {'national_distributor', 'wholesaler', 'retail', 'workshop', 'fleet'}
        valid_internal_groups = {'internal_reman', 'external_repairs'}

        # Grade-level exclusive pricelists (group-agnostic)
        EXCLUSIVE_BY_GRADE = {
            'autozone': 'Autozone Exclusive Pricing (40-50%)',
            'alert': 'Alert Exclusive Pricing (35-50%)',
        }

        for partner in self:
            seg = partner.market_segment or ''
            grp = partner.distribution_group_id or ''

            # --- Validation ---------------------------------------------------
            if seg in vehicular_segments and grp in invalid_groups_vehicular:
                pretty_seg = (' '.join(seg.split('_'))).title()
                raise UserError(
                    "❌ Invalid Distribution Group for this Market Segment.\n\n"
                    f"➡ Segment: {pretty_seg}\n"
                    f"➡ Group: {grp.title()}\n\n"
                    "✅ Please select one of the following valid groups:\n"
                    "- National Distributor\n"
                    "- Wholesaler\n"
                    "- Retail\n"
                    "- Workshop\n"
                    "- Fleet"
                )

            if seg == 'components' and grp in invalid_groups_components:
                raise UserError(
                    "❌ Invalid Distribution Group for this Market Segment.\n\n"
                    "➡ Segment: Components\n"
                    f"➡ Group: {grp.title()}\n\n"
                    "✅ Please select one of the following valid groups:\n"
                    "- Turbo\n"
                    "- Diesel\n"
                    "- Electrical\n"
                    "- Auto Engineer"
                )

            if seg == 'internal' and grp not in valid_internal_groups:
                raise UserError(
                    "❌ Invalid Distribution Group for Internal Market Segment.\n\n"
                    "➡ Segment: Internal\n"
                    f"➡ Group: {grp.title()}\n\n"
                    "✅ Please select one of:\n"
                    "- Internal Reman\n"
                    "- External Repairs"
                )

            # --- Side effects: tags, pricelist, industry, salesperson --------
            vals = {}
            tags = []

            # Tags from segment/group/grade
            for field, mapping in tag_map.items():
                key = getattr(partner, field)
                if key and key in mapping:
                    tag_id = self._get_tag_id(mapping[key])
                    if tag_id:
                        tags.append(tag_id)

            if partner.has_customer_info:
                customer_tag = self._get_tag_id('Customer')
                if customer_tag:
                    tags.append(customer_tag)
            if partner.has_supplier_info:
                supplier_tag = self._get_tag_id('Supplier')
                if supplier_tag:
                    tags.append(supplier_tag)

            if tags:
                vals['category_id'] = [(6, 0, list(set(tags)))]

            # Pricelist resolution
            grading = partner.customer_grading

            # 1) Exclusive grades: Autozone / Alert (ignore group)
            forced_name = EXCLUSIVE_BY_GRADE.get(grading)
            if forced_name:
                pl_id = self._get_pricelist_id(forced_name)
                if pl_id:
                    vals['property_product_pricelist'] = pl_id
                else:
                    _logger.warning("Exclusive pricelist '%s' not found; leaving partner %s pricelist unchanged.",
                                    forced_name, partner.display_name or partner.id)
            else:
                # 2) Standard A–E / COD / Competitor mapping
                if grp and grading:
                    pl_name = pricelist_map.get((grp, grading))
                    if pl_name:
                        pl_id = self._get_pricelist_id(pl_name)
                        if pl_id:
                            vals['property_product_pricelist'] = pl_id

            # Industry by group
            if grp and grp in industry_map_by_group:
                industry_name = industry_map_by_group[grp]
                industry_id = self._get_industry_id(industry_name)
                if industry_id:
                    vals['industry_id'] = industry_id

            # Salesperson by group
            if grp and grp in salesperson_map:
                vals['user_id'] = salesperson_map[grp]

            if vals:
                # Avoid recursion with write()
                self.browse(partner.id).with_context(skip_tag_update=True).sudo().write(vals)

    # --- Hooks ---------------------------------------------------------------
    def write(self, vals):
        result = super().write(vals)
        if not self.env.context.get('skip_tag_update'):
            self._update_tags_and_pricelist()
        return result

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        records._update_tags_and_pricelist()
        return records
