<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!--
    ============================================================================
    Assets
    ============================================================================
    -->
    <template id="layout" inherit_id="website.layout" name="Theme Prime: Layout">
        <xpath expr="//head/meta[last()]" position="after">
            <t t-if="request.website.dr_pwa_activated">
                <link rel="manifest" t-attf-href="/pwa/#{request.website.id}/manifest.json"/>
                <link rel="apple-touch-icon" sizes="192x192" t-attf-href="/web/image/website/#{request.website.id}/dr_pwa_icon_192/192x192"/>
                <meta name="theme-color" t-att-content="request.website.dr_pwa_theme_color"/>
                <meta name="mobile-web-app-capable" content="yes"/>
                <meta name="apple-mobile-web-app-title" t-att-content="request.website.dr_pwa_name"/>
                <meta name="apple-mobile-web-app-capable" content="yes"/>
                <meta name="apple-mobile-web-app-status-bar-style" content="default"/>
            </t>
        </xpath>
        <xpath expr="//head/link[last()]" position="after">
            <link rel="preload" href="/theme_prime/static/lib/Droggol-Pack-v1.0/fonts/Droggol-Pack.ttf?4iwoe7" as="font" crossorigin=""/>
        </xpath>
    </template>

    <!--
    ============================================================================
    Pricelist
    ============================================================================
    -->
    <template id="tp_pricelist_html_data" inherit_id="website.layout" name="Pricelist Attribute" priority="1">
        <xpath expr="//html" position="before">
            <t t-set="html_data" t-value="dict(html_data, **{'data-pricelist-id': website.pricelist_id.id if website.pricelist_id else '*'})"/>
        </xpath>
    </template>

    <!--
    ============================================================================
    Bottombar
    ============================================================================
    -->
    <template id="template_bottom_bar" inherit_id="website.layout" name="Theme Prime: Bottombar">
        <xpath expr="//div[@id='wrapwrap']/header" position="before">
            <t t-if="request.website._get_dr_theme_config('json_bottom_bar')['show_bottom_bar']">
                <t t-call="theme_prime.tp_mobile_bottombar_component"/>
            </t>
        </xpath>
    </template>

    <!--
    ============================================================================
    Menu Label
    ============================================================================
    -->
    <template id="submenu" inherit_id="website.submenu" name="Theme Prime: Submenu">
        <xpath expr="//span[@t-field='submenu.name']/.." position="attributes">
            <attribute name="t-attf-style">#{submenu.dr_is_highlight_menu and 'background-color: %s; color: %s;' % (submenu.dr_highlight_menu_bg_color, submenu.dr_highlight_menu_text_color) or ''}</attribute>
        </xpath>
        <xpath expr="//span[@t-field='submenu.name']" position="replace">
            <span>
                <span t-field="submenu.name"/>
                <span t-if="submenu.dr_menu_label_id" class="tp-menu-label" t-attf-style="background-color: #{submenu.dr_menu_label_id.background_color}; color: #{submenu.dr_menu_label_id.text_color}">
                    <span t-field="submenu.dr_menu_label_id.name"/>
                    <div class="before" t-attf-style="border-color: #{submenu.dr_menu_label_id.background_color};"/>
                </span>
            </span>
        </xpath>
        <xpath expr="//a[@data-bs-toggle='dropdown']" position="attributes">
            <attribute name="t-attf-style">#{submenu.dr_is_highlight_menu and 'background-color: %s; color: %s;' % (submenu.dr_highlight_menu_bg_color, submenu.dr_highlight_menu_text_color) or ''}</attribute>
        </xpath>
        <xpath expr="//a[@data-bs-toggle='dropdown']/span" position="replace">
            <span>
                <span t-field="submenu.name"/>
                <span t-if="submenu.dr_menu_label_id" class="tp-menu-label" t-attf-style="background-color: #{submenu.dr_menu_label_id.background_color}; color: #{submenu.dr_menu_label_id.text_color}">
                    <span t-field="submenu.dr_menu_label_id.name"/>
                    <div class="before" t-attf-style="border-color: #{submenu.dr_menu_label_id.background_color};"/>
                </span>
            </span>
        </xpath>
        <xpath expr="//span[@t-field='submenu.name']" position="before">
            <i t-if="submenu.dr_is_highlight_menu and submenu.is_mega_menu" class="dri dri-category me-1"/>
        </xpath>
        <xpath expr="//a[@data-bs-toggle='dropdown']/span/span" position="before">
            <i t-if="submenu.dr_is_highlight_menu and submenu.is_mega_menu" class="dri dri-category me-1"/>
        </xpath>
        <xpath expr="//t[@t-set='show_dropdown']" position="after">
            <t t-set="item_class" t-valuef="#{item_class or ''} #{submenu.dr_is_highlight_menu and 'tp-highlight-menu' or ''} #{submenu.dr_menu_visible_on == 'desktop' and 'd-none d-lg-block' or ''}"/>
        </xpath>
    </template>

    <!--
    ============================================================================
    Variants
    ============================================================================
    -->
    <template id="variants" inherit_id="website_sale.variants" name="Theme Prime: Variants">
        <xpath expr="//strong[hasclass('attribute_name')]" position="replace">
            <div class="d-flex align-items-center mb-2">
                <h6 t-field="ptal.attribute_id.name" class="attribute_name mb-0 pb-0"/>
                <t t-if="show_attribute_guide">
                    <t t-set="dr_attribute_popup_id" t-value="ptal.dr_attribute_popup_id or ptal.attribute_id.dr_attribute_popup_id"/>
                    <a t-if="dr_attribute_popup_id.active" class="dr-attribute-instruction-btn tp-lazy-content link-primary ms-4" style="display: none;" href="#" data-res-model="dr.website.content" t-att-data-res-id="dr_attribute_popup_id.id" t-att-data-popup-style="dr_attribute_popup_id.popup_style" data-field="content">
                        <span t-field="dr_attribute_popup_id.name"/> <i class="fa fa-angle-right"/>
                    </a>
                </t>
            </div>
        </xpath>
        <xpath expr="//ul/t/li" position="inside">
            <t t-elif="attribute.display_type in ['radio_circle', 'radio_square', 'radio_image']">
                <ul t-att-data-attribute_id="attribute.id"
                    t-attf-class="dr-attribute-item list-inline o_wsale_product_attribute #{'d-none' if single_and_custom else ''}">
                    <t t-foreach="ptavs" t-as="ptav">
                        <li t-attf-class="me-1 list-inline-item js_attribute_value #{'active' if ptav in combination else ''}">
                            <label>
                                <div>
                                    <input type="radio"
                                        t-attf-class="js_variant_change #{attribute.create_variant} d-none"
                                        t-att-checked="ptav in combination"
                                        t-att-name="'ptal-%s' % ptal.id"
                                        t-att-value="ptav.id"
                                        t-att-data-value_id="ptav.id"
                                        t-att-id="ptav.id"
                                        t-att-data-attribute-value-id="ptav.product_attribute_value_id.id"
                                        t-att-data-value_name="ptav.name"
                                        t-att-data-attribute_name="attribute.name"
                                        t-att-data-is_custom="ptav.is_custom"
                                        t-att-data-is_single_and_custom="single_and_custom"
                                        t-att-autocomplete="off"/>
                                    <div class="radio_input_value d-flex flex-column align-items-center me-0">
                                        <div t-if="not attribute.display_type == 'radio_image'" t-attf-class="dr-value-item fw-normal #{attribute.display_type == 'radio_circle' and 'circle' or 'square'}" t-field="ptav.name"/>
                                        <div t-else="" class="dr-value-item image" t-att-title="ptav.name" t-field="ptav.dr_image" t-options="{'widget': 'image'}"/>
                                        <t t-call="website_sale.badge_extra_price"/>
                                    </div>
                                </div>
                            </label>
                        </li>
                    </t>
                </ul>
            </t>
        </xpath>
        <t t-set="img_style" position="after">
            <t t-if="ptav.dr_thumb_image" t-set="img_style"
                t-value="'background:url(/web/image/product.template.attribute.value/%s/dr_thumb_image); background-size:cover;' % ptav.id if ptav.dr_thumb_image else ''"
            />
        </t>
    </template>

    <!--
    ============================================================================
    404 Page
    ============================================================================
    -->
    <template id="404" inherit_id="http_routing.404" name="Theme Prime: 404">
        <xpath expr="//div[@id='wrap']" position="replace">
            <div id="wrap">
                <t t-out="0"/>
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-12 col-lg-6">
                            <t t-call="theme_prime.tp_svg_404">
                                <t t-set="_classes" t-value="'mt-4'"/>
                            </t>
                        </div>
                        <div class="col-12 text-center mt32">
                            <h2>We can't seem to find the page you are looking for</h2>
                            <p class="mt-3 lead">
                                Here are some helpful links instead
                            </p>
                        </div>
                    </div>
                </div>
                <div class="oe_structure oe_empty">
                    <div class="container my-4">
                        <div class="row justify-content-center">
                            <div class="col-12 col-lg-10 col-xl-8">
                                <div class="row">
                                    <div class="col-12 col-lg-4 mb-3">
                                        <a class="card" href="/">
                                            <div class="card-body py-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0">
                                                        <span class="dri dri-home-l fa-2x text-primary ms-1 me-3"></span>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">Homepage</h6>
                                                        <div>Return to homepage</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-12 col-lg-4 mb-3">
                                        <a class="card tp-search-sidebar-action" href="#">
                                            <div class="card-body py-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0">
                                                        <span class="dri dri-search-l fa-2x text-success ms-1 me-3"></span>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">Search</h6>
                                                        <div>Find your product</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-12 col-lg-4 mb-3">
                                        <a class="card" href="/contactus">
                                            <div class="card-body py-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0">
                                                        <span class="dri dri-phone-l fa-2x text-info ms-1 me-3"></span>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">Contact us</h6>
                                                        <div>Get in touch with us</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </xpath>
    </template>

    <!--
    ============================================================================
    Cart Page
    ============================================================================
    -->
    <template id="cart_lines" inherit_id="website_sale.cart_lines" name="Shopping Cart Lines">
        <t t-call="website_sale.cart_line_description_following_lines" position="after">
            <ul class="list-inline mt-2 mb-0 d-none d-md-block" groups="product.group_product_variant">
                <t t-foreach="line.product_id.product_template_variant_value_ids" t-as="att">
                    <li class="list-inline-item mb-1">
                        <span class="badge text-bg-primary">
                            <span t-field="att.attribute_id.name"/>: <span t-field="att.name"/>
                        </span>
                    </li>
                </t>
            </ul>
        </t>
    </template>

    <template id="coupon_form" inherit_id="website_sale.coupon_form">
        <xpath expr="//a[hasclass('a-submit')]" position="attributes">
            <attribute name="class" separator=" " add="btn-primary" remove="btn-secondary"/>
        </xpath>
    </template>

    <template id="suggested_products_list" inherit_id="website_sale.suggested_products_list" name="Alternative Products in my cart">
        <h5 t-if="suggested_products" position="replace">
            <h5 t-attf-class="mt32 mb-4" t-if="suggested_products">Suggested Products</h5>
        </h5>
        <xpath expr="//div[hasclass('o_cart_suggested_product_name')]//a" position="attributes">
            <attribute name="class" separator=" " add="h6"/>
        </xpath>
        <xpath expr="//a[hasclass('js_add_suggested_products')]" position="attributes">
            <attribute name="class" separator=" " add="mb-2"/>
        </xpath>
    </template>

    <!--
    ============================================================================
    Wishlist page
    ============================================================================
    -->
    <template id="product_wishlist" inherit_id="website_sale_wishlist.product_wishlist" name="Theme Prime: Wishlist Page">
        <xpath expr="//section[hasclass('wishlist-section')]" position="attributes">
            <attribute name="t-if">False</attribute>
        </xpath>
        <xpath expr="//section[hasclass('wishlist-section')]" position="after">
            <section class="container wishlist-section my-4">
                <div class="row align-items-center">
                    <div class="col-12">
                        <h3 class="d-inline-block mb-0 me-2">My Wishlist</h3>
                        <span class="tp-wishlist-counter" t-out="len(wishes)"/> items
                        <hr/>
                    </div>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="b2b_wish" value="1"/>
                    <label class="form-check-label" for="b2b_wish">Add product to my cart but keep it in my wishlist</label>
                </div>
                <div class="row py-4">
                    <t t-foreach="wishes" t-as="wish">
                        <t t-set="combination_info" t-value="wish.product_id._get_combination_info_variant()"/>
                        <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4 tp-wishlist-item" t-att-data-wish-id="wish.id" t-att-data-product-id="wish.product_id.id" t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])">
                            <div class="card h-100">
                                <a class="card-img-top text-center" t-att-href="wish.product_id.website_url">
                                    <img t-attf-src="/web/image/product.product/#{wish.product_id.id}/image_1920/350x350" class="img-fluid" t-att-alt="wish.product_id.display_name"/>
                                </a>
                                <a href="#" class="tp_wish_rm tp-icon-center-1 dr-d-icon shadow rounded-circle text-muted position-absolute top-0 end-0 m-2" title="Remove from wishlist">
                                    <i class="fa fa-times"/>
                                </a>
                                <t t-call="theme_prime.product_label">
                                    <t t-set="label" t-value="wish.product_id.dr_label_id"/>
                                </t>
                                <div class="card-body p-2">
                                    <div class="card-text">
                                        <h6 class="text-truncate mb-1">
                                            <a t-att-href="wish.product_id.website_url" class="tp-link-dark" t-att-title="wish.product_id.display_name" t-out="wish.product_id.display_name"/>
                                        </h6>
                                        <div t-if="request.website._dr_has_b2b_access()">
                                            <div t-if="combination_info['prevent_zero_price_sale']">
                                                <strong t-field="website.prevent_zero_price_sale_text"/>
                                            </div>
                                            <div t-else="">
                                                <h6 class="text-primary d-inline-block mb-0" t-out="combination_info['price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                <small t-attf-class="oe_default_price ms-1 {{'' if combination_info['has_discounted_price'] else 'd-none'}}" style="text-decoration: line-through; white-space: nowrap;" t-out="combination_info['list_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                <t t-call="theme_prime.discount_percentage"/>
                                                <small t-if="combination_info['base_unit_price']" class="cart_product_base_unit_price d-block text-muted" groups="website_sale.group_show_uom_price">
                                                    <t t-call='website_sale.base_unit_price'>
                                                        <t t-set='product' t-value='wish.product_id' />
                                                    </t>
                                                </small>
                                            </div>
                                        </div>
                                        <t t-else="" t-call="theme_prime.tp_b2b_price_label"/>
                                        <t t-if="not wish.product_id.allow_out_of_stock_order and wish.product_id._is_sold_out()">
                                            <small class="text-danger">Temporarily out of stock</small>
                                            <t t-set="has_stock_notification" t-value="wish.product_id._has_stock_notification(wish.partner_id) or request and wish.product_id.id in request.session.get('product_with_stock_notification_enabled', set())"/>
                                            <div id="stock_notification_div" t-if="not wish.product_id.allow_out_of_stock_order" class="small d-inline-block w-100">
                                                <div class="btn btn-link" t-if="not has_stock_notification"
                                                    id="wishlist_stock_notification_message">
                                                    <small>
                                                        <i class="fa fa-envelope-o"/>
                                                        Get notified when back in stock
                                                    </small>
                                                </div>
                                                <div id="stock_notification_form" class="d-none">
                                                    <div class="input-group">
                                                        <input id="stock_notification_input"
                                                            class="form-control"
                                                            name="email"
                                                            type="text"
                                                            placeholder="<EMAIL>"
                                                            t-att-value="request.env.user.partner_id.email or request.session.get('stock_notification_email', '')"/>
                                                        <div id="wishlist_stock_notification_form_submit_button"
                                                                class="btn btn-secondary">
                                                            <i class="fa fa-paper-plane"/>
                                                        </div>
                                                        <div id="stock_notification_input_incorrect" class="btn d-none">
                                                            <i class="fa fa-times text-danger"/>
                                                            Invalid email
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="stock_notification_success_message" class="text-muted"
                                                    t-att-class="'text-muted' if has_stock_notification else 'd-none'">
                                                    <i class="fa fa-bell"/>
                                                    We'll notify you once the product is back in stock.
                                                </div>
                                            </div>
                                        </t>
                                        <div class="btn-group w-100" role="group">
                                            <t t-set="is_sold_out" t-value="not wish.product_id.allow_out_of_stock_order and wish.product_id._is_sold_out()"/>
                                            <a t-if="combination_info['prevent_zero_price_sale']" t-att-href="website.contact_us_button_url" class="btn btn-primary btn_cta mt-2">Contact Us</a>
                                            <button t-else="" id="add_to_cart_button" role="button" class="btn btn-primary tp_wish_add mt-2" t-att-disabled="is_sold_out">
                                                <i class="dri dri-cart"></i> Add to cart
                                            </button>
                                            <button type="button" t-if="is_view_active('website_sale_comparison.add_to_compare')" class="btn btn-link o_add_to_compare mt-2 d-none d-md-inline tp-link-dark" t-att-data-product-id='wish.product_id.id'>
                                                <small>
                                                    <i class="dri dri-compare"/> Add to compare
                                                </small>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </section>
        </xpath>
    </template>

    <!--
    ============================================================================
    Comparison page
    ============================================================================
    -->
    <template id="product_compare" inherit_id="website_sale_comparison.product_compare">
        <section class="container" position="attributes">
            <attribute name="t-if">False</attribute>
        </section>
        <section class="container" position="after">
            <section class="container my-4">
                <div class="row align-items-center">
                    <div class="col-12">
                        <h3 class="d-inline-block me-2 mb-0">Comparison</h3>
                        <span>
                            <t t-out="len(products)"/> items
                        </span>
                    </div>
                </div>
                <div class="row py-4 tp-product-comparison">
                    <div class="table-responsive">
                        <table class="table w-auto">
                            <t t-set="categories" t-value="products._prepare_categories_for_display()"/>
                            <thead>
                                <tr>
                                    <td t-if="len(categories)"/>
                                    <td t-foreach="products" t-as="product">
                                        <div class="card border-0">
                                            <a class="card-img-top text-center" t-att-href="product.website_url">
                                                <img t-attf-src="/web/image/product.product/#{product.id}/image_1920/350x350" class="img-fluid" t-att-alt="product.display_name"/>
                                            </a>
                                            <a t-if="len(products) &gt; 2" href="#" t-att-data-product_product_id="product.id" class="o_comparelist_remove tp-icon-center-1 dr-d-icon shadow rounded-circle text-muted position-absolute top-0 end-0 m-2" title="Remove from comparison">
                                                <i class="fa fa-times"/>
                                            </a>
                                            <t t-call="theme_prime.product_label">
                                                <t t-set="label" t-value="product.dr_label_id"/>
                                            </t>
                                            <div class="card-body p-2">
                                                <div class="card-text">
                                                    <t t-set="combination_info" t-value="product._get_combination_info_variant()"/>
                                                    <h6 class="text-truncate mb-1">
                                                        <a class="tp-link-dark" t-att-title="combination_info['display_name']" t-att-href="product.website_url">
                                                            <t t-out="combination_info['display_name']"/>
                                                        </a>
                                                    </h6>
                                                    <div t-if="request.website._dr_has_b2b_access()">
                                                        <span class="o_comparison_price" t-if="combination_info['prevent_zero_price_sale']">
                                                            <strong t-field="website.prevent_zero_price_sale_text"/>
                                                        </span>
                                                        <span class="o_comparison_price" t-else="">
                                                            <h6 class="text-primary d-inline-block mb-0" t-out="combination_info['price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                            <small t-if="combination_info['compare_list_price'] and (combination_info['compare_list_price'] &gt; combination_info['price'])" t-attf-class="oe_default_price ms-1" style="text-decoration: line-through; white-space: nowrap;" t-out="combination_info['compare_list_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                            <small t-else="" t-attf-class="oe_default_price ms-1 {{'' if combination_info['has_discounted_price'] else 'd-none'}}" style="text-decoration: line-through; white-space: nowrap;" t-out="combination_info['list_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                            <t t-call="theme_prime.discount_percentage"/>
                                                            <small class="d-block text-muted" groups="website_sale.group_show_uom_price" t-if="combination_info['base_unit_price']">
                                                                <t t-call='website_sale.base_unit_price'/>
                                                            </small>
                                                        </span>
                                                    </div>
                                                    <t t-else="" t-call="theme_prime.tp_b2b_price_label"/>
                                                    <form action="/shop/cart/update" method="post" class="mt-2 o_add_cart_form_compare">
                                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                                        <input name="product_id"
                                                            type="hidden"
                                                            t-att-value="product.id"
                                                            t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])"/>
                                                        <a t-if="combination_info['prevent_zero_price_sale']" t-att-href="website.contact_us_button_url" class="btn btn-primary w-100 btn_cta">Contact Us</a>
                                                        <a t-else="" role="button" class="btn btn-primary-soft w-100 a-submit" href="#">
                                                            <i class="dri dri-cart"></i> Add to cart
                                                        </a>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="categories" t-as="category">
                                    <t t-foreach="categories[category]" t-as="attribute">
                                        <tr>
                                            <td class="align-middle">
                                                <h6 class="m-0" t-field="attribute.name"/>
                                            </td>
                                            <td t-foreach="categories[category][attribute]" t-as="product">
                                                <t t-foreach="categories[category][attribute][product]" t-as="ptav">
                                                    <span t-field="ptav.name"/>
                                                    <t t-if="not ptav_last">, </t>
                                                </t>
                                                <t t-if="not categories[category][attribute][product]">
                                                    -
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </section>
    </template>

    <!--
    ============================================================================
    Comparison popup
    ============================================================================
    -->
    <template id="comparison_popup" inherit_id="website_sale_comparison.product_product" name="Theme Prime: Comparison Popup">
        <xpath expr="//h6" position="attributes">
            <attribute name="t-if">request.website._dr_has_b2b_access()</attribute>
        </xpath>
        <xpath expr="//h6" position="after">
            <t t-else="">
                <h6 class="mb-1"><a t-att-href="product.website_url"><t t-esc="combination_info['display_name']" /></a></h6>
                <t t-call="theme_prime.tp_b2b_price_label"/>
            </t>
        </xpath>
    </template>

    <!--
    ============================================================================
    Options
    ============================================================================
    -->
    <template id="option_back_to_top" inherit_id="website.layout" active="True">
        <xpath expr="//div[@id='wrapwrap']" position="inside">
            <a href="#" class="tp-back-to-top rounded tp-icon-center-2 dr-p-icon o_not_editable shadow-tp d-none position-fixed" contenteditable="false" title="Scroll back to top">
                <i class="fa fa-angle-up"/>
            </a>
        </xpath>
    </template>

    <template id="option_icon_pack_1" inherit_id="website.layout" active="True">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-1.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_2" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-2.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_3" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-3.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_4" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-4.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_5" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-5.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_6" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-6.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_7" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-7.css"/>
        </xpath>
    </template>

    <template id="option_icon_pack_8" inherit_id="website.layout" active="False">
        <xpath expr="//head/link[last()]" position="after">
            <link rel="stylesheet" type="text/css" href="/theme_prime/static/lib/Droggol-Pack-v1.0/droggol-pack-8.css"/>
        </xpath>
    </template>

</odoo>
