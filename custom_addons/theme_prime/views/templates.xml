<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!--
    ============================================================================
    Dynamic Snippet Preview
    ============================================================================
    -->
    <template id="tp_preview_body" name="Preview: Body">
        <t t-call="web.frontend_layout">
            <t t-set="no_header" t-value="True"/>
            <t t-set="no_footer" t-value="True"/>
            <t t-set="no_livechat" t-value="True"/>
            <t t-if="mobile">
                <div class="d-flex text-center justify-content-between p-2 w-100 px-3">
                    <div class="small fw-bold" t-esc="datetime.datetime.now().strftime('%H:%M')"/>
                    <div class="small">
                        <i style="font-size:14px;" class="fa fa-wifi ms-1"></i>
                        <i style="font-size:14px;" class="fa fa-signal ms-1"></i>
                        <i style="font-size:14px;" class="fa fa-battery-full ms-1"/>
                    </div>
                </div>
                <div class="row g-0 mb-4 pb-2 border-bottom">
                    <div class="p-2 col-2 justify-content-center text-center align-items-center"><i style="font-size:18px;" class="fa text-muted fa-home"/></div>
                    <div class="p-2 col-7 text-truncate" style="background-color: #f7f8fb;border-radius: 40px;"><i class="fa text-muted fa-lock px-2 text-success"></i> <t t-esc="request.httprequest.url_root"/></div>
                    <div class="p-2 col-3 justify-content-between d-flex align-items-center"><i style="font-size:18px;" class="fa text-muted fa-plus-square-o ps-2"></i> <i style="font-size:18px;" class="fa fa-ellipsis-v pe-3"></i></div>
                </div>
            </t>
            <div id="tp_wrap"></div>
        </t>
    </template>

    <!--
    ============================================================================
    Quick View
    ============================================================================
    -->
    <template id="tp_product_quick_view" name="Product Quick View">
        <t t-set="combination" t-value="product._get_first_possible_combination() if not dr_variant_id else dr_variant_id.product_template_attribute_value_ids"/>
        <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1)"/>
        <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>
        <t t-set="rtl_class" t-value="request.env['res.lang']._get_data(code=request.env.lang).direction == 'rtl' and 'o_rtl' or ''"/>
        <section id="product_detail"
            t-att-data-product-tmpl-id="product.id"
            t-attf-class="oe_website_sale container #{'tp-combo-product' if product.type == 'combo' else ''} tp-product-quick-view-layout tp-noupdate-variant-change-url #{rtl_class}"
            t-att-data-view-track="view_track and '1' or '0'"
            t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])"
            >
            <div class="row">
                <div class="col-12 col-lg-6 col-xl-6 py-2 d-flex flex-column justify-content-center">
                    <div id="product_detail_main" t-att-data-image_width="website.product_page_image_width" t-att-data-image_layout="website.product_page_image_layout">
                        <t t-call="website_sale.shop_product_images"/>
                    </div>
                    <a t-att-href="product.website_url" class="btn btn-link btn-block mt-2">
                        <i class="fa fa-info-circle me-1"></i> Full Product Information
                    </a>
                </div>
                <div class="col-12 col-lg-6 col-xl-6 py-2" id="product_details">
                    <!-- Label -->
                    <t t-call="theme_prime.product_label">
                        <t t-set="label" t-value="product.dr_label_id"/>
                        <t t-set="style" t-valuef="1"/>
                        <t t-set="_classes" t-valuef="mb-2 d-inline-block position-relative top-0 start-0"/>
                    </t>
                    <h3 itemprop="name" t-field="product.name">Product Name</h3>
                    <div t-if="is_view_active('website_sale.product_comment') or product.get_recent_sold_qty().get(product.id)" class="d-flex align-items-center flex-wrap gap-3 mb-2">
                        <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                            <t t-set="rating_avg" t-value="product.rating_avg"/>
                            <t t-set="trans_text_plural">%s reviews</t>
                            <t t-set="trans_text_singular">%s review</t>
                            <t t-set="rating_count" t-value="(trans_text_plural if product.rating_count > 1 else trans_text_singular) % product.rating_count"/>
                        </t>
                    </div>
                    <div t-field="product.description_ecommerce"/>
                    <input id="wsale_user_email" type="hidden" t-att-value="user_email"/>
                    <form t-if="product._is_add_to_cart_possible()" action="/shop/cart/update" method="POST">
                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" t-nocache="The csrf token must always be up to date."/>
                        <div class="js_product js_main_product mb-2">
                            <div>
                                <t t-call="website_sale.product_price"/>
                                <small t-if="'base_unit_price' in combination_info"
                                        class="ms-1 text-muted o_base_unit_price_wrapper d-none" groups="website_sale.group_show_uom_price">
                                    <t t-call='website_sale.base_unit_price'/>
                                </small>
                            </div>
                            <hr name="after_price"/>
                            <t t-placeholder="select">
                                <input type="hidden" class="product_id" name="product_id" t-att-value="product_variant.id" />
                                <input type="hidden" class="product_template_id" name="product_template_id" t-att-value="product.id" />
                                <input t-if="product.public_categ_ids.ids" type="hidden" class="product_category_id" name="product_category_id" t-att-value="product.public_categ_ids.ids[0]" />
                                <t t-call="website_sale.variants">
                                    <t t-set="ul_class" t-valuef="flex-column" />
                                    <t t-set="parent_combination" t-value="None" />
                                    <t t-set="combination" t-value="combination_info['combination']"/>
                                </t>
                            </t>
                            <p t-if="True" class="css_not_available_msg alert alert-warning">This combination does not exist.</p>
                            <div id="o_wsale_cta_wrapper" class="d-flex flex-wrap align-items-center mt-3">
                                <div t-attf-class="css_quantity input-group {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} me-2 mb-2 align-middle" contenteditable="false">
                                    <a t-attf-href="#" class="btn btn-link js_add_cart_json" aria-label="Remove one" title="Remove one">
                                        <i class="fa fa-minus"/>
                                    </a>
                                    <input type="text" class="form-control quantity text-center" data-min="1" name="add_qty" t-att-value="1"/>
                                    <a t-attf-href="#" class="btn btn-link float_left js_add_cart_json" aria-label="Add one" title="Add one">
                                        <i class="fa fa-plus"/>
                                    </a>
                                </div>
                                <div id="add_to_cart_wrap" t-attf-class="{{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} align-items-center mb-2 me-auto">
                                    <a data-animation-selector=".product_detail_img" role="button" id="add_to_cart" class="btn btn-primary btn-lg js_check_product a-submit flex-grow-1" href="#">
                                        <i class="dri dri-cart me-1"/> Add to Cart
                                    </a>
                                </div>
                            </div>
                            <div id="product_option_block" class="d-flex flex-wrap w-100">
                                <t t-nocache="The wishlist depends on the user and must not be shared with other users. The product come from the controller.">
                                    <t t-set="product_variant" t-value="product_variant or product._create_first_product_variant()"/>
                                    <t t-set="in_wish" t-value="product_variant and product_variant._is_in_wishlist()"/>
                                    <button t-if="product_variant" type="button" role="button" class="btn btn-link px-0 pe-3 o_add_wishlist_dyn" t-att-disabled='in_wish or None' t-att-data-product-template-id="product.id" t-att-data-product-product-id="product_variant.id" data-action="o_wishlist" title="Add to wishlist"><i class="fa fa-heart-o me-2" role="img" aria-label="Add to wishlist"/>Add to wishlist</button>
                                </t>
                            </div>
                            <div class="availability_messages o_not_editable mt-2"/>
                        </div>
                    </form>
                    <p t-elif="not product.active" class="alert alert-warning">This product is no longer available.</p>
                    <p t-else="" class="alert alert-warning">This product has no valid combination.</p>
                    <div id="contact_us_wrapper" t-attf-class="{{'d-flex' if combination_info['prevent_zero_price_sale'] else 'd-none'}} oe_structure oe_structure_solo #{_div_classes}">
                        <section class="s_text_block" data-snippet="s_text_block" data-name="Text">
                            <div class="container">
                                <a t-att-href="website.contact_us_button_url"
                                    class="btn btn-primary btn_cta">Contact Us
                                </a>
                            </div>
                        </section>
                    </div>
                    <!-- Brand -->
                    <div class="d-flex align-items-center gap-3 mb-3" t-if="product.dr_brand_value_id">
                        <div t-if="product.dr_brand_value_id.dr_image" t-field="product.with_context(show_attribute=False).dr_brand_value_id.dr_image" t-options="{'widget': 'image', 'qweb_img_responsive': False, 'class': 'border rounded object-fit-contain', 'width': 80, 'height': 80}"/>
                        <div>
                            <h6>
                                <a class="tp-link-dark" t-attf-href="/shop?attribute_value=#{product.dr_brand_value_id.attribute_id.id}-#{product.dr_brand_value_id.id}">
                                    <span t-field="product.dr_brand_value_id.name"/>
                                </a>
                            </h6>
                            <p class="mb-0" t-field="product.dr_brand_value_id.dr_brand_description"/>
                        </div>
                    </div>
                    <!-- Extra fields -->
                    <t t-call="theme_prime.product_extra_fields" />
                </div>
            </div>
        </section>
    </template>

    <!--
    ============================================================================
    Right Panel only (TO-DO) use existing quick view template
    ============================================================================
    -->
    <template id="tp_product_right_panel">
        <t t-set="combination" t-value="product._get_first_possible_combination()"/>
        <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1)"/>
        <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>
        <t t-set="rtl_class" t-value="request.env['res.lang']._get_data(code=request.env.lang).direction == 'rtl' and 'o_rtl' or ''"/>

        <section t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])" t-att-data-product-tmpl-id="product.id" t-attf-class="#{'auto-add-product' if auto_add_product else '' } #{'tp-combo-product' if product.type == 'combo' else ''} container px-0 o_not_editable oe_website_sale tp-product-variant-selector-layout tp-noupdate-variant-change-url #{rtl_class}">
            <div id="product_detail_main" class="d-none" t-att-data-image_width="website.product_page_image_width" t-att-data-image_layout="website.product_page_image_layout">
                <t t-call="website_sale.shop_product_images"/>
            </div>
            <div id="product_details">
                <h2 class="tp-hover-color-primary m-0 mb-2 mt-2 text-truncate">
                    <a t-att-href="product.website_url">
                        <span t-field="product.name"/>
                    </a>
                </h2>
                <t t-if="is_view_active('website_sale.product_comment')">
                    <t t-call="portal_rating.rating_widget_stars_static">
                        <t t-set="rating_avg" t-value="product.rating_avg"/>
                        <t t-set="trans_text_plural">%s reviews</t>
                        <t t-set="trans_text_singular">%s review</t>
                        <t t-set="rating_count" t-value="(trans_text_plural if product.rating_count > 1 else trans_text_singular) % product.rating_count"/>
                    </t>
                </t>
                <p t-field="product.description_ecommerce" class="mt-3 text-muted" placeholder="A short description that will also appear on documents." />

                <t if="product.type == 'combo' and product.combo_ids">
                    <div class="row g-2">
                        <t t-foreach="product.sudo().combo_ids" t-as="_combo">
                            <div class="col-4" t-if="_combo.combo_item_ids">
                                <div class="card h-100 d-flex">
                                    <div class="d-flex align-items-center" style="flex-grow:1;">
                                        <div style="width: 67%;padding-right: 0.12rem;">
                                            <img style="max-height: 376px; object-fit:contain;" loading="lazy" t-attf-src="/web/image/product.product/#{_combo.combo_item_ids[0].product_id.id}/image_512" class="d-block w-100"/>
                                        </div>
                                        <div style="width: 33%;padding-left: 0.12rem;">
                                            <img t-if="len(_combo.combo_item_ids) > 1" style="max-height: 188px; object-fit:contain;" loading="lazy" t-attf-src="/web/image/product.product/#{_combo.combo_item_ids[1].product_id.id}/image_512" class="d-block w-100 pb-1"/>
                                            <img t-if="len(_combo.combo_item_ids) > 2"  style="max-height: 188px; object-fit:contain;" loading="lazy" t-attf-src="/web/image/product.product/#{_combo.combo_item_ids[2].product_id.id}/image_512" class="d-block w-100"/>
                                        </div>
                                    </div>
                                    <div class="card-body py-2 px-1" style="flex-grow:0;">
                                        <h6 class="text-center mb-0" t-esc="_combo.name"/>
                                        <p class="text-center small mb-0 text-muted">
                                            <t t-out="len(_combo.combo_item_ids)"></t> Options
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </t>
                <input id="wsale_user_email" type="hidden" t-att-value="user_email"/>
                <form t-if="product._is_add_to_cart_possible()" class="d_cart_update_form" action="/shop/cart/update" method="POST">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" />
                    <div class="js_product js_main_product">
                        <div>
                            <t t-call="website_sale.product_price"/>
                            <small t-if="'base_unit_price' in combination_info" class="o_base_unit_price_wrapper d-none" groups="website_sale.group_show_uom_price">
                                <t t-call='website_sale.base_unit_price'/>
                            </small>
                        </div>
                        <hr class="my-3"/>
                        <t t-placeholder="select">
                            <input type="hidden" class="product_id" name="product_id" t-att-value="product_variant.id" />
                            <input type="hidden" class="product_template_id" name="product_template_id" t-att-value="product.id" />
                            <input t-if="product.public_categ_ids.ids" type="hidden" class="product_category_id" name="product_category_id" t-att-value="product.public_categ_ids.ids[0]" />
                            <input t-if="product.product_tag_ids.ids" type="hidden" class="product_tag_id" name="product_tag_id" t-att-value="product.product_tag_ids.ids[0]" />
                            <t t-if="combination" t-call="website_sale.variants">
                                <t t-set="ul_class" t-valuef="flex-column" />
                                <t t-set="parent_combination" t-value="None" />
                            </t>
                            <t t-else="">
                                <ul class="d-none js_add_cart_variants mb-0" t-att-data-attribute_exclusions="{'exclusions: []'}"/>
                            </t>
                        </t>
                        <p t-if="True" class="css_not_available_msg alert alert-warning mt-2">This combination does not exist.</p>
                        <div class="availability_messages o_not_editable my-3"/>
                        <div id="o_wsale_cta_wrapper" class="d-flex flex-column align-items-center mt-3">
                            <div t-if="is_view_active('website_sale.product_quantity')" t-attf-class="css_quantity input-group {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} me-2 mb-2 align-middle #{'input-group-lg' if ctaSizeBig else ''}" contenteditable="false">
                                <a t-attf-href="#" class="btn btn-link js_add_cart_json" aria-label="Remove one" title="Remove one">
                                    <i class="fa fa-minus"></i>
                                </a>
                                <input type="text" class="form-control quantity text-center" data-min="1" name="add_qty" t-att-value="add_qty or 1"/>
                                <a t-attf-href="#" class="btn btn-link float_left js_add_cart_json" aria-label="Add one" title="Add one">
                                    <i class="fa fa-plus"></i>
                                </a>
                            </div>
                            <div id="contact_us_wrapper" t-if="combination_info['prevent_zero_price_sale']">
                                <a t-att-href="website.contact_us_button_url" class="btn btn-primary btn-lg btn_cta mt-2">Contact Us</a>
                            </div>
                            <div id="add_to_cart_wrap" class="w-100" t-else="">
                                <a role="button" id="add_to_cart" class="btn w-100 btn-primary btn-lg js_check_product a-submit mt-2" href="#">
                                    <i class="dri dri-cart me-1"/> Add to Cart
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
                <p t-elif="not product.active" class="alert alert-warning">This product is no longer available.</p>
                <p t-else="" class="alert alert-warning">This product has no valid combination.</p>
            </div>
        </section>
    </template>

    <!--
    ============================================================================
    Product Variant Selector Dialog
    ============================================================================
    -->
    <template id="product_variant_selector_dialog" name="Product Variant Selector Dialog">
        <t t-set="combination" t-value="product._get_first_possible_combination() if not dr_variant_id else dr_variant_id.product_template_attribute_value_ids"/>
        <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1)"/>
        <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>
        <t t-set="rtl_class" t-value="request.env['res.lang']._get_data(code=request.env.lang).direction == 'rtl' and 'o_rtl' or ''"/>
        <section id="product_detail"
            t-att-data-product-tmpl-id="product.id"
            t-attf-class="oe_website_sale container px-0 tp-product-quick-view-layout tp-noupdate-variant-change-url #{rtl_class} #{'auto-add-product' if auto_add_product else '' } #{'tp-combo-product' if product.type == 'combo' else ''} #{'tp-product-out-of-stock' if not product.allow_out_of_stock_order and product.is_storable and product.dr_free_qty &lt;= 0 else ''}"
            t-att-data-view-track="view_track and '1' or '0'"
            t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])"
            >
            <div id="product_detail_main" t-att-data-image_width="website.product_page_image_width" t-att-data-image_layout="website.product_page_image_layout">
                <t t-call="website_sale.shop_product_images"/>
            </div>
            <hr/>
            <div id="product_details">
                <h4 itemprop="name" t-field="product.name">Product Name</h4>
                <input id="wsale_user_email" type="hidden" t-att-value="user_email"/>
                <form t-if="product._is_add_to_cart_possible()" action="/shop/cart/update" method="POST">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" t-nocache="The csrf token must always be up to date."/>
                    <div class="js_product js_main_product">
                        <div>
                            <t t-call="website_sale.product_price"/>
                            <small t-if="'base_unit_price' in combination_info"
                                    class="ms-1 text-muted o_base_unit_price_wrapper d-none" groups="website_sale.group_show_uom_price">
                                <t t-call='website_sale.base_unit_price'/>
                            </small>
                        </div>
                        <hr name="after_price"/>
                        <t t-placeholder="select">
                            <input type="hidden" class="product_id" name="product_id" t-att-value="product_variant.id" />
                            <input type="hidden" class="product_template_id" name="product_template_id" t-att-value="product.id" />
                            <input t-if="product.public_categ_ids.ids" type="hidden" class="product_category_id" name="product_category_id" t-att-value="product.public_categ_ids.ids[0]" />
                            <t t-call="website_sale.variants">
                                <t t-set="ul_class" t-valuef="flex-column" />
                                <t t-set="parent_combination" t-value="None" />
                                <t t-set="combination" t-value="combination_info['combination']"/>
                            </t>
                        </t>
                        <p t-if="True" class="css_not_available_msg alert alert-warning">This combination does not exist.</p>
                        <div class="availability_messages o_not_editable mt-2"/>
                        <div id="o_wsale_cta_wrapper" class="d-flex flex-column align-items-center mt-3">
                            <div t-attf-class="css_quantity input-group {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} me-2 mb-2 align-middle" contenteditable="false">
                                <a t-attf-href="#" class="btn btn-link js_add_cart_json" aria-label="Remove one" title="Remove one">
                                    <i class="fa fa-minus"></i>
                                </a>
                                <input type="text" class="form-control quantity text-center" data-min="1" name="add_qty" t-att-value="1"/>
                                <a t-attf-href="#" class="btn btn-link float_left js_add_cart_json" aria-label="Add one" title="Add one">
                                    <i class="fa fa-plus"></i>
                                </a>
                            </div>
                            <div id="add_to_cart_wrap" t-attf-class="{{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} align-items-center w-100">
                                <a data-animation-selector=".product_detail_img" role="button" id="add_to_cart" class="btn btn-primary btn-lg js_check_product py-3 a-submit flex-grow-1 rounded-0" href="#">
                                    <i class="dri dri-cart me-1"/> Add to Cart
                                </a>
                                <a t-if="is_view_active('website_sale.product_buy_now')" role="button" class="btn btn-primary-soft py-3 btn-lg o_we_buy_now rounded-0" href="#">
                                    <i class="dri dri-bolt me-1"/> Buy Now
                                </a>
                            </div>
                        </div>
                        <div id="contact_us_wrapper"
                                t-attf-class="{{'my-3 justify-content-center' if combination_info['prevent_zero_price_sale'] else 'd-none'}} oe_structure oe_structure_solo #{_div_classes}">
                            <section class="s_text_block" data-snippet="s_text_block" data-name="Text">
                                <div class="container">
                                    <a t-att-href="website.contact_us_button_url"
                                        class="btn btn-primary btn_cta w-100">Contact Us
                                    </a>
                                </div>
                            </section>
                        </div>
                    </div>
                </form>
                <p t-elif="not product.active" class="alert alert-warning">This product is no longer available.</p>
                <p t-else="" class="alert alert-warning">This product has no valid combination.</p>
            </div>
        </section>
    </template>

</odoo>
