<?xml version="1.0" ?>
<templates>
    <div t-name="theme_prime.cart_confirmation_dialog">
        <div class="o_dialog" t-att-id="id" t-att-class="{ o_inactive_modal: !data.isActive }">
            <div role="dialog" class="modal d-block"
                tabindex="-1"
                t-att-class="{ o_technical_modal: props.technical, o_modal_full: isFullscreen, o_inactive_modal: !data.isActive }"
                t-ref="modalRef"
                >
                <div class="modal-dialog modal-dialog-centered tp-cart-confirmation-dialog" t-attf-class="modal-{{props.size}}">
                    <div class="modal-content" t-att-class="props.contentClass" t-att-style="contentStyle">
                        <main class="modal-body" t-attf-class="{{ props.bodyClass }} {{ !props.withBodyPadding ? 'p-0': '' }}">
                            <button type="button" class="btn-close position-absolute top-0 end-0 p-3 shadow-none" aria-label="Close" tabindex="-1" t-on-click="dismiss"></button>
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-md-6 text-center border-end py-3 dr-confim-col">
                                        <span class="dr-confirm-product-image d-inline-block">
                                            <img class="img img-fluid w-50 rounded-circle" t-attf-src="/web/image/product.product/#{props.info.product_id}/image_1024"/>
                                            <span class="fa fa-check fa-2x text-primary"></span>
                                        </span>
                                        <h5 class="text-primary mt-4 mb-1">
                                            <t t-esc="props.info.product_name"/>
                                        </h5>
                                        <div class="lead text-black">Added successfully to cart.</div>
                                    </div>
                                    <div class="col-md-6 text-center py-3 d-flex d-flex flex-column justify-content-center">
                                        <p class="text-muted mb-2"> There are <b t-out="props.info.cart_quantity"/> items in your cart.</p>
                                        <h5> Cart Total: <span class="text-primary"> <t t-out="markup(props.info.notification_info.order_amount_html)"/> </span> </h5>
                                        <div class="mt-3">
                                            <button class="btn btn-primary mb-2 me-2" t-on-click="dismiss"> Continue Shopping </button>
                                            <a href="/shop/cart" class="btn btn-outline-primary d_view_cart mb-2"> View Cart </a>
                                        </div>
                                    </div>
                                    <div class="col-md-12 text-start pt-10 border-top mb-3 d-none d-md-block" t-if="props.info.accessory_product_ids.length">
                                        <div class="mt-4">
                                            <h4 class="s_d_full_title">
                                                <span class="s_d_title_text">
                                                    <p class="mb-0 d-inline-block o_default_snippet_text">You may also like</p>
                                                </span>
                                            </h4>
                                        </div>
                                        <div t-ref="content" class="s_d_product_small_block tp-droggol-18-builder-snippet in_confirm_dialog" data-tp-snippet-id="s_d_product_small_block" data-empty-message="No products are selected." data-sub-message="Please select products from snippet option." t-attf-data-selection-info='{"selectionType":"manual","recordsIDs": #{JSON.stringify(props.info.accessory_product_ids)}}'>
                                            <div class="s_d_product_small_block_body"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </main>
                    </div>
                </div>
            </div>
        </div>
    </div>
</templates>
