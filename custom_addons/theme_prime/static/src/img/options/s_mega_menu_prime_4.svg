<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" zoomAndPan="magnify" viewBox="0 0 61.5 44.999999" height="60" preserveAspectRatio="xMidYMid meet" version="1.0"><defs><clipPath id="01313a5ae0"><path d="M 0 0.179688 L 19 0.179688 L 19 19 L 0 19 Z M 0 0.179688 " clip-rule="nonzero"/></clipPath><clipPath id="ba8307cc84"><path d="M -20.503906 0.5 L 0.0742188 -20.082031 L 18.058594 -2.101562 L -2.523438 18.480469 Z M -20.503906 0.5 " clip-rule="nonzero"/></clipPath><clipPath id="d21e447286"><path d="M -10.230469 -9.777344 L 18.027344 -2.070312 L -2.523438 18.480469 Z M -10.230469 -9.777344 " clip-rule="nonzero"/></clipPath><clipPath id="7695142205"><path d="M 1.320312 1.648438 L 7.617188 1.648438 L 7.617188 7.640625 L 1.320312 7.640625 Z M 1.320312 1.648438 " clip-rule="nonzero"/></clipPath><clipPath id="d81f5b99dd"><path d="M 4.464844 1.648438 L 5.207031 3.9375 L 7.605469 3.9375 L 5.664062 5.351562 L 6.40625 7.640625 L 4.464844 6.226562 L 2.519531 7.640625 L 3.261719 5.351562 L 1.320312 3.9375 L 3.722656 3.9375 Z M 4.464844 1.648438 " clip-rule="nonzero"/></clipPath><clipPath id="3101d2240b"><path d="M 6.289062 15.792969 L 21.167969 15.792969 L 21.167969 28.441406 L 6.289062 28.441406 Z M 6.289062 15.792969 " clip-rule="nonzero"/></clipPath><clipPath id="e5cbb48cdf"><path d="M 6.289062 20 L 21.167969 20 L 21.167969 28.441406 L 6.289062 28.441406 Z M 6.289062 20 " clip-rule="nonzero"/></clipPath></defs><g clip-path="url(#01313a5ae0)"><g clip-path="url(#ba8307cc84)"><g clip-path="url(#d21e447286)"><path fill="#ff914d" d="M -20.503906 0.5 L 0.0742188 -20.082031 L 18.058594 -2.101562 L -2.523438 18.480469 Z M -20.503906 0.5 " fill-opacity="1" fill-rule="nonzero"/></g></g></g><g clip-path="url(#7695142205)"><g clip-path="url(#d81f5b99dd)"><path fill="#ffffff" d="M 1.320312 1.648438 L 7.617188 1.648438 L 7.617188 7.640625 L 1.320312 7.640625 Z M 1.320312 1.648438 " fill-opacity="1" fill-rule="nonzero"/></g></g><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 25.583438, 15.980216)" fill="none" stroke-linejoin="miter" d="M 0.0000809376 0.998005 L 12.22411 0.997467 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 25.654631, 19.766138)" fill="none" stroke-linejoin="miter" d="M 0.00217642 0.498125 L 16.61007 0.498125 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 25.654631, 21.474938)" fill="none" stroke-linejoin="miter" d="M 0.00217642 0.500998 L 16.61007 0.500998 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 25.589799, 23.18374)" fill="none" stroke-linejoin="miter" d="M 0.0000608201 0.498617 L 16.607954 0.498617 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><g clip-path="url(#3101d2240b)"><path fill="#ffffff" d="M 7.769531 15.792969 L 19.625 15.792969 C 19.824219 15.792969 20.011719 15.832031 20.195312 15.90625 C 20.375 15.984375 20.535156 16.089844 20.675781 16.230469 C 20.8125 16.367188 20.921875 16.527344 20.996094 16.710938 C 21.070312 16.890625 21.109375 17.082031 21.109375 17.277344 L 21.109375 27.15625 C 21.109375 27.355469 21.070312 27.542969 20.996094 27.726562 C 20.921875 27.90625 20.8125 28.066406 20.675781 28.207031 C 20.535156 28.34375 20.375 28.453125 20.195312 28.527344 C 20.011719 28.601562 19.824219 28.640625 19.625 28.640625 L 7.769531 28.640625 C 7.574219 28.640625 7.382812 28.601562 7.203125 28.527344 C 7.019531 28.453125 6.859375 28.34375 6.722656 28.207031 C 6.582031 28.066406 6.476562 27.90625 6.402344 27.726562 C 6.324219 27.542969 6.289062 27.355469 6.289062 27.15625 L 6.289062 17.277344 C 6.289062 17.082031 6.324219 16.890625 6.402344 16.710938 C 6.476562 16.527344 6.582031 16.367188 6.722656 16.230469 C 6.859375 16.089844 7.019531 15.984375 7.203125 15.90625 C 7.382812 15.832031 7.574219 15.792969 7.769531 15.792969 Z M 7.769531 15.792969 " fill-opacity="1" fill-rule="nonzero"/></g><g clip-path="url(#e5cbb48cdf)"><path fill="#00a09d" d="M 20.964844 23.84375 L 17.503906 20.386719 C 17.410156 20.289062 17.292969 20.242188 17.15625 20.242188 C 17.019531 20.242188 16.902344 20.289062 16.808594 20.386719 L 14.6875 22.507812 L 13.304688 21.125 C 13.210938 21.03125 13.09375 20.980469 12.957031 20.980469 C 12.820312 20.980469 12.703125 21.03125 12.609375 21.125 L 9.542969 24.191406 L 6.78125 24.191406 C 6.644531 24.191406 6.527344 24.242188 6.433594 24.339844 C 6.335938 24.433594 6.289062 24.550781 6.289062 24.6875 L 6.289062 27.15625 C 6.289062 27.355469 6.324219 27.542969 6.402344 27.726562 C 6.476562 27.90625 6.582031 28.066406 6.722656 28.207031 C 6.859375 28.34375 7.019531 28.453125 7.203125 28.527344 C 7.382812 28.601562 7.574219 28.640625 7.769531 28.640625 L 19.625 28.640625 C 19.824219 28.640625 20.011719 28.601562 20.195312 28.527344 C 20.375 28.453125 20.535156 28.34375 20.675781 28.207031 C 20.8125 28.066406 20.921875 27.90625 20.996094 27.726562 C 21.070312 27.542969 21.109375 27.355469 21.109375 27.15625 L 21.109375 24.191406 C 21.109375 24.058594 21.058594 23.941406 20.964844 23.84375 Z M 20.964844 23.84375 " fill-opacity="1" fill-rule="nonzero"/></g><path fill="#ffbd59" d="M 11.226562 19.253906 C 11.226562 19.449219 11.191406 19.636719 11.117188 19.820312 C 11.039062 20 10.933594 20.160156 10.792969 20.300781 C 10.65625 20.441406 10.496094 20.546875 10.3125 20.621094 C 10.132812 20.699219 9.941406 20.734375 9.746094 20.734375 C 9.550781 20.734375 9.359375 20.699219 9.179688 20.621094 C 8.996094 20.546875 8.835938 20.441406 8.699219 20.300781 C 8.558594 20.160156 8.453125 20 8.375 19.820312 C 8.300781 19.636719 8.265625 19.449219 8.265625 19.253906 C 8.265625 19.054688 8.300781 18.867188 8.375 18.6875 C 8.453125 18.503906 8.558594 18.34375 8.699219 18.203125 C 8.835938 18.066406 8.996094 17.957031 9.179688 17.882812 C 9.359375 17.808594 9.550781 17.769531 9.746094 17.769531 C 9.941406 17.769531 10.132812 17.808594 10.3125 17.882812 C 10.496094 17.957031 10.65625 18.066406 10.792969 18.203125 C 10.933594 18.34375 11.039062 18.503906 11.117188 18.6875 C 11.191406 18.867188 11.226562 19.054688 11.226562 19.253906 Z M 11.226562 19.253906 " fill-opacity="1" fill-rule="nonzero"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 25.719462, 24.671643)" fill="none" stroke-linejoin="miter" d="M -0.000956415 0.499126 L 16.606937 0.499126 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 25.719462, 26.380444)" fill="none" stroke-linejoin="miter" d="M -0.000956415 0.501999 L 16.606937 0.501999 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 25.654631, 28.089247)" fill="none" stroke-linejoin="miter" d="M 0.00217642 0.499616 L 16.61007 0.499616 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 42.22403, 16.352192)" fill="none" stroke-linejoin="miter" d="M 0.000090104 1.00207 L 12.224119 1.001532 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 42.295216, 20.138114)" fill="none" stroke-linejoin="miter" d="M 0.00222961 0.50219 L 16.610123 0.50219 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 42.295216, 21.846914)" fill="none" stroke-linejoin="miter" d="M 0.00222961 0.499812 L 16.610123 0.499812 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 42.230387, 23.555716)" fill="none" stroke-linejoin="miter" d="M 0.000110491 0.497432 L 16.608004 0.497432 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 42.360051, 25.043619)" fill="none" stroke-linejoin="miter" d="M -0.000908344 0.49794 L 16.606985 0.49794 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 42.360051, 26.752419)" fill="none" stroke-linejoin="miter" d="M -0.000908344 0.500813 L 16.606985 0.500813 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 42.295216, 28.461223)" fill="none" stroke-linejoin="miter" d="M 0.00222961 0.498431 L 16.610123 0.498431 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/></svg>