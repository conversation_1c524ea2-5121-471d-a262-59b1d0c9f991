<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" zoomAndPan="magnify" viewBox="0 0 61.5 44.999999" height="60" preserveAspectRatio="xMidYMid meet" version="1.0"><defs><clipPath id="2e4ae336aa"><path d="M 0 0.179688 L 19 0.179688 L 19 19 L 0 19 Z M 0 0.179688 " clip-rule="nonzero"/></clipPath><clipPath id="525aa09c3e"><path d="M -20.503906 0.5 L 0.0742188 -20.082031 L 18.058594 -2.101562 L -2.523438 18.480469 Z M -20.503906 0.5 " clip-rule="nonzero"/></clipPath><clipPath id="d5f331291b"><path d="M -10.230469 -9.777344 L 18.027344 -2.070312 L -2.523438 18.480469 Z M -10.230469 -9.777344 " clip-rule="nonzero"/></clipPath><clipPath id="fe09835c7b"><path d="M 1.320312 1.648438 L 7.617188 1.648438 L 7.617188 7.640625 L 1.320312 7.640625 Z M 1.320312 1.648438 " clip-rule="nonzero"/></clipPath><clipPath id="c26d90deec"><path d="M 4.464844 1.648438 L 5.207031 3.9375 L 7.605469 3.9375 L 5.664062 5.351562 L 6.40625 7.640625 L 4.464844 6.226562 L 2.519531 7.640625 L 3.261719 5.351562 L 1.320312 3.9375 L 3.722656 3.9375 Z M 4.464844 1.648438 " clip-rule="nonzero"/></clipPath></defs><g clip-path="url(#2e4ae336aa)"><g clip-path="url(#525aa09c3e)"><g clip-path="url(#d5f331291b)"><path fill="#ff914d" d="M -20.503906 0.5 L 0.0742188 -20.082031 L 18.058594 -2.101562 L -2.523438 18.480469 Z M -20.503906 0.5 " fill-opacity="1" fill-rule="nonzero"/></g></g></g><g clip-path="url(#fe09835c7b)"><g clip-path="url(#c26d90deec)"><path fill="#ffffff" d="M 1.320312 1.648438 L 7.617188 1.648438 L 7.617188 7.640625 L 1.320312 7.640625 Z M 1.320312 1.648438 " fill-opacity="1" fill-rule="nonzero"/></g></g><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 5.851956, 12.43498)" fill="none" stroke-linejoin="miter" d="M 0.00141669 1.001064 L 14.661851 1.000346 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.936075, 15.195629)" fill="none" stroke-linejoin="miter" d="M 0.00191526 0.498389 L 19.922987 0.498389 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.936075, 16.622156)" fill="none" stroke-linejoin="miter" d="M 0.00191526 0.497386 L 19.922987 0.497386 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.936075, 18.044368)" fill="none" stroke-linejoin="miter" d="M 0.00191526 0.502184 L 19.922987 0.502184 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.858314, 19.633736)" fill="none" stroke-linejoin="miter" d="M 0.00142555 0.497573 L 19.922497 0.497573 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 23.049867, 12.325436)" fill="none" stroke-linejoin="miter" d="M -0.00207778 1.001262 L 14.658356 1.000544 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.133986, 15.086071)" fill="none" stroke-linejoin="miter" d="M -0.00157771 0.498635 L 19.919494 0.498635 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.133986, 16.512612)" fill="none" stroke-linejoin="miter" d="M -0.00157771 0.497613 L 19.919494 0.497613 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.133986, 17.934809)" fill="none" stroke-linejoin="miter" d="M -0.00157771 0.502431 L 19.919494 0.502431 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.056227, 19.524192)" fill="none" stroke-linejoin="miter" d="M -0.00206998 0.4978 L 19.919001 0.4978 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 40.247781, 12.697412)" fill="none" stroke-linejoin="miter" d="M -0.000313523 1.000091 L 14.660121 0.999373 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.331901, 15.458046)" fill="none" stroke-linejoin="miter" d="M 0.000175516 0.49745 L 19.921247 0.49745 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.331901, 16.884588)" fill="none" stroke-linejoin="miter" d="M 0.000175516 0.501678 L 19.921247 0.501678 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.331901, 18.306785)" fill="none" stroke-linejoin="miter" d="M 0.000175516 0.501246 L 19.921247 0.501246 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.254137, 19.896168)" fill="none" stroke-linejoin="miter" d="M -0.000310038 0.501865 L 19.920761 0.501865 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 5.851956, 24.562633)" fill="none" stroke-linejoin="miter" d="M 0.00144717 0.997498 L 14.661836 1.002031 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.936075, 27.32326)" fill="none" stroke-linejoin="miter" d="M 0.00191526 0.500102 L 19.922987 0.500102 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.936075, 28.749817)" fill="none" stroke-linejoin="miter" d="M 0.00191526 0.499061 L 19.922987 0.499061 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.936075, 30.172028)" fill="none" stroke-linejoin="miter" d="M 0.00191526 0.498608 L 19.922987 0.498608 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 5.858314, 31.761382)" fill="none" stroke-linejoin="miter" d="M 0.00142555 0.499267 L 19.922497 0.499267 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 23.049867, 24.453075)" fill="none" stroke-linejoin="miter" d="M -0.00204747 0.997715 L 14.658342 1.002248 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.133986, 27.213717)" fill="none" stroke-linejoin="miter" d="M -0.00157771 0.50033 L 19.919494 0.50033 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.133986, 28.640258)" fill="none" stroke-linejoin="miter" d="M -0.00157771 0.499308 L 19.919494 0.499308 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.133986, 30.06247)" fill="none" stroke-linejoin="miter" d="M -0.00157771 0.498855 L 19.919494 0.498855 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 23.056227, 31.651838)" fill="none" stroke-linejoin="miter" d="M -0.00206998 0.499494 L 19.919001 0.499494 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743924, -0.00635833, 0.00635833, 0.743924, 40.247781, 24.825051)" fill="none" stroke-linejoin="miter" d="M -0.000328088 1.001795 L 14.660106 1.001077 " stroke="#00a09d" stroke-width="2" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.331901, 27.585692)" fill="none" stroke-linejoin="miter" d="M 0.000175516 0.499144 L 19.921247 0.499144 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.331901, 29.012234)" fill="none" stroke-linejoin="miter" d="M 0.000175516 0.498122 L 19.921247 0.498122 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.331901, 30.434445)" fill="none" stroke-linejoin="miter" d="M 0.000175516 0.497669 L 19.921247 0.497669 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/><path stroke-linecap="butt" transform="matrix(0.743952, 0, 0, 0.743952, 40.254137, 32.023814)" fill="none" stroke-linejoin="miter" d="M -0.000310038 0.498308 L 19.920761 0.498308 " stroke="#d8d8d8" stroke-width="1" stroke-opacity="1" stroke-miterlimit="4"/></svg>