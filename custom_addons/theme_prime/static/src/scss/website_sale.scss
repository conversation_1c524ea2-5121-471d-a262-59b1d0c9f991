//------------------------------------------------------------------------------
// Discount Percentage
//------------------------------------------------------------------------------
.tp-discount-percentage {
    color: #ff905a;
}

//------------------------------------------------------------------------------
// Drift Zoom
//------------------------------------------------------------------------------
.tp-rtl-zoom-pane.drift-zoom-pane {
    direction: rtl;
}
.tp-zoom-pane.drift-zoom-pane, .tp-rtl-zoom-pane.drift-zoom-pane {
    z-index: $zindex-modal + 1;
}

//------------------------------------------------------------------------------
// Attributes Design
//------------------------------------------------------------------------------
// General
.js_add_cart_variants {
    .variant_attribute {
        padding-bottom: 1rem;
        &:last-child {
            padding-bottom: 0px;
        }
        .attribute_name {
            text-transform: capitalize;
            &::after {
                content: none;
            }
        }
        .list-inline-item {
            margin-right: 0.6rem !important;
        }
    }
}

// Color variant
.css_attribute_color {
    cursor: pointer;
    input {
        cursor: pointer;
    }
    &::before {
        box-shadow: none;
        border-width: 3px;
    }
}

.radio_input_value .badge {
    padding: 0.25em 0.8em;
}

.dr_search_autocomplete {

    margin-top: 10px;
    @include media-breakpoint-up(md) {
        &.dr_search_wide {
            width: 130% !important;
            left: -15%;
        }
    }

    .text-muted, .btn-link {
        color: $text-muted !important;
    }
    .dropdown-item {
        .o_search_result_item {
            border-radius: 4px;
        }
        button.pe-0 {
            padding-left: 0px !important;
        }
        &:hover, &:focus {
            .o_search_result_item {
                background-color: #F1F1F1;
            }
        }
    }
}

//------------------------------------------------------------------------------
// Shop Page
//------------------------------------------------------------------------------
.tp-category-pills-container {
    &.dr_nowrap {
        white-space: nowrap;
        scroll-snap-type: x mandatory;
        @include tp-scroll(0px, 5px, #EDEDED, o-color('primary'));
        cursor: grab;
        &::-webkit-scrollbar-thumb {
            border: 2px solid #EDEDED;
            border-radius: 8px;
        }
        &:hover {
            &::-webkit-scrollbar-thumb {
                border: 0px solid #EDEDED;
            }
        }
        .tp-category-pill {
            scroll-snap-align: start;
        }
    }
    &.style-1 {
        .tp-category-pill {
            height: 80px;
            width: 90px;
            transition: 0.2s;
            .tp-category-img {
                height: 54px;
                width: 54px;
            }
            &.active, &:active, &:hover {
                h6 {
                    color: o-color('primary');
                }
            }
        }
    }
    &.style-2 {
        .tp-category-pill {
            padding: 0.4rem 1.2rem;
            font-weight: 500;
            color: $body-color;
            border: 1px solid $border-color;
            transition: 0.2s;
            background-color: white;
            &.active, &:active, &:hover {
                border-color: o-color('primary');
                color: o-color('primary');
            }
        }
    }
    &.style-3 {
        .tp-category-pill {
            transition: 0.2s;
            border: 1px solid $border-color;
            border-radius: 0.5rem;
            color: $body-color;
            background-color: white;
            .tp-category-img {
                height: 34px;
                width: 34px;
            }
            &.active, &:active, &:hover {
                border-color: o-color('primary');
                h6 {
                    color: o-color('primary');
                }
            }
        }
    }
    &.style-4 {
        .tp-category-pill {
            border: 1px solid $border-color;
            transition: 0.2s;
            background-color: white;
            h6 {
                color: $body-color;
            }
            .tp-category-img {
                height: 28px;
                width: 28px;
            }
            &.active, &:active, &:hover {
                border-color: o-color('primary');
                h6 {
                    color: o-color('primary');
                }
            }
        }
    }
    &.style-5 {
        .tp-category-pill {
            transition: 0.2s;
            border: 1px solid $border-color;
            border-radius: 0.6rem;
            padding: 0.3rem;
            background-color: white;
            .tp-category-img {
                height: 38px;
                width: 38px;
            }
            &.active, &:active, &:hover {
                border-color: o-color('primary');
            }
        }
    }
}

.tp-selected-attributes {
    .tp-attribute {
        letter-spacing: .3px;
        color: $headings-color;
        font-weight: $headings-font-weight;
        font-size: 13px;
        &:hover {
            opacity: 0.7;
        }
        .tp-attribute-color {
            height: 10px;
            width: 10px;
            border-radius: 3rem;
        }
    }
}

#wrap.js_sale .oe_website_sale {
    @include media-breakpoint-up(lg) {
        .tp-shop-topbar.bordered {
            border-bottom: 1px solid $border-color;
        }
        .tp-shop-row.bordered {
            #products_grid_before {
                border-right: 1px solid $border-color;
                &.order-1 {
                    border-right-width: 0px;
                    border-left: 1px solid $border-color;
                }
            }
        }
    }
    .tp-rounded-border-radius {
        border-radius: 6px;
    }

    // TODO: JAT: Remove once odoo fix it
    .o_categories_recursive_button:after {
        background-image: escape-svg($accordion-button-icon);
        display: inline-block;
        height: 16px;
        width: 16px;
        background-size: 16px;
    }
    .o_categories_recursive_button:not(.collapsed):after {
        transform: rotate(180deg);
    }
}

.tp-filter-attribute {
    .tp-filter-attribute-title.collapsible {
        &:hover {
            color: o-color('primary');
            h6 {
                color: o-color('primary');
            }
        }
        h6 {
            transition: all 0.2s ease;
        }
        .tp-collapse-indicator {
            line-height: 0px;
            i, svg {
                transition: all 0.2s ease;
                &.bi-dash-lg {
                    display: none;
                }
            }
        }
        &.expanded .tp-collapse-indicator {
            i {
                transform: rotate(90deg);
            }
            svg {
                &.bi-plus-lg {
                    display: none;
                }
                &.bi-dash-lg {
                    display: block;
                }
            }
        }
    }
    .tp-filter-attribute-scrollbar {
        max-height: 300px;
        overflow-y: auto;
        @include tp-scroll(5px);
    }
    label.form-check-label {
        font-weight: normal;
        cursor: pointer;
        .tp-filter-count {
            color: $text-muted;
            font-size: 12px;
            margin-right: 10px;
        }
        &:hover {
            color: o-color('primary');
            .tp-filter-count {
                color: o-color('primary');
                font-weight: bold;
            }
        }
    }
    .tp-search-input-group {
        .tp-search-icon {
            @include o-position-absolute($top: 10px, $right: 10px);
            z-index: 5;
        }
    }
    .tp-attribute-color-box {
        display: inline-block;
        height: 16px;
        width: 16px;
        border-radius: 3rem;
    }
    .tp-attribute-image-box {
        display: inline-block;
        height: 24px;
        width: 24px;
        border-radius: 3rem;
        vertical-align: middle;
        background-size: cover;
    }
    .tp-filter-image-style {
        .nav-item {
            flex: 0 1 33.33%;
            label {
                display: block;
                position: relative;
                img {
                    cursor: pointer;
                    height: 60px;
                    object-fit: contain;
                    border: 1px solid $border-color;
                    transition: 0.2s;
                    &:hover {
                        border-color: o-color('primary');
                    }
                }
            }
            &.compact {
                flex: 0 1 16.66%;
                label img {
                    height: 46px;
                }
            }
            :checked + label {
                img {
                    border-color: o-color('primary');
                }
                &:before {
                    font-family: "FontAwesome";
                    content: '\f058';
                    @include o-position-absolute($top: 3px, $right: 5px);
                    color: o-color('primary');
                    font-size: 14px;
                    z-index: 1;
                }
            }
        }
    }
}

.tp-range-filter {
    .irs--square {
        .irs-line {
            background: o-color('300');
        }
        .irs-min, .irs-max {
            background-color: transparent;
        }
        .irs-bar {
            background: o-color('primary');
        }
        .irs-handle {
            border-color: o-color('primary');
            border-radius: 1rem;
            -webkit-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            transform: rotate(0deg);
            cursor: pointer;
        }
    }
}

.tp-product-preview-swatches {
    --tp-product-preview-swatch-padding: 2px;
    --tp-product-preview-swatch-size: 30px;
    &.tp-product-preview-swatches-md {
        --tp-product-preview-swatch-size: 26px;
    }
    &.tp-product-preview-swatches-sm {
        --tp-product-preview-swatch-size: 24px;
    }
    .tp-swatch {
        height: var(--tp-product-preview-swatch-size);
        width: var(--tp-product-preview-swatch-size);
        border: 1px solid $border-color;
        padding: var(--tp-product-preview-swatch-padding);
        transition: all .2s ease;
        &:not(:last-child) {
            margin-right: 6px;
        }
        > div {
            background-size: cover;
        }
        &.active {
            border-color: o-color('primary');
        }
    }
}
@include media-breakpoint-up(lg) {
    .tp-product-preview-active {
        transition: opacity 0.5s ease, transform 2s cubic-bezier(0,0,.44,1.18);
        transform: scale(1.09);
    }
}

#products_grid {
    &.tp-product-image-fill-cover {
        --tp-product-image-fill: cover;
    }
    &.tp-product-image-fill-fill {
        --tp-product-image-fill: fill;
    }

    &.tp-product-image-size-landscape {
        --tp-product-image-size: 4/3;
    }
    &.tp-product-image-size-portrait {
        --tp-product-image-size: 4/5;
    }
    &.tp-product-image-size-vertical {
        --tp-product-image-size: 2/3;
    }
}
.tp-product-item {
    .tp-product-image-container {
        padding-top: calc(100% / (var(--tp-product-image-size, 1)));
        .tp-product-image {
            @include o-position-absolute(0, 0, 0, 0);
            object-fit: var(--tp-product-image-fill, contain);
        }
    }
    .tp-action-buttons {
        --tp-action-button-font-size: 16px;
        --tp-action-button-size: 45px;
        --tp-action-button-radius: 0rem;
        --tp-action-button-margin-bottom: 0px;
        @include media-breakpoint-down(lg) {
            --tp-action-button-font-size: 12px;
            --tp-action-button-size: 34px;
        }
        &.tp-action-buttons-rounded {
            --tp-action-button-font-size: 14px;
            --tp-action-button-size: 40px;
            --tp-action-button-radius: 30rem;
            --tp-action-button-margin-bottom: 5px;
            @include media-breakpoint-down(lg) {
                --tp-action-button-font-size: 12px;
                --tp-action-button-size: 30px;
            }
        }
        top: 10px;
        right: 10px;
        transition: all .3s ease;
        &:not(.tp-action-buttons-rounded) {
            box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.10);
        }
        @include media-breakpoint-up(lg) {
            opacity: 0;
            transform: translateX(20px) translateZ(0);
        }
        > a, > button {
            position: relative;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            background-color: white;
            border-radius: var(--tp-action-button-radius);
            font-size: var(--tp-action-button-font-size);
            width: var(--tp-action-button-size);
            height: var(--tp-action-button-size);
            margin-bottom: var(--tp-action-button-margin-bottom);
            box-shadow: none;
            opacity: 1;
            &:not(:disabled) {
                color: o-color('dark');
            }
            &:hover {
                color: o-color('primary');
            }
        }
        &.tp-action-buttons-rounded {
            > a, > button {
                box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.10);
            }
        }
    }
    .tp-product-rating-count {
        @include o-position-absolute($top: 14px, $right: 14px);
        padding: 0.2rem 0.6rem;
        box-shadow: $box-shadow-sm;
        color: #EABE12;
    }
    .tp-add-to-cart-btn {
        @include o-position-absolute($right: 0, $bottom: 0, $left: 0);
        z-index: 1;
        transition: transform .3s ease, opacity .3s ease;
        backface-visibility: hidden;
        transform: translateY(103%) translateZ(0);
    }
    .tp-product-stock-label {
        transition: transform .3s ease, opacity .3s ease;
    }
    @include media-breakpoint-down(lg) {
        .tp-product-title {
            font-size: 90%;
        }
        .tp-product-stock-label {
            display: none;
        }
        .tp-product-rating-count {
            display: none;
        }
    }
    &:hover {
        .tp-product-rating-count {
            display: none;
        }
        .tp-action-buttons {
            opacity: 1;
            transform: translateY(0) translateZ(0);
        }
        .tp-add-to-cart-btn {
            transform: translateY(0) translateZ(0);
        }
        .tp-product-stock-label {
            transform: translate(0, 100%);
        }
    }
    // Grid - 1
    &.tp-product-item-grid-1 {
        @include media-breakpoint-up(lg) {
            .tp-product-preview-swatches {
                @include o-position-absolute($right: -1px, $bottom: 0, $left: -1px);
                transition: transform .3s ease, opacity .3s ease;
                backface-visibility: hidden;
                visibility: hidden;
                padding: 4px;
                z-index: 2;
                border: 1px solid $border-color;
                opacity: 0;
                border-top-width: 0;
                transform: translate(0px, 60px);
            }
            &:hover {
                .tp-product-preview-swatches {
                    opacity: 1;
                    visibility: visible;
                    transform: translate(0px, 34px);
                }
            }
        }
        @include media-breakpoint-down(lg) {
            .tp-product-preview-swatches {
                --tp-product-preview-swatch-size: 22px;
            }
        }
    }
    // Grid - 2
    &.tp-product-item-grid-2 {
        @include media-breakpoint-up(lg) {
            .tp-product-preview-swatches {
                @include o-position-absolute($top: -4px, $right: 0px);
                transition: transform .3s ease, opacity .3s ease;
                backface-visibility: hidden;
                visibility: hidden;
                opacity: 0;
                box-shadow: -10px 0 10px 0px white;
            }
            &:hover {
                .tp-product-preview-swatches {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
        @include media-breakpoint-down(lg) {
            .tp-product-preview-swatches {
                --tp-product-preview-swatch-size: 22px;
            }
        }
    }
    // List
    &.tp-product-list-item {
        @include media-breakpoint-up(md) {
            .tp-product-wrapper {
                padding-top: 20px;
                padding-bottom: 20px;
                border-bottom: 1px solid $border-color;
                display: flex;
                align-items: center;
                flex-wrap: nowrap;
            }
            .tp-product-top {
                flex: 0 0 300px;
                margin-right: 20px;
            }
            .tp-product-content {
                flex: 1 1 auto;
                min-width: 0;
            }
            // In list mode don't hide stock label
            .tp-product-stock-label {
                transform: none;
            }
        }
    }
}

//------------------------------------------------------------------------------
// Product Detail
//------------------------------------------------------------------------------
.tp-navigation-content {
    img {
        width: 84px;
        height: 84px;
        object-fit: cover;
    }
    .flex-grow-1 {
        min-width: 116px;
    }
}
section#product_detail {
    #o-carousel-product {
        .carousel-control-prev, .carousel-control-next {
            opacity: 1;
        }
        @include media-breakpoint-up(lg) {
            .o_carousel_product_indicators {
                .carousel-indicators {
                    li {
                        opacity: 1;
                        border-color: transparent;
                        border-width: 2px;
                        &.active {
                            border: 2px solid o-color('primary');
                            box-shadow: none;
                        }
                    }
                }
            }
        }
        @include media-breakpoint-down(lg) {
            .carousel-indicators {
                li {
                    &.active {
                        background-color: o-color('primary');
                        border-color: o-color('primary');
                    }
                }
            }
        }
        &:not(.o_carousel_product_left_indicators) {
            .o_carousel_product_indicators {
                margin-top: 1rem;
            }
        }
        &.o_carousel_product_left_indicators {
            @include media-breakpoint-down(lg) {
                .o_carousel_product_indicators {
                    margin-top: 1rem;
                }
            }
        }
    }
    // Smaller discount price
    .oe_default_price, .tp-discount-percentage {
        font-size: 70%;
    }
    .tp-ecommerce-description > p {
        margin-bottom: 0px;
    }
    // Out of stock
    .availability_messages {
        margin-top: 10px;
    }
    .tp-bulk-price-block {
        &:hover {
            border-color: o-color('primary') !important;
        }
    }
    .tp-product-navigator {
        a .fa {
            font-size: 20px;
        }
    }
    .dr-attribute-instruction-btn {
        display: block !important;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.1rem;
        font-size: 11px;
    }
    // Wishlist/Compare button
    .o_add_wishlist_dyn {
        &.disabled, &[disabled], &:hover {
            @include tp-font-icons {
                &:before {
                    content: "\f00c";
                }
            }
        }
    }
    .o_add_compare_dyn {
        &:hover {
            @include tp-font-icons {
                &:before {
                    font-family: "FontAwesome";
                    content: "\f00c";
                }
            }
        }
    }
    // TODO
    .o_product_tags {
        display: none !important;
    }
    // Offer
    .tp-product-info {
        --list-group-item-padding-y: 0.8rem;
        --list-group-item-padding-x: 0.2rem;
        .list-group-item {
            border-style: dashed;
        }
        .description > p {
            margin-bottom: 0px;
        }
        .list-icon {
            width: 24px;
            height: 24px;
            i {
                font-size: 22px;
            }
        }
        .card-icon {
            width: 34px;
            height: 34px;
            i {
                font-size: 32px;
            }
        }
    }
    // Product Views
    .tp-views-indicator {
        height: 10px;
        width: 10px;
        .indicator-dot {
            height: 8px;
            width: 8px;
        }
        @keyframes views_animate {
            75%, to {
                transform: scale(2);
                opacity: 0
            }
        }
        .indicator-animate {
            animation: views_animate 1s cubic-bezier(0,0,.2,1) infinite;
        }
    }
}

.tp-sticky-add-to-cart {
    bottom: 55px;
    z-index: $zindex-fixed;
    left: 3%;
    background: o-color('white');
    .product-img {
        object-fit: cover;
    }
    .product-name {
        max-width: 180px;
        min-width: 100px;
    }
    .product-add-to-cart {
        font-size: 18px;
    }
}

// Tabs
.tp-product-details-tab {
    .nav-tabs {
        border-bottom-width: 0px;
        li.nav-item .nav-link {
            margin-bottom: 0px;
            border-width: 0px;
            color: $body-color;
            font-family: $headings-font-family;
            font-weight: $headings-font-weight;
        }
        @include media-breakpoint-down(lg) {
            flex-flow: column;
            background-color: o-color('100');
            li.nav-item {
                border-top: 1px solid $border-color;
                &:last-child {
                    border-bottom: 1px solid $border-color;
                }
                .nav-link {
                    padding: 0.8rem;
                    font-size: 0.9rem;
                    &.active {
                        border-color: o-color('primary');
                        color: color-contrast(o-color('primary'));
                        background-color: o-color('primary');
                    }
                }
            }
        }
        @include media-breakpoint-up(lg) {
            border-top: 1px solid $border-color;
            li.nav-item .nav-link {
                padding: 1.5rem 1.2rem;
                font-size: 1rem;
                &.active {
                    box-shadow: 0px 3px 0px 0px o-color('primary') inset;
                }
            }
        }
    }
    .tab-pane {
        border-bottom: 1px solid $border-color;
        .o_shop_discussion_rating {
            border-top-width: 0px;
            > section {
                padding-top: 1rem;
            }
            hr {
                display: none;
            }
        }
    }
}

.tp-suggested-product-slider {
    .tp-slider-controls {
        @include o-position-absolute($top: -2px, $right: 0);
    }
    .owl-stage {
        display: flex;
        align-items: stretch;
        .owl-item {
            display: flex;
            .card {
                display: flex;
                .card-img-top {
                    flex: 1 0;
                    display: flex;
                    img {
                        max-height: 22rem;
                        object-fit: contain;
                    }
                }
                .card-body {
                    flex: 0 0;
                }
            }
        }
    }
}

//------------------------------------------------------------------------------
// Searchbar
//------------------------------------------------------------------------------
.oe_search_found {
    display: none;
}

//------------------------------------------------------------------------------
// Qty Spinner
//------------------------------------------------------------------------------
.css_quantity {
    display: inline-flex;
    padding: 3px;
    border: 1px solid map-get($grays, '200');
    border-radius: $btn-border-radius-lg;
    background-color: $light;
    .btn {
        padding: 0.5rem 0.8rem;
        color: o-color('primary');
        border-width: 0px;
        &:focus, &:active {
            box-shadow: none;
        }
    }
    .quantity {
        border-width: 0px;
        text-align: center;
    }
    input { background-color: $light !important; }
    &.input-group-sm {
        .btn {
            padding: 0.4rem 0.6rem;
        }
        input { padding: 0.2rem; }
    }
    &.input-group-lg {
        .btn {
            font-size: 0.9rem;
            padding: 0.5rem 0.8rem;
        }
    }
}

//------------------------------------------------------------------------------
// Product Compare Button
//------------------------------------------------------------------------------
#wrap .o_product_feature_panel {
    border-width: 0px;
    right: 8px;
    left: auto;
    bottom: 55px;
    border-radius: $border-radius !important;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.10);
    height: 2.5rem;
    @include media-breakpoint-down(lg) {
        bottom: 90px;
    }
    .o_product_panel .o_product_panel_header {
        margin: 0px;
        .o_product_text {
            font-weight: $headings-font-weight;
            text-transform: capitalize;
        }
        .o_product_circle {
            vertical-align: 0;
            padding: 0 8px;
            line-height: 18px;
            border-radius: 1rem;
            margin-left: 4px;
        }
    }
}

//------------------------------------------------------------------------------
// Animation: Product Compare, Wishlist
//------------------------------------------------------------------------------
.o_animate_blink.o_red_highlight {
    background: o-color('primary') !important;
    box-shadow: 0 0 0 0 rgba(o-color('primary'), 0.4);
}
.o_animate_blink.o_shadow_animation {
    box-shadow: 0 0 5px 10px rgba(o-color('primary'), 0.4) !important;
}

//------------------------------------------------------------------------------
// Wishlist Page
//------------------------------------------------------------------------------
.tp-wishlist-item {
    .card {
        .card-img-top img {
            min-height: 16rem;
            object-fit: contain;
        }
    }
}

//------------------------------------------------------------------------------
// Comparison Page
//------------------------------------------------------------------------------
.oe_website_sale .tp-product-comparison {
    .table {
        thead tr td {
            vertical-align: initial;
            .card {
                width: 280px;
                img {
                    min-height: 16rem;
                    object-fit: contain;
                }
            }
        }
        tbody {
            border-top: 1px solid $border-color;
            tr td {
                &:first-child {
                    min-width: 180px;
                }
                &:not(:last-child) {
                    border-right: 1px solid $border-color;
                }
            }
        }
    }
}

//------------------------------------------------------------------------------
// Cart Page
//------------------------------------------------------------------------------
#cart_products {
    del.text-danger {
        font-weight: normal;
        color: $text-muted !important;
    }
}

//------------------------------------------------------------------------------
// Kacharo Code
//------------------------------------------------------------------------------

// Remove this in future version
// odoo change in stable vesion here is the PR : https: //github.com/odoo/odoo/pull/190764/files
// To manage older and latest community version apply css and remove xpath

.o_we_buy_now:not(.btn-primary-soft) {
    display: none !important;
}
