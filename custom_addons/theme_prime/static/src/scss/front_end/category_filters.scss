.d_category_lable {
    cursor: pointer;
}

// ======  Style 1 =========
.tp-droggol-18-builder-snippet-tab-1 {
    .d_category_lable {
        &.d_active {
            font-weight: bold;
            color: black;
            border-bottom: 3px solid o-color('primary');
        }
        margin-bottom: -1px;
        border-bottom: 3px solid transparent;
        transition: color 300ms ease-in-out 0s, background-color 300ms ease-in-out 0s, background-position 300ms ease-in-out 0s;
        &:not(.d_active):before {
            content: "";
            @include o-position-absolute($left: 0px, $bottom: -3px);
            width: 100%;
            height: 3px;
            background-color: o-color('primary');
            visibility: hidden;
            -webkit-transform: scaleX(0);
            transform: scaleX(0);
            -webkit-transition: all 0.3s ease-in-out 0s;
            transition: all 0.3s ease-in-out 0s;
        }
        &:not(.d_active):hover:before {
            visibility: visible;
            -webkit-transform: scaleX(1);
            transform: scaleX(1);
        }
    }
}

// ======  Style 2 =========

.tp-droggol-18-builder-snippet-tab-2 {
    .d_category_lable {
        &:hover, &.d_active {
            border: 1px solid o-color('primary') !important;
            background-color: o-color('primary') !important;
            color: white !important;
        }
    }
}

// ======  Style 3 =========

.tp-droggol-18-builder-snippet-tab-3 {
    .d_category_lable {
        color: map-get($grays, '500');
        &.d_active {
            border-bottom: 2px solid o-color('primary');
            color: map-get($grays, '600');
            font-weight: 500;
        }
    }
}

// ======  Style 4 =========

.tp-droggol-18-builder-snippet-tab-4 {
    overflow-x: auto;
    overflow-y: hidden;
    .d_category_lable {
        border-bottom: 1px solid $border-color;
        &.d_active {
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            color: o-color('primary');
            background-color: rgba(o-color('primary'), 0.1) !important;
            border-bottom: 2px solid o-color('primary');
        }
    }
}

// ======  Style 5 =========

.tp-droggol-18-builder-snippet-tab-5 {
    .d_category_lable {
        border-radius: 3px;
        border: 1px solid $border-color;
        &:hover, &.d_active {
            border: 1px solid o-color('primary') !important;
            background-color: o-color('primary') !important;
            color: white !important;
            box-shadow: $box-shadow-sm;
        }
    }
}

// ======  Style 6 =========

.tp-droggol-18-builder-snippet-tab-6 {
    .d_category_lable:not(:last-child):after {
        display: inline-block;
        content: '|';
        margin-left: 12px;
        margin-right: 12px;
        color: $body-color;
        font-size: 16px;
    }
    .d_category_lable {
        font-size: 16px;
        &.d_active {
            color: o-color('primary');
        }
    }
}
