<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="tp_config_field_category">
        <span t-if="record.category_info" class="d-block tp-text-body text-truncate" style="max-width:110px;" t-esc="record.category_info.name"/>
    </t>

    <t t-name="tp_config_field_brand">
        <img class="tp-rounded-border-lg border" style="height:45px;" t-attf-src="/web/image/product.attribute.value/#{record.brand_info.id}/dr_image"/>
    </t>

    <t t-name="tp_config_field_product_name">
        <div class="row g-0 px-2" t-if="['product.template', 'product.product'].includes(environmentModel)">
            <span t-out="record.name" class="d-block col-8 h6 mb-0 tp-text-body text-truncate fw-normal"/>
            <div class="col-4 position-relative text-end">
                <span class="text-primary h6 mb-0" t-out="record.price"/>
                <div class="text-danger position-absolute small ps-1 end-0" style="text-decoration: line-through; white-space: nowrap;" t-if="record.has_discounted_price" t-out="record.list_price"/>
            </div>
        </div>
        <t t-if="environmentModel == 'product.public.category'">
            <t t-set="CategoryData" t-value="this.getCategoryRecord(recordID)"/>
            <h6 t-if="record.name" class="mb-1 fw-light ps-2 d-block text-truncate text-primary"><t t-esc="record.name"/></h6>
            <small class="tp-text-body fw-light ps-2"><t t-out="CategoryData.count"/> Products</small>
        </t>
        <t t-if="environmentModel == 'product.attribute.value'">
            <h6 t-out="record.name" class="d-block tp-text-body fw-light ps-4 text-truncate mb-0"/>
        </t>
    </t>

    <t t-name="tp_config_field_product_stock_label">
        <span t-if="record.dr_show_out_of_stock == 'OUT_OF_STOCK'" class="small px-2 text-danger">
            <i class="fa fa-circle"/> Out Of Stock
        </span>
        <span t-elif="record.dr_show_out_of_stock" class="small px-2 text-warning">
            <i class="fa fa-circle"/> <t t-esc="record.dr_show_out_of_stock"/> Units Left
        </span>
        <span t-else=" " class="small px-2 text-success">
            <i class="fa fa-circle"/> In stock
        </span>
    </t>

    <t t-name="theme_prime.TpRecordSelector">
        <div class="tp-record-selector m-0">
            <div t-if="SelectionMode === 'badge'" class="row g-0 mb-4">
                <div class="col-12 tp-card-selector">
                    <t t-component="getOWLComponent('TpSearchInput')" t-props="getSubComponent('TpSearchInput')"/>
                </div>
                <div class="col-12 py-2">
                    <t t-foreach="recordsIDs" t-as="recordID" t-key="recordID + state.last_update">
                        <t t-set="record" t-value="this.getRecord(this.records, recordID, 'id')"/>
                        <span class="border px-2 py-1 mt-2 tp-rounded-border-lg d-inline-block me-2">
                            <small t-esc="record.name"/>
                            <i class="fa fa-times text-500 ms-2 small cursor-pointer" t-on-click.prevent="() => this._onClickRemoveItem(record.id)"/>
                        </span>
                    </t>
                </div>
            </div>
            <t t-else=" ">
                <div t-if="hasSwitcher or (models and models.length and models.length > 1)" class="row g-0 p-2 py-3 align-items-center tp-rounded-border border my-3">
                    <t t-if="hasSwitcher">
                        <div class="col-7 d-flex align-items-center">
                            <h6 class="mb-0 text-uppercase tp-text-body small">Selection Mode:</h6>
                        </div>
                        <div class="col-5">
                            <t t-component="getOWLComponent('TpDropDown')" t-props="getSubComponent('selectionType')"/>
                        </div>
                    </t>
                    <hr t-if="hasSwitcher and models and models.length > 1" class="my-3" style="border-style:dashed;" />
                    <t t-if="models and models.length > 1">
                        <div class="col-7 d-flex align-items-center">
                            <h6 class="mb-0 text-uppercase tp-text-body small">Model:</h6>
                        </div>
                        <div class="col-5">
                            <t t-component="getOWLComponent('TpDropDown')" t-props="getSubComponent('model')"/>
                        </div>
                    </t>
                </div>
                <div t-attf-class="row g-0 #{componentDefaultVal.selectionType !== 'manual' ? 'd-none' : ' '}">
                    <div class="col-12 my-3">
                        <t t-component="getOWLComponent('TpSearchInput')" t-props="getSubComponent('TpSearchInput')"/>
                    </div>
                    <div t-if="recordsIDs.length == recordsLimit" class="col-12 my-2">
                        <div class="alert font-weight-light alert-warning tp-warning-alert alert-dismissible p-2 mb-0 shadow-sm" role="alert">
                            <i class="fa fa-exclamation-triangle pr-2"></i> You can select only <t t-out="recordsLimit"/> items.
                        </div>
                    </div>
                    <div class="col-12">
                        <div t-attf-class="bg-white border mb-0 bg-white p-0 shadow-sm border tp-rounded-border #{!recordsIDs.length ? 'd-none' : ''}">
                            <div class="border-bottom d-flex align-items-center justify-content-between p-2">
                                <h6 class="tp-text-body mb-0">Selected Records</h6>
                                <span t-on-click="() => state.collectionState = 'creating'" t-if="state.collectionState === 'draft'" class="badge badge-soft-primary tp-rounded-border-lg small badge-sm px-2 py-1 fw-light"><t t-call="theme_prime.icon_plus"><t t-set="hw" t-value="20"/></t> New Collection</span>
                                <div t-if="state.collectionState === 'creating'" class="input-group mb-0 tp-rounded-border-lg" style="max-width:185px">
                                    <input type="text" class="form-control" style="border-top-left-radius: 5px;border-bottom-left-radius: 5px;" placeholder="Collection Name" t-ref="tp-collection-input"/>
                                    <button t-on-click="() => this._onClickCreateCollection()" class="btn btn-primary-soft btn-sm" style="border-top-right-radius: 5px;border-bottom-right-radius: 5px;" ><t t-call="theme_prime.icon_check"/></button>
                                </div>
                                <span t-on-click="() => state.collectionState = 'creating'" t-if="state.collectionState === 'created'" class="small text-success"><t t-call="theme_prime.icon_check"><t t-set="hw" t-value="20"/></t> Collection Created</span>
                            </div>
                            <div t-ref="tp-records-container" class="tp-selected-record-list p-1 tp-light-bg">
                                <t t-set="num_records" t-value="0"/>
                                <t t-foreach="recordsIDs" t-as="recordID" t-key="recordID + state.last_update">
                                    <t t-set="record" t-value="this.getRecord(this.records, recordID, 'id')"/>
                                    <div t-if="record" t-attf-class="tp-record-item position-relative tp-rounded-border bg-white row g-0 #{recordsIDs.length > 1 ? 'mt-1' : ''} #{recordID_last ? 'mb-1' : ''}" t-att-data-record-id="record.id">
                                        <t t-set="num_records" t-value="num_records+1"/>
                                        <div style="visibility: hidden;" class="tp-bg-soft-primary tp-sortable-handle h-100 justify-content-center d-flex align-items-center">
                                            <span class="text-primary fa fa-ellipsis-v"></span>
                                            <span style="padding-left:2px;" class="text-primary fa fa-ellipsis-v"></span>
                                        </div>
                                        <div t-attf-class="#{environmentModel === 'product.attribute.value' ? 'col-3' : 'col-2'}">
                                            <t t-if="environmentModel === 'product.attribute.value'">
                                                <img style="max-height:60px;" t-attf-src="/web/image?model=#{environmentModel}&amp;id=#{record.id}&amp;field=dr_image" class="img me-auto tp-rounded-border"/>
                                            </t>
                                            <t t-else="">
                                                <img t-if="record.img_small" t-att-src="record.img_small" class="img img-fluid tp-rounded-border p-1 mx-auto"/>
                                                <img t-else=" " t-attf-src="/web/image?model=#{environmentModel}&amp;id=#{record.id}&amp;field=image_128" class="img img-fluid tp-rounded-border p-1 mx-auto"/>
                                            </t>
                                        </div>
                                        <div t-attf-class="#{environmentModel === 'product.attribute.value' ? 'col-9' : 'col-10'} d-flex flex-column justify-content-center">
                                            <t t-foreach="this.fields" t-as="field" t-key="field">
                                                <t t-if="fieldsLabel[field]">
                                                    <t t-if="Object.keys(getFieldsTemplates).includes(field)">
                                                        <t t-call="#{getFieldsTemplates[field]}"/>
                                                    </t>
                                                    <t t-else=" ">
                                                        <span t-if="this.fieldsToMarkup.includes(field)" t-out="record[field]"/>
                                                        <span class="px-2" t-else="" t-esc="record[field]"/>
                                                    </t>
                                                </t>
                                            </t>
                                        </div>
                                        <div style="visibility: hidden;" class="d-flex flex-column position-absolute end-0 top-0 tp-action-buttons-container overflow-hidden h-100">
                                            <span t-if="!isReadOnly" t-on-click="() => this._onClickEditItem(record.id)" class="badge tp-edit-action d-flex align-items-center justify-content-center badge-primary p-1">
                                                <t t-call="theme_prime.icon_pencil">
                                                    <t t-set="hw" t-value="12"/>
                                                </t>
                                            </span>
                                            <span t-on-click="() => this._onClickRemoveItem(record.id)" class="badge d-flex align-items-center justify-content-center tp-remove-action badge-danger p-1">
                                                <t t-call="theme_prime.icon_trash">
                                                    <t t-set="hw" t-value="12"/>
                                                </t>
                                            </span>
                                        </div>
                                    </div>
                                </t>
                            </div>
                            <div class="d-flex align-items-center justify-content-between p-2">
                                <small class="tp-text-body fw-light mb-0"><t t-esc="num_records"/> Records</small>
                                <button t-on-click="() => this._onClickClearItems()" class="btn btn-soft-danger d-block btn-sm rounded-circle">
                                    <i class="fa fa-trash-o pe-2"></i> Clear Selection
                                </button>
                            </div>
                        </div>
                        <div class="my-4 text-center p-3 tp-rounded-border-lg" style="border: 1px dashed #dee2e6;" t-if="!recordsIDs.length">
                            <img class="img img-fluid mx-auto" style="max-height:200px;" src="/theme_prime/static/src/img/search.png"/>
                            <h6 class="mt-3 fw-light tp-text-body">Please search and add few records from the input.</h6>
                            <h6 class="small fw-light tp-text-body-light">No records are selected</h6>
                        </div>
                    </div>
                </div>
                <div t-if="componentDefaultVal.selectionType === 'advance'">
                    <!-- VDOM Magic :) -->
                    <t t-key="environmentModel" t-component="getOWLComponent('TpDomainComponent')" t-props="{... this.props.componentData.domain_params, isReadOnly: isReadOnly, environmentModel: environmentModel}"/>
                </div>
            </t>
        </div>
    </t>
</templates>
