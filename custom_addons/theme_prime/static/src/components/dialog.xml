<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="theme_prime.icon_core">
        <t t-set="hw" t-value="hw || 18"/>
        <svg xmlns="http://www.w3.org/2000/svg" t-att-width="hw" t-att-height="hw" fill="currentColor" viewBox="0 0 16 16"><t t-out="0"/></svg>
    </t>
    <t t-name="theme_prime.icon_display_large">
        <t t-call="theme_prime.icon_core">
            <path d="M0 4s0-2 2-2h12s2 0 2 2v6s0 2-2 2h-4c0 .667.083 1.167.25 1.5H11a.5.5 0 0 1 0 1H5a.5.5 0 0 1 0-1h.75c.167-.333.25-.833.25-1.5H2s-2 0-2-2V4zm1.398-.855a.758.758 0 0 0-.254.302A1.46 1.46 0 0 0 1 4.01V10c0 .325.078.502.145.602.07.105.17.188.302.254a1.464 1.464 0 0 0 .538.143L2.01 11H14c.325 0 .502-.078.602-.145a.758.758 0 0 0 .254-.302 1.464 1.464 0 0 0 .143-.538L15 9.99V4c0-.325-.078-.502-.145-.602a.757.757 0 0 0-.302-.254A1.46 1.46 0 0 0 13.99 3H2c-.325 0-.502.078-.602.145z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_tools">
        <t t-call="theme_prime.icon_core">
            <path d="M1 0 0 1l2.2 3.081a1 1 0 0 0 .815.419h.07a1 1 0 0 1 .708.293l2.675 2.675-2.617 2.654A3.003 3.003 0 0 0 0 13a3 3 0 1 0 5.878-.851l2.654-2.617.968.968-.305.914a1 1 0 0 0 .242 1.023l3.27 3.27a.997.997 0 0 0 1.414 0l1.586-1.586a.997.997 0 0 0 0-1.414l-3.27-3.27a1 1 0 0 0-1.023-.242L10.5 9.5l-.96-.96 2.68-2.643A3.005 3.005 0 0 0 16 3c0-.269-.035-.53-.102-.777l-2.14 2.141L12 4l-.364-1.757L13.777.102a3 3 0 0 0-3.675 3.68L7.462 6.46 4.793 3.793a1 1 0 0 1-.293-.707v-.071a1 1 0 0 0-.419-.814zm9.646 10.646a.5.5 0 0 1 .708 0l2.914 2.915a.5.5 0 0 1-.707.707l-2.915-2.914a.5.5 0 0 1 0-.708M3 11l.471.242.529.026.287.445.445.287.026.529L5 13l-.242.471-.026.529-.445.287-.287.445-.529.026L3 15l-.471-.242L2 14.732l-.287-.445L1.268 14l-.026-.529L1 13l.242-.471.026-.529.445-.287.287-.445.529-.026z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_display_mobile">
        <t t-call="theme_prime.icon_core">
            <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
            <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_setting">
        <t t-call="theme_prime.icon_core">
            <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492M5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0"/>
            <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_undo">
        <t t-call="theme_prime.icon_core">
            <path fill-rule="evenodd" d="M8 3a5 5 0 1 1-4.546 2.914.5.5 0 0 0-.908-.417A6 6 0 1 0 8 2z"/>
            <path d="M8 4.466V.534a.25.25 0 0 0-.41-.192L5.23 2.308a.25.25 0 0 0 0 .384l2.36 1.966A.25.25 0 0 0 8 4.466"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_redo">
        <t t-call="theme_prime.icon_core">
            <path fill-rule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2z"/>
            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_grid_1_2">
        <t t-call="theme_prime.icon_core">
            <path d="M8.235 1.559a.5.5 0 0 0-.47 0l-7.5 4a.5.5 0 0 0 0 .882L3.188 8 .264 9.559a.5.5 0 0 0 0 .882l7.5 4a.5.5 0 0 0 .47 0l7.5-4a.5.5 0 0 0 0-.882L12.813 8l2.922-1.559a.5.5 0 0 0 0-.882l-7.5-4zm3.515 7.008L14.438 10 8 13.433 1.562 10 4.25 8.567l3.515 1.874a.5.5 0 0 0 .47 0l3.515-1.874zM8 9.433 1.562 6 8 2.567 14.438 6z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_fullscreen">
        <t t-call="theme_prime.icon_core">
            <path fill-rule="evenodd" d="M5.828 10.172a.5.5 0 0 0-.707 0l-4.096 4.096V11.5a.5.5 0 0 0-1 0v3.975a.5.5 0 0 0 .5.5H4.5a.5.5 0 0 0 0-1H1.732l4.096-4.096a.5.5 0 0 0 0-.707zm4.344 0a.5.5 0 0 1 .707 0l4.096 4.096V11.5a.5.5 0 1 1 1 0v3.975a.5.5 0 0 1-.5.5H11.5a.5.5 0 0 1 0-1h2.768l-4.096-4.096a.5.5 0 0 1 0-.707zm0-4.344a.5.5 0 0 0 .707 0l4.096-4.096V4.5a.5.5 0 1 0 1 0V.525a.5.5 0 0 0-.5-.5H11.5a.5.5 0 0 0 0 1h2.768l-4.096 4.096a.5.5 0 0 0 0 .707zm-4.344 0a.5.5 0 0 1-.707 0L1.025 1.732V4.5a.5.5 0 0 1-1 0V.525a.5.5 0 0 1 .5-.5H4.5a.5.5 0 0 1 0 1H1.732l4.096 4.096a.5.5 0 0 1 0 .707z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_play_circle">
        <t t-call="theme_prime.icon_core">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_check_circle">
        <t t-call="theme_prime.icon_core">
            <path d="M2.5 8a5.5 5.5 0 0 1 8.25-4.764.5.5 0 0 0 .5-.866A6.5 6.5 0 1 0 14.5 8a.5.5 0 0 0-1 0 5.5 5.5 0 1 1-11 0z"/>
            <path d="M15.354 3.354a.5.5 0 0 0-.708-.708L8 9.293 5.354 6.646a.5.5 0 1 0-.708.708l3 3a.5.5 0 0 0 .708 0l7-7z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_cross_circle">
        <t t-call="theme_prime.icon_core">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_check_list">
        <t t-call="theme_prime.icon_core">
            <path fill-rule="evenodd" d="M5 11.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zM3.854 2.146a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 1 1 .708-.708L2 3.293l1.146-1.147a.5.5 0 0 1 .708 0zm0 4a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 1 1 .708-.708L2 7.293l1.146-1.147a.5.5 0 0 1 .708 0zm0 4a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 0 1 .708-.708l.146.147 1.146-1.147a.5.5 0 0 1 .708 0z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_hash_tag">
        <t t-call="theme_prime.icon_core">
            <path d="M8.39 12.648a1.32 1.32 0 0 0-.015.18c0 .305.21.508.5.508.266 0 .492-.172.555-.477l.554-2.703h1.204c.421 0 .617-.234.617-.547 0-.312-.188-.53-.617-.53h-.985l.516-2.524h1.265c.43 0 .618-.227.618-.547 0-.313-.188-.524-.618-.524h-1.046l.476-2.304a1.06 1.06 0 0 0 .016-.164.51.51 0 0 0-.516-.516.54.54 0 0 0-.539.43l-.523 2.554H7.617l.477-2.304c.008-.04.015-.118.015-.164a.512.512 0 0 0-.523-.516.539.539 0 0 0-.531.43L6.53 5.484H5.414c-.43 0-.617.22-.617.532 0 .312.187.539.617.539h.906l-.515 2.523H4.609c-.421 0-.609.219-.609.531 0 .313.188.547.61.547h.976l-.516 2.492c-.008.04-.015.125-.015.18 0 .305.21.508.5.508.265 0 .492-.172.554-.477l.555-2.703h2.242l-.515 2.492zm-1-6.109h2.266l-.515 2.563H6.859l.532-2.563z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_tag">
        <t t-call="theme_prime.icon_core">
            <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_eye">
        <t t-call="theme_prime.icon_core">
            <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/>
            <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_cart">
        <t t-call="theme_prime.icon_core">
            <path d="M0 2.5A.5.5 0 0 1 .5 2H2a.5.5 0 0 1 .485.379L2.89 4H14.5a.5.5 0 0 1 .485.621l-1.5 6A.5.5 0 0 1 13 11H4a.5.5 0 0 1-.485-.379L1.61 3H.5a.5.5 0 0 1-.5-.5zM3.14 5l1.25 5h8.22l1.25-5H3.14zM5 13a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm-2 1a2 2 0 1 1 4 0 2 2 0 0 1-4 0zm9-1a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm-2 1a2 2 0 1 1 4 0 2 2 0 0 1-4 0z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_font">
        <t t-call="theme_prime.icon_core">
            <path d="M12.258 3h-8.51l-.083 2.46h.479c.26-1.544.758-1.783 2.693-1.845l.424-.013v7.827c0 .663-.144.82-1.3.923v.52h4.082v-.52c-1.162-.103-1.306-.26-1.306-.923V3.602l.431.013c1.934.062 2.434.301 2.693 1.846h.479L12.258 3z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_heart">
        <t t-call="theme_prime.icon_core">
            <path d="m8 6.236-.894-1.789c-.222-.443-.607-1.08-1.152-1.595C5.418 2.345 4.776 2 4 2 2.324 2 1 3.326 1 4.92c0 1.211.554 2.066 1.868 3.37.337.334.721.695 1.146 1.093C5.122 10.423 6.5 11.717 8 13.447c1.5-1.73 2.878-3.024 3.986-4.064.425-.398.81-.76 1.146-1.093C14.446 6.986 15 6.131 15 4.92 15 3.326 13.676 2 12 2c-.777 0-1.418.345-1.954.852-.545.515-.93 1.152-1.152 1.595L8 6.236zm.392 8.292a.513.513 0 0 1-.784 0c-1.601-1.902-3.05-3.262-4.243-4.381C1.3 8.208 0 6.989 0 4.92 0 2.755 1.79 1 4 1c1.6 0 2.719 1.05 3.404 2.008.26.365.458.716.596.992a7.55 7.55 0 0 1 .596-.992C9.281 2.049 10.4 1 12 1c2.21 0 4 1.755 4 3.92 0 2.069-1.3 3.288-3.365 5.227-1.193 1.12-2.642 2.48-4.243 4.38z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_exchange">
        <t t-call="theme_prime.icon_core">
            <path fill-rule="evenodd" d="M1 11.5a.5.5 0 0 0 .5.5h11.793l-3.147 3.146a.5.5 0 0 0 .708.708l4-4a.5.5 0 0 0 0-.708l-4-4a.5.5 0 0 0-.708.708L13.293 11H1.5a.5.5 0 0 0-.5.5zm14-7a.5.5 0 0 1-.5.5H2.707l3.147 3.146a.5.5 0 1 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 4H14.5a.5.5 0 0 1 .5.5z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_star">
        <t t-call="theme_prime.icon_core">
            <path d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.522-3.356c.33-.314.16-.888-.282-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767-3.686 1.894.694-3.957a.565.565 0 0 0-.163-.505L1.71 6.745l4.052-.576a.525.525 0 0 0 .393-.288L8 2.223l1.847 3.658a.525.525 0 0 0 .393.288l4.052.575-2.906 2.77a.565.565 0 0 0-.163.506l.694 3.957-3.686-1.894a.503.503 0 0 0-.461 0z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_description">
        <t t-call="theme_prime.icon_core">
            <path d="M14.5 3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h13zm-13-1A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-13z"/>
            <path d="M3 5.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zM3 8a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9A.5.5 0 0 1 3 8zm0 2.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_box">
        <t t-call="theme_prime.icon_core">
            <path d="M11 2a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V5a3 3 0 0 1 3-3h6zM5 1a4 4 0 0 0-4 4v6a4 4 0 0 0 4 4h6a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_brush">
        <t t-call="theme_prime.icon_core">
            <path d="M15.825.12a.5.5 0 0 1 .132.584c-1.53 3.43-4.743 8.17-7.095 10.64a6.067 6.067 0 0 1-2.373 1.534c-.018.227-.06.538-.16.868-.201.659-.667 1.479-1.708 1.74a8.118 8.118 0 0 1-3.078.132 3.659 3.659 0 0 1-.562-.135 1.382 1.382 0 0 1-.466-.247.714.714 0 0 1-.204-.288.622.622 0 0 1 .004-.443c.095-.245.316-.38.461-.452.394-.197.625-.453.867-.826.095-.144.184-.297.287-.472l.117-.198c.151-.255.326-.54.546-.848.528-.739 1.201-.925 1.746-.896.126.007.243.025.348.048.062-.172.142-.38.238-.608.261-.619.658-1.419 1.187-2.069 2.176-2.67 6.18-6.206 9.117-8.104a.5.5 0 0 1 .596.04zM4.705 11.912a1.23 1.23 0 0 0-.419-.1c-.246-.013-.573.05-.879.479-.197.275-.355.532-.5.777l-.105.177c-.106.181-.213.362-.32.528a3.39 3.39 0 0 1-.76.861c.69.112 1.736.111 2.657-.12.559-.139.843-.569.993-1.06a3.122 3.122 0 0 0 .126-.75l-.793-.792zm1.44.026c.12-.04.277-.1.458-.183a5.068 5.068 0 0 0 1.535-1.1c1.9-1.996 4.412-5.57 6.052-8.631-2.59 1.927-5.566 4.66-7.302 6.792-.442.543-.795 1.243-1.042 1.826-.121.288-.214.54-.275.72v.001l.575.575zm-4.973 3.04.007-.005a.031.031 0 0 1-.007.004zm3.582-3.043.002.001h-.002z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_trash">
        <t t-call="theme_prime.icon_core">
            <path d="M6.5 1h3a.5.5 0 0 1 .5.5v1H6v-1a.5.5 0 0 1 .5-.5ZM11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3A1.5 1.5 0 0 0 5 1.5v1H2.506a.58.58 0 0 0-.01 0H1.5a.5.5 0 0 0 0 1h.538l.853 10.66A2 2 0 0 0 4.885 16h6.23a2 2 0 0 0 1.994-1.84l.853-10.66h.538a.5.5 0 0 0 0-1h-.995a.59.59 0 0 0-.01 0H11Zm1.958 1-.846 10.58a1 1 0 0 1-.997.92h-6.23a1 1 0 0 1-.997-.92L3.042 3.5h9.916Zm-7.487 1a.5.5 0 0 1 .528.47l.5 8.5a.5.5 0 0 1-.998.06L5 5.03a.5.5 0 0 1 .47-.53Zm5.058 0a.5.5 0 0 1 .47.53l-.5 8.5a.5.5 0 1 1-.998-.06l.5-8.5a.5.5 0 0 1 .528-.47ZM8 4.5a.5.5 0 0 1 .5.5v8.5a.5.5 0 0 1-1 0V5a.5.5 0 0 1 .5-.5Z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_magic">
        <t t-call="theme_prime.icon_core">
            <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0v1.829Zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_pencil">
        <t t-call="theme_prime.icon_core">
            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_plus">
        <t t-call="theme_prime.icon_core">
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_check">
        <t t-call="theme_prime.icon_core">
            <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0"/>
        </t>
    </t>
    <t t-name="theme_prime.icon_full_screen">
        <t t-call="theme_prime.icon_core">
            <path fill-rule="evenodd" d="M5.828 10.172a.5.5 0 0 0-.707 0l-4.096 4.096V11.5a.5.5 0 0 0-1 0v3.975a.5.5 0 0 0 .5.5H4.5a.5.5 0 0 0 0-1H1.732l4.096-4.096a.5.5 0 0 0 0-.707m4.344 0a.5.5 0 0 1 .707 0l4.096 4.096V11.5a.5.5 0 1 1 1 0v3.975a.5.5 0 0 1-.5.5H11.5a.5.5 0 0 1 0-1h2.768l-4.096-4.096a.5.5 0 0 1 0-.707m0-4.344a.5.5 0 0 0 .707 0l4.096-4.096V4.5a.5.5 0 1 0 1 0V.525a.5.5 0 0 0-.5-.5H11.5a.5.5 0 0 0 0 1h2.768l-4.096 4.096a.5.5 0 0 0 0 .707m-4.344 0a.5.5 0 0 1-.707 0L1.025 1.732V4.5a.5.5 0 0 1-1 0V.525a.5.5 0 0 1 .5-.5H4.5a.5.5 0 0 1 0 1H1.732l4.096 4.096a.5.5 0 0 1 0 .707"/>
        </t>
    </t>
    <t t-name="theme_prime.action_buttons_group">
        <button style="flex: 1 0 0%;" class="btn btn-primary" t-on-click="() => this.save()">
            <t t-call="theme_prime.icon_check_circle">
                <t t-set="hw" t-value="20"/>
            </t>
            <span class="ps-3 fw-light">SAVE</span>
        </button>
        <button style="flex: 1 0 0%;" class="btn btn-light ms-2" t-on-click="() => this._onDiscardChange()">
            <t t-call="theme_prime.icon_cross_circle">
                <t t-set="hw" t-value="20"/>
            </t>
            <span class="ps-3 fw-light">CLOSE</span>
        </button>
    </t>
    <t t-name="theme_prime.preview_renderer">
        <div class="row bg-white m-1 mt-0 tp-rounded-border-lg shadow-sm border border-top-0" style="border-top-left-radius: 0px !important;border-top-right-radius: 0px !important;">
            <div class="col-3 py-2 d-flex align-items-center px-2 justify-content-center">
                <div class="border tp-rounded-border-lg w-100">
                    <t t-if="this.registryToUse" t-component="allSnippptsDropdown.component" t-props="allSnippptsDropdown.props"/>
                </div>
            </div>
            <div class="col-2 py-2 d-flex align-items-center justify-content-center">
                <button t-on-click="() => this.toggleViewPort('desktop')" t-attf-class="btn px-3 d-flex align-items-center justify-content-center tp-configurator-action-btn rounded-circle #{state.isMobile ? 'btn-light' : 'btn-soft-primary'}">
                    <t t-call="theme_prime.icon_display_large">
                        <t t-set="hw" t-value="18"/>
                    </t>
                    <span style="font-size:12px !important; line-height:12px;" class="fw-normal ms-2">Desktop</span>
                </button>
                <button t-on-click="() => this.toggleViewPort('mobile')" t-attf-class="ms-2 px-3 tp-configurator-action-btn rounded-circle btn #{!state.isMobile ? 'btn-light' : 'btn-soft-primary'}">
                    <t t-call="theme_prime.icon_display_mobile">
                        <t t-set="hw" t-value="16"/>
                    </t>
                    <span style="font-size:12px !important; line-height:12px;" class="fw-normal ms-2">Mobile</span>
                </button>
            </div>
            <div class="col-2 py-2 d-flex align-items-center justify-content-between">
                <div>
                    <button t-on-click="() => this._onClickCommand('undo')" t-attf-class="btn px-3 tp-configurator-action-btn rounded-circle #{previewState.activeStep === 1 ? 'btn-light' : 'btn-soft-primary'}" t-att-disabled="previewState.activeStep === 1">
                        <t t-call="theme_prime.icon_undo">
                            <t t-set="hw" t-value="18"/>
                        </t>
                        <span style="font-size:12px !important; line-height:12px;" class="fw-normal ms-2">Undo</span>
                    </button>
                    <button t-on-click="() => this._onClickCommand('redo')" t-attf-class="ms-2 px-3 tp-configurator-action-btn rounded-circle btn #{previewState.history.length === previewState.activeStep ? 'btn-light' : 'btn-soft-primary'}" t-att-disabled="previewState.history.length === previewState.activeStep">
                        <t t-call="theme_prime.icon_redo">
                            <t t-set="hw" t-value="18"/>
                        </t>
                        <span style="font-size:12px !important; line-height:12px;" class="fw-normal ms-2">Redo</span>
                    </button>
                </div>
            </div>
            <div class="col-2 py-2 d-flex align-items-center justify-content-between">
                <t t-if="pricelists.length" t-component="allPricelistDropdown.component" t-props="allPricelistDropdown.props"/>
            </div>
            <div class="col-3 py-2 px-3 d-flex align-items-center justify-content-between" style="width:24%;margin-left:1%;">
                <t t-call="theme_prime.action_buttons_group"/>
            </div>
        </div>
        <div class="row g-0">
            <div class="col-9">
                <div t-if="state.isMobile and previewState.HasRecords" class="tp_preview_window sticky-top tp_mobile_mode">
                    <div class="tp_preview_container" style="top:430px !important;">
                        <iframe t-if="previewState.HasRecords" src="/theme_prime/get_preview_body?mobile=1" t-on-load="_onPreviewLoaded" class="tp-preview-iframe h-100 w-100 pt-0 bg-white shadow-sm tp-rounded-border-lg" t-ref="tp-preview-container"/>
                        <div class="tp_mobile_body">
                            <img alt="phone" src="/website/static/src/img/phone.png"/>
                        </div>
                    </div>
                </div>
                <div t-if="!state.isMobile and previewState.HasRecords" class="tp_preview_window sticky-top" style="top:-2rem;">
                    <div class="tp_preview_container shadow-sm">
                        <div class="tp_desktop_body bg-white" style="border-top-left-radius: 15px; border-top-right-radius: 15px;">
                            <img class="w-100" alt="phone" src="/theme_prime/static/src/img/browser.png"/>
                        </div>
                        <iframe t-if="previewState.HasRecords" src="/theme_prime/get_preview_body" t-on-load="_onPreviewLoaded" class="tp-preview-iframe h-100 w-100 pt-3 bg-white" t-ref="tp-preview-container"/>
                        <div class="tp-bottom-action-bar mt-n2 overflow-hidden shadow-sm" style="border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">
                            <div class="row g-0">
                                <div class="col-4 bg-100 py-2 px-3">
                                    <div class="tp-rounded-border w-100">
                                        <div class="d-flex align-items-center p-2 m-1">
                                            <div class="flex-shrink-0">
                                                <span class="bg-primary tp-rounded-border-lg p-2 d-flex align-items-center justify-content-center" style="height:50px;width:50px;">
                                                    <t t-call="theme_prime.icon_tools">
                                                        <t t-set="hw" t-value="25"/>
                                                    </t>
                                                </span>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h4 class="my-1">Prime Snippet Builder Tool</h4>
                                                <h6 t-att-title="path" class="text-muted text-truncate fw-light mb-1">You're editing page: <t t-esc="title"/>
                                                </h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-8 d-flex align-items-center justify-content-end bg-100 py-2">
                                    <a t-att-href="videoURL" target="_blank" class="btn btn-light bg-white mx-3">
                                        <t t-call="theme_prime.icon_play_circle">
                                            <t t-set="hw" t-value="20"/>
                                        </t>
                                        <span class="ps-3 h6 fw-light">How it works?</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div t-if="!previewState.HasRecords" class="my-4 text-center bg-white shadow-sm overflow-hidden" style="height:600px;border-radius:15px;width:99%">
                    <img class="w-100" alt="phone" src="/theme_prime/static/src/img/browser.png"/>
                    <img class="img my-3 img-fluid mx-auto" style="max-height:400px;" src="/theme_prime/static/src/img/canvas.png"/>
                    <h4 class="mt-3 tp-text-body">Hey, You don't need to be a designer to configure the snippet.</h4>
                    <h6 class="small d-block mb-4 fw-light tp-text-body">Just activate options from the right panel as per your need and see the magic.</h6>
                </div>
            </div>
            <div class="col-3 ps-3 mt-4">
                <div class="position-absolute bg-white shadow-sm border overflow-hidden tp-rounded-border-lg" style="width:24%;right: 21px;height:calc(100% - 102px);">
                    <div class="overflow-auto h-100">
                        <!-- Hack -->
                        <nav>
                            <div class="nav nav-underline row g-0" id="pills-tab" style="gap:0;" role="tablist">
                                <t t-foreach="supportedComponents.components" t-as="tpComponent" t-key="tpComponent.name + previewState.templateID + previewState.activePricelist">
                                    <button t-attf-class="nav-link col py-3 #{tpComponent_index === 0 ? 'active' : 'border-start'} " data-bs-toggle="pill" t-attf-data-bs-target="##{tpComponent.name}" type="button" role="tab">
                                        <t t-if="tpComponent.name === 'TpRecordSelector'">
                                            <t t-call="theme_prime.icon_check_list">
                                                <t t-set="hw" t-value="18"/>
                                            </t>
                                            <span class="mt-1 ps-2 fw-normal text-uppercase">Selection</span>
                                        </t>
                                        <t t-if="tpComponent.name === 'TpUiComponent'">
                                            <t t-call="theme_prime.icon_brush">
                                                <t t-set="hw" t-value="18"/>
                                            </t>
                                            <span class="mt-1 ps-2 fw-normal text-uppercase">Design</span>
                                        </t>
                                        <t t-if="tpComponent.name === 'TpExtraOpts'">
                                            <t t-call="theme_prime.icon_setting">
                                                <t t-set="hw" t-value="18"/>
                                            </t>
                                            <span class="mt-1 ps-2 fw-normal text-uppercase">Extra</span>
                                        </t>
                                    </button>
                                </t>
                            </div>
                        </nav>
                        <div class="tab-content" id="pills-tabContent">
                            <t t-foreach="supportedComponents.components" t-as="tpComponent" t-key="tpComponent.name + previewState.templateID">
                                <div t-attf-class="tab-pane px-3 fade #{tpComponent_index === 0 ? 'show active' : ''}" t-att-id="tpComponent.name" role="tabpanel" tabindex="0">
                                    <t t-component="tpComponent.Component" t-props="tpComponent.props"/>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <t t-name="theme_prime.preview_record_selector">
        <div class="row py-3" style="background-color: #f2f7ff;">
            <t t-foreach="supportedComponents.components" t-as="tpComponent" t-key="tpComponent.name">
                <div class="col-12">
                    <t t-component="tpComponent.Component" t-props="tpComponent.props"/>
                </div>
            </t>
        </div>
    </t>

    <t t-name="theme_prime.snippetConfigDialog">
        <TpConfigDialog contentClass="contentClass" size="size" footer="footer" fullscreen="true" title="title" header="false">
            <div t-if="previewState.templateID">
                <t t-call="theme_prime.preview_renderer"/>
            </div>
            <div t-else=" ">
                <t t-call="theme_prime.preview_record_selector"/>
            </div>
            <t t-if="!previewState.templateID"  t-set-slot="footer">
                <t t-call="theme_prime.action_buttons_group"/>
            </t>
        </TpConfigDialog>
    </t>
</templates>
