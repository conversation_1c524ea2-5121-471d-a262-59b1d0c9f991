@mixin tp-s-banner-2-effect() {
    .tp-block-container {
        -webkit-transform: scale(1);
        transform: scale(1);
        .tp-block-content {
            &:before {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }
            .tp-title {
                -webkit-transform: translate3d(0,-50%,0) translate3d(0,-35px,0);
                transform: translate3d(0,-50%,0) translate3d(0,-35px,0);
            }
            .tp-subtitle {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }
        }
    }
}

.s_banner_2 {
    .tp-block-wrapper {
        overflow: hidden;
        .tp-block-container {
            min-height: 260px;
            background-position: center;
            background-size: cover;
            transform: scale(1.1);
            -webkit-transform: scale(1.1);
            -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
            transition: opacity 0.35s, transform 0.35s;
            .tp-block-content {
                @include o-position-absolute($top: 0, $left: 0);
                padding: 1.1rem;
                backface-visibility:hidden;
                .tp-title {
                    @include o-position-absolute($top: 45%, $left: 0);
                    color: white;
                    transition: transform 0.35s, color 0.35s;
                }
                .tp-subtitle {
                    @include o-position-absolute($bottom: 20px, $left: 0);
                    padding: 1.6rem;
                    color: white;
                    margin: 0 auto;
                    -webkit-transform: scale(1.3);
                    transform: scale(1.3);
                    opacity: 0;
                    -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
                    transition: opacity 0.35s, transform 0.35s;
                }
                &:before {
                    @include o-position-absolute($top: 20px, $left: 20px, $bottom: 20px,$right: 20px);
                    border: 1px solid #fff;
                    background-color: rgba(255,255,255,0.2);
                    content: '';
                    opacity: 0;
                    -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
                    -webkit-transform: scale(1.1);
                    transform: scale(1.1);
                }
            }
        }
        @include media-breakpoint-down(lg) {
            @include tp-s-banner-2-effect();
        }
        &:hover {
            @include tp-s-banner-2-effect();
        }
    }
}

body.editor_enable .s_banner_2 .tp-block-wrapper {
    @include tp-s-banner-2-effect();
}
