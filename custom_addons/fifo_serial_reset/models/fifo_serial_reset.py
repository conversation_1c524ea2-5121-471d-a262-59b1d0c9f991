from odoo import models, fields, api
from odoo.exceptions import UserError


class FIFORebuildWizard(models.TransientModel):
    _name = "fifo.serial.rebuild"
    _description = "One-time FIFO Serial Rebuilder"

    product_ids = fields.Many2many('product.product', string="Products")

    def action_rebuild_serials(self):
        self.ensure_one()
        Product = self.env['product.product']
        Lot = self.env['stock.lot']
        Quant = self.env['stock.quant']

        products = self.product_ids.filtered(lambda p: p.tracking == 'serial')
        if not products:
            raise UserError("No serial-tracked products selected.")

        serial_data = []

        for product in products:
            lots = Lot.search([
                ('product_id', '=', product.id),
                ('quant_ids.quantity', '>', 0.0)
            ], order='name ASC')

            for lot in lots:
                quant = Quant.search([
                    ('lot_id', '=', lot.id),
                    ('quantity', '>', 0.0)
                ], limit=1)

                if quant:
                    serial_data.append({
                        'product_id': product.id,
                        'serial_name': lot.name,
                        'location_id': quant.location_id.id,
                        'company_id': quant.company_id.id,
                        'lot_id': lot.id,
                        'quant_id': quant.id,
                    })

        if not serial_data:
            raise UserError("No in-stock serials found to rebuild.")

        # Step 1: Delete quants
        quant_ids = [d['quant_id'] for d in serial_data]
        Quant.sudo().browse(quant_ids).unlink()

        # Step 2: Archive lots (don't delete to avoid FK errors)
        lot_ids = [d['lot_id'] for d in serial_data]
        Lot.sudo().browse(lot_ids).write({'active': False})

        # Step 3: Recreate serials in FIFO order
        for data in serial_data:
            new_lot = Lot.create({
                'product_id': data['product_id'],
                'name': data['serial_name'],
                'company_id': data['company_id'],
            })

            Quant.create({
                'product_id': data['product_id'],
                'lot_id': new_lot.id,
                'location_id': data['location_id'],
                'quantity': 1.0,
                'company_id': data['company_id'],
            })
class StockLot(models.Model):
    _inherit = "stock.lot"

    active = fields.Boolean(default=True)
