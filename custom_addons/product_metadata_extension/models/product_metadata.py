from odoo import models, fields, api

# ① NEW MODEL
class OtherIdentifyingCode(models.Model):
    _name = 'product.other.identifying.code'
    _description = 'Other Identifying Codes/Numbers'
    _sql_constraints = [
        ('name_unique', 'unique(name)', 'Other Identifying Code must be unique.')
    ]

    name = fields.Char(required=True)
    product_ids = fields.Many2many('product.template', string="Products")


class OriginalPartNumber(models.Model):
    _name = 'product.original.part'
    _description = 'Original Manufacturer Part Number'
    _sql_constraints = [('name_unique', 'unique(name)', 'Original Manufacturer Part Number must be unique.')]

    name = fields.Char(required=True)
    product_ids = fields.Many2many('product.template', string="Products")


class EngineCode(models.Model):
    _name = 'product.engine.code'
    _description = 'Engine Code'
    _sql_constraints = [('name_unique', 'unique(name)', 'Engine Code must be unique.')]

    name = fields.Char(required=True)
    product_ids = fields.Many2many('product.template', string="Products")


class OENumber(models.Model):
    _name = 'product.oe.number'
    _description = 'OE Number'
    _sql_constraints = [('name_unique', 'unique(name)', 'OE Number must be unique.')]

    name = fields.Char(required=True)
    product_ids = fields.Many2many('product.template', string="Products")


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    original_part_ids = fields.Many2many('product.original.part', string='Original Manufacturer Part Numbers')
    engine_code_ids = fields.Many2many('product.engine.code', string='Engine Codes')
    oe_number_ids = fields.Many2many('product.oe.number', string='OE Numbers')
    other_identifying_code_ids = fields.Many2many('product.other.identifying.code', string='Other Identifying Codes')

    technical_spec_summary = fields.Char(
        string='Technical Specs',
        compute='_compute_technical_spec_summary',
        store=True,
        searchable=True
    )

    @api.depends(
        'original_part_ids.name',
        'engine_code_ids.name',
        'oe_number_ids.name',
        'other_identifying_code_ids.name'
    )
    def _compute_technical_spec_summary(self):
        for rec in self:
            values = (
                rec.original_part_ids.mapped('name') +
                rec.engine_code_ids.mapped('name') +
                rec.oe_number_ids.mapped('name') +
                rec.other_identifying_code_ids.mapped('name')
            )
            rec.technical_spec_summary = ', '.join(sorted(set(values)))

    def _sync_metadata_to_tags(self):
        for product in self:
            tag_names = set(product.product_tag_ids.mapped('name'))
            metadata_names = set(
                product.original_part_ids.mapped('name') +
                product.engine_code_ids.mapped('name') +
                product.oe_number_ids.mapped('name') +
                product.other_identifying_code_ids.mapped('name')
            )

            preserved_tags = product.env.context.get('preserve_tags', set())

            for name in metadata_names - tag_names:
                tag = self.env['product.tag'].search([('name', '=', name)], limit=1)
                if not tag:
                    tag = self.env['product.tag'].create({'name': name})
                product.product_tag_ids |= tag

            for tag in product.product_tag_ids:
                if tag.name not in metadata_names and tag.name not in preserved_tags:
                    product.product_tag_ids -= tag

    def write(self, vals):
        res = super().write(vals)
        self.with_context(preserve_tags=set(self.mapped('product_tag_ids.name')))._sync_metadata_to_tags()
        return res

    @api.model
    def create(self, vals):
        record = super().create(vals)
        record._sync_metadata_to_tags()
        return record


class ProductProduct(models.Model):
    _inherit = 'product.product'

    original_part_ids = fields.Many2many(related='product_tmpl_id.original_part_ids', readonly=False)
    engine_code_ids = fields.Many2many(related='product_tmpl_id.engine_code_ids', readonly=False)
    oe_number_ids = fields.Many2many(related='product_tmpl_id.oe_number_ids', readonly=False)
    other_identifying_code_ids = fields.Many2many(related='product_tmpl_id.other_identifying_code_ids', readonly=False)
