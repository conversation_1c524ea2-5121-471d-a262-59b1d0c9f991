<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="product_template_search_view_inherit_metadata" model="ir.ui.view">
        <field name="name">product.template.search.metadata.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">

                <!-- Default selected search field -->
                <field name="technical_spec_summary" string="All"/>

                <!-- Dropdown searchable fields -->
                <field name="original_part_ids" string="Original Part Numbers"/>
                <field name="engine_code_ids" string="Engine Codes"/>
                <field name="oe_number_ids" string="OE Numbers"/>
                <field name="other_identifying_code_ids" string="Other Identifying Codes"/>

                <group expand="0" string="Technical Specs">
                    <filter
                        name="technical_specs_filter"
                        string="Has Technical Specs"
                        domain="['|', '|', '|', 
                            ('original_part_ids', '!=', False), 
                            ('engine_code_ids', '!=', False), 
                            ('oe_number_ids', '!=', False), 
                            ('other_identifying_code_ids', '!=', False)
                        ]"
                    />
                </group>
            </xpath>
        </field>
    </record>
</odoo>
