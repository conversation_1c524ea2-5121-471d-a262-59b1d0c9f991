# -*- coding: utf-8 -*-
# Copyright (c) 2019-Present <PERSON><PERSON><PERSON> Infotech Private Limited. (<https://www.droggol.com/>)

from odoo import api, models


class DrCacheMixin(models.AbstractModel):
    _name = 'dr.cache.mixin'
    _description = 'Cache Mixin'

    @api.model_create_multi
    def create(self, vals_list):
        self.env.registry.clear_cache()
        return super().create(vals_list)

    def write(self, vals):
        self.env.registry.clear_cache()
        return super().write(vals)

    def unlink(self):
        self.env.registry.clear_cache()
        return super().unlink()
