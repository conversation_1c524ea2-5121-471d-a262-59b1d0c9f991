<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_pricelist_item_form_view_inherit_droggol_theme_common" model="ir.ui.view">
        <field name="name">product.pricelist.item.form.inherit.droggol.theme.common</field>
        <field name="model">product.pricelist.item</field>
        <field name="inherit_id" ref="product.product_pricelist_item_form_view"/>
        <field name="arch" type="xml">
            <group name="pricelist_rule_limits" position="inside">
                <field name="dr_offer_msg" invisible="not date_end or applied_on == '0_product_variant'" required="date_end"/>
                <field name="dr_offer_finish_msg" invisible="not date_end or applied_on == '0_product_variant'" required="date_end"/>
            </group>
        </field>
    </record>

    <record id="website_sale_pricelist_form_view_inherit_droggol_theme_common" model="ir.ui.view">
        <field name="name">product.pricelist.form.inherit.droggol.theme.common</field>
        <field name="model">product.pricelist</field>
        <field name="inherit_id" ref="website_sale.website_sale_pricelist_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='item_ids']/list/field[@name='date_end']" position="after">
                <field name="dr_offer_msg" optional="hide" required="date_end"/>
                <field name="dr_offer_finish_msg" optional="hide" required="date_end"/>
            </xpath>
        </field>
    </record>

</odoo>
