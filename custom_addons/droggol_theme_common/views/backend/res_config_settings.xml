<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.droggol.theme.common</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="website_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_website_create_new']" position="after">
                <button name="dr_open_theme_custom_modules" type="object" string="Theme Customizations" class="btn-info ms-2" icon="fa-cog" invisible="not dr_has_custom_module"/>
                <field name="dr_has_custom_module" invisible="1"/> <!-- Need for domain  -->
            </xpath>
            <block id="sale_product_catalog_settings" position="inside">
                <setting id="discount_sale_order_lines" title="Apply manual discounts on sales order lines or display discounts computed from pricelists (option to activate in the pricelist configuration)." help="Grant discounts on sales order lines">
                    <field name="group_discount_per_so_line"/>
                </setting>
            </block>
            <block id="sale_product_catalog_settings" position="after">
                <block title="Progressive Web Apps (PWA)" id="droggol_theme_common_pwa_settings">
                    <div class="row">
                        <setting id="dr_pwa_activated" string="Enable Progressive Web Apps" help="Allows users to install your website as an application on mobile and desktop.">
                            <field name="dr_pwa_activated"/>
                        </setting>
                    </div>
                    <setting id="dr_pwa_name" string="Name" help="Used in the app install prompt." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_name" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_short_name" string="Short Name" help="Used on the user's home screen, launcher, or other places." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_short_name" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_background_color" string="Background Color" help="Used on the splash screen when the application is first launched." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_background_color" widget="color" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_theme_color" string="Theme Color" help="Used on toolbar, and may be in the app's preview in task switchers." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_theme_color" widget="color" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_icon_192" string="Icon (192x192)" help="These icons are used in places like the home screen, app launcher, task switcher, splash screen, etc." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_icon_192" widget="image" options="{'size': [90, 90]}" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_icon_512" string="Icon (512x512)" help="These icons are used in places like the home screen, app launcher, task switcher, splash screen, etc." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_icon_512" widget="image" options="{'size': [90, 90]}" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_start_url" string="Start URL" help="Your application will start from this URL when it is launched." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_start_url" required="dr_pwa_activated"/>
                    </setting>
                    <setting id="dr_pwa_offline_page" string="Enable Offline Page" help="Show offline page when there is no internet." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_offline_page"/>
                    </setting>
                    <setting id="dr_pwa_show_install_banner" string="Show Custom Install Banner" help="Prompt the user to install the PWA with a custom banner." invisible="not dr_pwa_activated">
                        <field name="dr_pwa_show_install_banner"/>
                    </setting>
                    <setting id="dr_pwa_screenshots" string="Screenshots" help="Show what the app looks like and what it can do." invisible="not dr_pwa_activated">
                        <button type="object" name="dr_open_pwa_screenshots" string="Configure Screenshots" class="btn-link" icon="fa-cogs me-1"/>
                    </setting>
                    <setting id="dr_pwa_shortcuts" string="App Shortcuts" help="App shortcuts help users quickly start common or recommended tasks within your web app." invisible="not dr_pwa_activated">
                        <button type="object" name="dr_open_pwa_shortcuts" string="Configure Shortcuts" class="btn-link" icon="fa-cogs me-1"/>
                    </setting>
                </block>
            </block>
        </field>
    </record>

</odoo>
