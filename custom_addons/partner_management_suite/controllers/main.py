from odoo import http, fields
from odoo.http import request
import logging

_logger = logging.getLogger(__name__)

class ContactVerificationController(http.Controller):

    def _is_verification_disabled(self):
        return request.env['ir.config_parameter'].sudo().get_param('partner_management_suite.disable_verification_globally')

    @http.route('/verify/contact/accept/<int:partner_id>', type='http', auth='public', website=True)
    def approve_new_contact(self, partner_id, **kwargs):
        if self._is_verification_disabled():
            return request.render('partner_management_suite.verification_disabled')

        partner = request.env['res.partner'].sudo().browse(partner_id)
        if partner and partner.is_pending_contact:
            partner.sudo().write({
                'is_pending_contact': False,
                'is_verified_contact': True,
            })
            request.env['contact.verification.log'].sudo().create({
                'partner_id': partner.id,
                'action_type': 'verified',
                'status': 'success',
                'timestamp': fields.Datetime.now(),
            })
            return request.render('partner_management_suite.verification_success', {'partner': partner})
        else:
            return request.render('partner_management_suite.verification_expired')

    @http.route('/verify/contact/reject/<int:partner_id>', type='http', auth='public', website=True)
    def reject_new_contact(self, partner_id, **kwargs):
        if self._is_verification_disabled():
            return request.render('partner_management_suite.verification_disabled')

        partner = request.env['res.partner'].sudo().browse(partner_id)
        if partner and partner.is_pending_contact:
            request.env['contact.verification.log'].sudo().create({
                'partner_id': partner.id,
                'action_type': 'rejected',
                'status': 'rejected',
                'timestamp': fields.Datetime.now(),
            })
            try:
                partner.sudo().unlink()
            except Exception:
                pass
            return request.render('partner_management_suite.verification_success', {'partner': None})
        else:
            return request.render('partner_management_suite.verification_expired')

    @http.route('/verify/change/<string:token>', type='http', auth='public', website=True)
    def view_change_request(self, token, **kwargs):
        if self._is_verification_disabled():
            return request.render('partner_management_suite.verification_disabled')

        change_request = request.env['contact.change.request'].sudo().search([
            ('token', '=', token),
            ('status', '=', 'pending')
        ], limit=1)

        if not change_request or change_request.expiry_datetime < fields.Datetime.now():
            return request.render('partner_management_suite.verification_expired')

        template_map = {
            'edit': 'partner_management_suite.change_request_landing_edit',
            'create_sub': 'partner_management_suite.change_request_landing_create_sub',
            'assign_master': 'partner_management_suite.change_request_landing_assign_master',
            'remove_master': 'partner_management_suite.change_request_landing_remove_master',
            'assign_accountant': 'partner_management_suite.change_request_landing_assign_accountant',
            'remove_accountant': 'partner_management_suite.change_request_landing_remove_accountant',
            'assign_driver': 'partner_management_suite.change_request_landing_assign_driver',
            'remove_driver': 'partner_management_suite.change_request_landing_remove_driver',
            'assign_sub': 'partner_management_suite.change_request_landing_assign_sub',
            'remove_sub': 'partner_management_suite.change_request_landing_remove_sub',
        }
        return request.render(template_map.get(change_request.trigger_type, 'partner_management_suite.verification_expired'), {
            'change_request': change_request
        })

    @http.route('/verify/change/accept/<string:token>', type='http', auth='public', website=True)
    def approve_change_request(self, token, **kwargs):
        if self._is_verification_disabled():
            return request.render('partner_management_suite.verification_disabled')

        change_request = request.env['contact.change.request'].sudo().search([
            ('token', '=', token),
            ('status', '=', 'pending')
        ], limit=1)

        if change_request and change_request.expiry_datetime > fields.Datetime.now():
            partner = change_request.partner_id
            change_data = change_request.change_data or {}

            if change_request.trigger_type == 'create_sub':
                partner.sudo().write({
                    'active': True,
                    'is_pending_contact': False,
                })

            elif change_request.trigger_type in ['assign_master', 'assign_accountant', 'assign_driver', 'assign_debtors', 'assign_creditors']:
                vals = {}
                if 'is_master_contact' in change_data:
                    vals['is_master_contact'] = True
                if 'is_accountant_contact' in change_data:
                    vals['is_accountant_contact'] = True
                if 'is_driver_contact' in change_data:
                    vals['is_driver_contact'] = True
                if 'driver_id_number' in change_data:
                    vals['driver_id_number'] = change_data['driver_id_number']
                if 'is_debtors_contact' in change_data:
                    vals['is_debtors_contact'] = True
                if 'is_creditors_contact' in change_data:
                    vals['is_creditors_contact'] = True
                if vals:
                    partner.sudo().with_context(skip_role_verification=True).write(vals)

            elif change_request.trigger_type in ['remove_master', 'remove_accountant', 'remove_driver', 'remove_debtors', 'remove_creditors']:
                vals = {}
                if 'is_master_contact' in change_data:
                    vals['is_master_contact'] = False
                if 'is_accountant_contact' in change_data:
                    vals['is_accountant_contact'] = False
                if 'is_driver_contact' in change_data:
                    vals['is_driver_contact'] = False
                if 'is_debtors_contact' in change_data:
                    vals['is_debtors_contact'] = False
                if 'is_creditors_contact' in change_data:
                    vals['is_creditors_contact'] = False
                if vals:
                    partner.sudo().with_context(skip_role_verification=True).write(vals)

            elif change_request.trigger_type == 'assign_sub':
                subcontact = request.env['res.partner'].sudo().browse(change_data.get('subcontact_id'))
                new_parent = request.env['res.partner'].sudo().browse(change_data.get('new_parent_id'))
                if subcontact and new_parent:
                    subcontact.sudo().with_context(
                        skip_subcontact_verification=True,
                        skip_sensitive_verification=True,
                        skip_role_verification=True
                    ).write({'parent_id': new_parent.id})

            elif change_request.trigger_type == 'remove_sub':
                subcontact = request.env['res.partner'].sudo().browse(change_data.get('subcontact_id'))
                if subcontact:
                    subcontact.sudo().with_context(
                        skip_subcontact_verification=True,
                        skip_sensitive_verification=True,
                        skip_role_verification=True
                    ).write({
                        'parent_id': False,
                        'email': False,
                        'phone': False,
                        'mobile': False,
                        'driver_id_number': False,
                        'title': False,
                        'function': False,
                        'street': False,
                        'street2': False,
                        'zip': False,
                        'city': False,
                        'state_id': False,
                        'country_id': False,
                        'vat': False,
                        'company_type': 'person',
                    })

            else:
                partner.sudo().with_context(
                    skip_subcontact_verification=True,
                    skip_sensitive_verification=True,
                    skip_role_verification=True
                ).write(change_data)

            change_request.sudo().write({'status': 'approved'})

            request.env['contact.verification.log'].sudo().create({
                'partner_id': partner.id,
                'action_type': 'verified',
                'old_value': change_request.old_value,
                'new_value': change_request.new_value,
                'status': 'success',
                'timestamp': fields.Datetime.now(),
            })

            return request.render('partner_management_suite.verification_success', {
                'partner': partner,
            })

        else:
            return request.render('partner_management_suite.verification_expired')

    @http.route('/verify/change/reject/<string:token>', type='http', auth='public', website=True)
    def reject_change_request(self, token, **kwargs):
        if self._is_verification_disabled():
            return request.render('partner_management_suite.verification_disabled')

        request.env.cr.rollback()
        request.env.cr.execute('SAVEPOINT reject_start')

        try:
            change_request = request.env['contact.change.request'].sudo().search([
                ('token', '=', token),
                ('status', '=', 'pending')
            ], limit=1)

            if not change_request or not change_request.id:
                return request.render('partner_management_suite.verification_expired')

            if change_request.expiry_datetime < fields.Datetime.now():
                return request.render('partner_management_suite.verification_expired')

            partner = change_request.partner_id

            change_request.sudo().write({'status': 'rejected'})
            request.env['contact.verification.log'].sudo().create({
                'partner_id': partner.id if partner else False,
                'action_type': 'rejected',
                'old_value': change_request.old_value,
                'new_value': change_request.new_value,
                'status': 'rejected',
                'timestamp': fields.Datetime.now(),
            })

            return request.render('partner_management_suite.verification_rejected', {
                'partner': partner,
            })

        except Exception as e:
            request.env.cr.execute('ROLLBACK TO SAVEPOINT reject_start')
            _logger.error(f"\ud83d\udd25 Rejection error: {str(e)}", exc_info=True)
            return request.render('partner_management_suite.verification_expired')