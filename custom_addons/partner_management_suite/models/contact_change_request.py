from odoo import models, fields, api
from odoo.exceptions import AccessError
import uuid
import logging

_logger = logging.getLogger(__name__)

class ContactChangeRequest(models.Model):
    _name = 'contact.change.request'
    _inherit = ['mail.thread']
    _description = 'Pending Contact Change Request'
    _order = 'expiry_datetime desc'
    
    disable_contact_verification_globally = fields.Boolean(
        string="Disable Contact Verification Globally",
        config_parameter='partner_management_suite.disable_verification_globally'
    )

    partner_id = fields.Many2one('res.partner', required=True, ondelete='cascade')
    company_id = fields.Many2one(
        'res.company',
        string="Company",
        readonly=True,
        default=lambda self: self.env.company,
        index=True
    )
    field_name = fields.Char()
    old_value = fields.Text()
    new_value = fields.Text()
    change_data = fields.Json()
    trigger_type = fields.Selection([
        ('edit', 'Edit Contact'),
        ('create_sub', 'Create Sub-Contact'),
        ('assign_master', 'Assign Master Contact'),
        ('remove_master', 'Remove Master Contact'),
        ('assign_accountant', 'Assign Accountant Contact'),
        ('remove_accountant', 'Remove Accountant Contact'),
        ('assign_debtors', 'Assign Debtors Contact'),
        ('remove_debtors', 'Remove Debtors Contact'),
        ('assign_creditors', 'Assign Creditors Contact'),
        ('remove_creditors', 'Remove Creditors Contact'),
        ('assign_driver', 'Assign Driver Contact'),
        ('remove_driver', 'Remove Driver Contact'),
        ('assign_sub', 'Assign Sub-Contact'),
        ('remove_sub', 'Remove Sub-Contact'),
    ], required=True)
    token = fields.Char(string="Verification Token", required=True, default=lambda self: str(uuid.uuid4()))
    expiry_datetime = fields.Datetime(string="Expiry Time", required=True)
    status = fields.Selection([
        ('pending', 'Pending Verification'),
        ('approved', 'Approved'),
        ('expired', 'Expired'),
        ('rejected', 'Rejected')
    ], default='pending', required=True)

    show_action_buttons = fields.Boolean(compute="_compute_show_action_buttons", store=False)

    @api.model_create_multi
    def create(self, vals_list):
        if self.env['ir.config_parameter'].sudo().get_param('partner_management_suite.disable_verification_globally'):
            _logger.info("⚠️ Verification globally disabled. Skipping creation of contact change request.")
            return self.env['contact.change.request']

        for vals in vals_list:
            if not vals.get('token'):
                vals['token'] = str(uuid.uuid4())
        return super().create(vals_list)

    def _send_verification_email(self):
        self.ensure_one()

        if self.env['ir.config_parameter'].sudo().get_param('partner_management_suite.disable_verification_globally'):
            _logger.info(f"⚠️ Skipping email for Change Request {self.id}: Verification disabled globally.")
            return

        if not self.partner_id:
            _logger.warning(f"⚠️ Missing partner on change request {self.id}")
            return

        recipient_email = None

        if self.trigger_type in ['create_sub', 'assign_sub', 'remove_sub']:
            parent = None
            if self.trigger_type == 'assign_sub':
                parent_id = self.change_data.get('new_parent_id')
                parent = self.env['res.partner'].browse(parent_id) if parent_id else None
            else:
                parent = self.partner_id.parent_id

            master_contact = None
            if parent:
                master_contact = parent.child_ids.filtered(lambda c: c.is_master_contact and c.email)
                master_contact = master_contact[0] if master_contact else None

            recipient_email = (
                (master_contact and master_contact.email)
                or (parent and parent.email)
            )

        elif self.trigger_type in [
            'assign_master', 'remove_master',
            'assign_accountant', 'remove_accountant',
            'assign_driver', 'remove_driver',
            'assign_debtors', 'remove_debtors',
            'assign_creditors', 'remove_creditors',
        ]:
            company = self.partner_id.parent_id or (self.partner_id if self.partner_id.is_company else None)

            master_contact = None
            if company:
                master_contact = company.child_ids.filtered(lambda c: c.is_master_contact and c.email)
                master_contact = master_contact[0] if master_contact else None

            if self.trigger_type in ['assign_master', 'remove_master']:
                recipient_email = company.email if company else None
            else:
                recipient_email = (
                    (master_contact and master_contact.email)
                    or (company and company.email)
                )

        elif self.partner_id.email:
            recipient_email = self.partner_id.email

        if not recipient_email:
            log_message = f"⚠️ Cannot send verification email for Change Request {self.id}: No valid recipient email."
            _logger.warning(log_message)
            self.message_post(body=log_message)
            return

        template_id = False
        if self.trigger_type == 'edit':
            template_id = self.env.ref('partner_management_suite.contact_change_verification_email_template', raise_if_not_found=False)
        elif self.trigger_type in ['create_sub', 'assign_sub', 'remove_sub']:
            template_id = self.env.ref('partner_management_suite.sub_contact_creation_verification_email_template', raise_if_not_found=False)
        elif self.trigger_type in [
            'assign_master', 'remove_master',
            'assign_accountant', 'remove_accountant',
            'assign_driver', 'remove_driver',
            'assign_debtors', 'remove_debtors',
            'assign_creditors', 'remove_creditors',
        ]:
            template_id = self.env.ref('partner_management_suite.master_contact_assignment_verification_email_template', raise_if_not_found=False)

        if not template_id:
            _logger.warning(f"⚠️ No email template found for Change Request {self.id} (Trigger Type: {self.trigger_type}).")
            self.message_post(body="⚠️ No email template found for this verification request.")
            return

        try:
            template_id.send_mail(self.id, force_send=True, email_values={'email_to': recipient_email})
            _logger.info(f"✅ Email triggered successfully for Change Request {self.id} to {recipient_email}.")
            self.message_post(body=f"✅ Email triggered successfully to [{recipient_email}].")
        except Exception as e:
            _logger.error(f"❌ Failed to send email for Change Request {self.id} to {recipient_email}. Error: {str(e)}")
            self.message_post(body=f"❌ Failed to send email to [{recipient_email}]. Error: {str(e)}")

    @api.depends('status')
    def _compute_show_action_buttons(self):
        for rec in self:
            rec.show_action_buttons = rec.status == 'pending'

    @api.model
    def _cron_expire_changes(self):
        expired_requests = self.search([
            ('expiry_datetime', '<', fields.Datetime.now()),
            ('status', '=', 'pending')
        ])
        for rec in expired_requests:
            rec.write({'status': 'expired'})
            self.env['contact.verification.log'].create({
                'partner_id': rec.partner_id.id,
                'action_type': 'field_change',
                'status': 'expired',
                'timestamp': fields.Datetime.now(),
                'old_value': rec.old_value,
                'new_value': rec.new_value,
            })

    def action_admin_approve(self):
        self.ensure_one()

        if not self.env.user.has_group('partner_management_suite.group_contact_change_manager'):
            raise AccessError("Only Contact Change Managers can approve requests.")

        if self.status != 'pending':
            return

        if self.trigger_type == 'edit':
            self.partner_id.sudo().with_context(
                skip_sensitive_verification=True,
                skip_subcontact_verification=True,
                skip_role_verification=True
            ).write(self.change_data)

        elif self.trigger_type == 'create_sub':
            sub_contact = self.partner_id
            if sub_contact and sub_contact.is_pending_contact and not sub_contact.active:
                update_vals = {'active': True, 'is_pending_contact': False}

                role_fields = [
                    'is_master_contact', 'is_accountant_contact',
                    'is_debtors_contact', 'is_creditors_contact',
                    'is_driver_contact', 'driver_id_number'
                ]
                for field in role_fields:
                    if field in self.change_data:
                        update_vals[field] = self.change_data[field]

                sub_contact.sudo().with_context(
                    skip_role_verification=True,
                    skip_sensitive_verification=True,
                    skip_subcontact_verification=True
                ).write(update_vals)

        elif self.trigger_type == 'assign_sub':
            subcontact_id = self.change_data.get('subcontact_id')
            new_parent_id = self.change_data.get('new_parent_id')
            if subcontact_id and new_parent_id:
                subcontact = self.env['res.partner'].browse(subcontact_id)
                subcontact.sudo().with_context(
                    skip_subcontact_verification=True,
                    skip_sensitive_verification=True,
                    skip_role_verification=True
                ).write({
                    'parent_id': new_parent_id
                })

        elif self.trigger_type == 'remove_sub':
            subcontact_id = self.change_data.get('subcontact_id')
            if subcontact_id:
                subcontact = self.env['res.partner'].browse(subcontact_id)
                subcontact.sudo().with_context(
                    skip_subcontact_verification=True,
                    skip_sensitive_verification=True,
                    skip_role_verification=True
                ).write({
                    'parent_id': False,
                    'email': False,
                    'phone': False,
                    'mobile': False,
                    'driver_id_number': False,
                    'title': False,
                    'function': False,
                    'street': False,
                    'street2': False,
                    'zip': False,
                    'city': False,
                    'state_id': False,
                    'country_id': False,
                    'vat': False,
                    'company_type': 'person',
                })

        elif self.trigger_type == 'assign_driver':
            if self.partner_id:
                update_vals = {
                    'is_driver_contact': True,
                }
                if 'driver_id_number' in self.change_data:
                    update_vals['driver_id_number'] = self.change_data['driver_id_number']

                self.partner_id.sudo().with_context(
                    skip_role_verification=True,
                    skip_sensitive_verification=True
                ).write(update_vals)

        elif self.trigger_type in ['assign_master', 'assign_accountant', 'assign_debtors', 'assign_creditors']:
            if self.partner_id:
                role_field = f'is_{self.trigger_type.split("_")[1]}_contact'
                self.partner_id.sudo().with_context(
                    skip_role_verification=True,
                    skip_sensitive_verification=True
                ).write({role_field: True})

        elif self.trigger_type in ['remove_master', 'remove_accountant', 'remove_debtors', 'remove_creditors', 'remove_driver']:
            if self.partner_id:
                role_field = f'is_{self.trigger_type.split("_")[1]}_contact'
                self.partner_id.sudo().with_context(
                    skip_role_verification=True,
                    skip_sensitive_verification=True
                ).write({role_field: False})

        self.status = 'approved'
        self.message_post(body=f"✅ Contact Change manually APPROVED by {self.env.user.name}.")

    def action_admin_verify(self):
        self.ensure_one()
        if not self.env.user.has_group('partner_management_suite.group_contact_change_manager'):
            raise AccessError("Only Contact Change Managers can verify requests.")
        if self.status != 'pending':
            return
        self.action_admin_approve()
        self.message_post(body=f"✅ Contact Change manually VERIFIED by {self.env.user.name}.")

    def action_admin_reject(self):
        self.ensure_one()

        if not self.env.user.has_group('partner_management_suite.group_contact_change_manager'):
            raise AccessError("Only Contact Change Managers can reject requests.")

        if self.status != 'pending':
            return

        partner = self.partner_id

        self.sudo().write({'status': 'rejected'})
        self.env['contact.verification.log'].sudo().create({
            'partner_id': partner.id if partner else False,
            'action_type': 'rejected',
            'old_value': self.old_value,
            'new_value': self.new_value,
            'status': 'rejected',
            'timestamp': fields.Datetime.now(),
        })

        if self.trigger_type == 'create_sub' and partner and partner.is_pending_contact and not partner.active:
            try:
                partner.sudo().unlink()
            except Exception as e:
                _logger.warning(f"Unable to unlink sub-contact {partner.id} during manual reject: {e}")

        self.message_post(body=f"❌ Contact Change manually REJECTED by {self.env.user.name}.")

    def _get_approval_url(self):
        self.ensure_one()
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url') or 'http://localhost:8069'
        return f"{base_url}/verify/change/accept/{self.token}"

    def _get_rejection_url(self):
        self.ensure_one()
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url') or 'http://localhost:8069'
        return f"{base_url}/verify/change/reject/{self.token}"
