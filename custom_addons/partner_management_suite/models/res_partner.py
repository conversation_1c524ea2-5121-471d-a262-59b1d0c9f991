from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import ValidationError, UserError
from datetime import timedelta
import uuid
import logging

_logger = logging.getLogger(__name__)


class SupplierType(models.Model):
    _name = 'res.partner.supplier.type'
    _description = 'Supplier Type'

    name = fields.Char(required=True)


class ResPartner(models.Model):
    _inherit = 'res.partner'
    
    def _is_partner_management_enabled(self):
        param = self.env['ir.config_parameter'].sudo().get_param('partner_management_suite.enabled_company_ids', '')
        enabled_ids = [int(x) for x in param.split(',') if x.strip()]
        return self.env.company.id in enabled_ids

    SENSITIVE_FIELDS = [
        'name', 'email', 'phone', 'mobile', 'vat', 'street', 'city', 'zip', 'driver_id_number'
    ]
    
    is_partner_suite_enabled = fields.Boolean(compute="_compute_suite_enabled", store=False)
    
    def _compute_suite_enabled(self):
        enabled_ids = self.env['ir.config_parameter'].sudo().get_param(
            'partner_management_suite.enabled_company_ids', ''
        )
        enabled_ids = [int(x) for x in enabled_ids.split(',') if x.strip().isdigit()]

        for rec in self:
            # Check against the company the contact belongs to
            rec_company = rec.company_id.id if rec.company_id else rec.env.company.id
            rec.is_partner_suite_enabled = rec_company in enabled_ids

    # === GLOBAL VERIFICATION DISABLE FLAG ===
    @api.model
    def _is_verification_disabled(self):
        return self.env['ir.config_parameter'].sudo().get_param('partner_management_suite.disable_verification_globally')

    show_subcontact_verification_warning = fields.Boolean(default=False)

    market_segment = fields.Selection([
        ('auto', 'Auto'), ('commercial', 'Commercial'), ('off_highway', 'Off-Highway'), ('components', 'Components'), ('internal', 'Internal'),
    ], string="Market Segment")

    distribution_group_id = fields.Selection([
        ('national_distributor', 'National Distributor'), ('wholesaler', 'Wholesaler'), ('retail', 'Retail'),
        ('workshop', 'Workshop'), ('fleet', 'Fleet'), ('turbo', 'Turbo'), ('diesel', 'Diesel'),
        ('electrical', 'Electrical'), ('auto_engineer', 'Auto Engineer'), ('internal_reman', 'Internal Reman'), ('external_repairs', 'External Repairs'),
    ], string="Distribution Group")

    customer_grading = fields.Selection([
        ('a', 'A'), ('b', 'B'), ('c', 'C'), ('d', 'D'), ('e', 'E'), ('cod', 'C.O.D'), ('competitor', 'Competitor'), ('autozone', 'Autozone'), ('alert', 'Alert'),
    ], string="Customer Grading")

    has_customer_info = fields.Boolean(compute="_compute_has_customer_info", store=True)
    supplier_of_ids = fields.Many2many(
        'res.partner.supplier.type',
        'partner_supplier_type_rel',
        'partner_id',
        'supplier_type_id',
        string="Supplier Of"
    )
    has_supplier_info = fields.Boolean(compute="_compute_has_supplier_info", store=True)

    competitor_1_id = fields.Many2one('res.partner', string="Competitor 1")
    competitor_2_id = fields.Many2one('res.partner', string="Competitor 2")
    competitor_3_id = fields.Many2one('res.partner', string="Competitor 3")
    competitor_4_id = fields.Many2one('res.partner', string="Competitor 4")
    competitor_5_id = fields.Many2one('res.partner', string="Competitor 5")

    is_credit_blocked = fields.Boolean(string="Manually Blocked for Credit", default=False)
    show_credit_block_buttons = fields.Boolean(compute="_compute_show_credit_buttons", store=False)

    is_master_contact = fields.Boolean(string='Master Contact')
    is_accountant_contact = fields.Boolean(string='Accountant Contact')
    is_debtors_contact = fields.Boolean(string='Debtors Account Contact')
    is_creditors_contact = fields.Boolean(string='Creditors Account Contact')
    is_driver_contact = fields.Boolean(string='Driver Contact')
    is_pending_contact = fields.Boolean(default=False)
    is_verified_contact = fields.Boolean(default=False)
    show_verified_badge = fields.Boolean(string="Show Verified Badge", compute="_compute_badge_flags", store=True)
    show_master_badge = fields.Boolean(string="Show Master Badge", compute="_compute_badge_flags", store=True)

    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=False)

    master_contact_id = fields.Many2one(
        'res.partner',
        string='Master Contact',
        domain="[('parent_id', '=', False), ('is_company', '=', False)]"
    )
    _pending_parent_id = fields.Many2one(
        'res.partner',
        string="Pending Parent ID",
        copy=False
    )

    driver_id_number = fields.Char(string="Driver ID Number")
    contact_type_label = fields.Char(string='Contact Type', compute='_compute_contact_type_label', store=False)
    is_master_contact_visible = fields.Boolean(compute='_compute_contact_role_visibility', store=False)
    is_accountant_contact_visible = fields.Boolean(compute='_compute_contact_role_visibility', store=False)
    is_debtors_contact_visible = fields.Boolean(compute='_compute_contact_role_visibility', store=False)
    is_creditors_contact_visible = fields.Boolean(compute='_compute_contact_role_visibility', store=False)
    is_driver_contact_visible = fields.Boolean(compute='_compute_contact_role_visibility', store=False)
    branch_name = fields.Char(string="Branch Name")

    # --- Role-specific filtered child fields ---
    master_child_ids = fields.One2many(
        'res.partner', 'parent_id',
        string="Master Contacts",
        domain=[('is_master_contact', '=', True),
                ('is_accountant_contact', '=', False),
                ('is_debtors_contact', '=', False),
                ('is_creditors_contact', '=', False),
                ('is_driver_contact', '=', False)]
    )

    accountant_child_ids = fields.One2many(
        'res.partner', 'parent_id',
        string="Accountant Contacts",
        domain=[('is_accountant_contact', '=', True),
                ('is_master_contact', '=', False),
                ('is_debtors_contact', '=', False),
                ('is_creditors_contact', '=', False),
                ('is_driver_contact', '=', False)]
    )

    debtors_child_ids = fields.One2many(
        'res.partner', 'parent_id',
        string="Debtors Contacts",
        domain=[('is_debtors_contact', '=', True),
                ('is_master_contact', '=', False),
                ('is_accountant_contact', '=', False),
                ('is_creditors_contact', '=', False),
                ('is_driver_contact', '=', False)]
    )

    creditors_child_ids = fields.One2many(
        'res.partner', 'parent_id',
        string="Creditors Contacts",
        domain=[('is_creditors_contact', '=', True),
                ('is_master_contact', '=', False),
                ('is_accountant_contact', '=', False),
                ('is_debtors_contact', '=', False),
                ('is_driver_contact', '=', False)]
    )

    driver_child_ids = fields.One2many(
        'res.partner', 'parent_id',
        string="Driver Contacts",
        domain=[('is_driver_contact', '=', True),
                ('is_master_contact', '=', False),
                ('is_accountant_contact', '=', False),
                ('is_debtors_contact', '=', False),
                ('is_creditors_contact', '=', False)]
    )

    other_child_ids = fields.One2many(
        'res.partner', 'parent_id',
        string="Other Contacts",
        domain=[('is_master_contact', '=', False),
                ('is_accountant_contact', '=', False),
                ('is_debtors_contact', '=', False),
                ('is_creditors_contact', '=', False),
                ('is_driver_contact', '=', False)]
    )

    def get_accountant_email(self):
        """
        Return the email address of the accountant contact if assigned.
        """
        if self.parent_id:
            accountant = self.parent_id.child_ids.filtered(lambda p: p.is_accountant_contact and p.email)
            return accountant[0].email if accountant else None
        elif self.is_company:
            accountant = self.child_ids.filtered(lambda p: p.is_accountant_contact and p.email)
            return accountant[0].email if accountant else None
        return None

    def message_get_reply_to(self, default=None):
        """
        Override the reply-to address for accounting related models to route to the accountant's email if present.
        """
        self.ensure_one()
        if self._context.get('accounting_context'):
            email = self.get_accountant_email()
            if email:
                return email
        return super().message_get_reply_to(default=default)

    def _notify_get_reply_to_addresses(self, records, default=None):
        """
        Override notification email address for accounting messages.
        """
        if self._context.get('accounting_context'):
            result = {}
            for rec in records:
                email = rec.get_accountant_email()
                result[rec.id] = email or default or super()._notify_get_reply_to_addresses(records, default=default).get(rec.id)
            return result
        return super()._notify_get_reply_to_addresses(records, default=default)

    @api.model_create_multi
    def create(self, vals_list):
        partners = super().create(vals_list)
        verification_disabled = self._is_verification_disabled()

        for rec in partners:
            ctx = rec.env.context
            contact_type = ctx.get('contact_type')
            force_show_toggles = ctx.get('force_show_toggles', False)
            skip_verification = (
                verification_disabled
                or ctx.get('skip_subcontact_verification')
            )

            # 🌟 Force show toggles for UI
            if force_show_toggles:
                rec.is_master_contact_visible = True
                rec.is_accountant_contact_visible = True
                rec.is_debtors_contact_visible = True
                rec.is_creditors_contact_visible = True
                rec.is_driver_contact_visible = True

            # 🌟 Assign role if explicitly passed
            if contact_type in ['master', 'accountant', 'debtors', 'creditors', 'driver']:
                field_name = f'is_{contact_type}_contact'
                setattr(rec, field_name, True)
                rec._clear_other_roles_on_assign(field_name)

            # 🌟 Sub-contact verification
            if not skip_verification and rec.parent_id:
                rec.active = False
                rec.is_pending_contact = True

                rec.env['contact.change.request'].create({
                    'partner_id': rec.id,
                    'change_data': {
                        'subcontact_id': rec.id,
                        'new_parent_id': rec.parent_id.id,
                        'is_master_contact': rec.is_master_contact,
                        'is_accountant_contact': rec.is_accountant_contact,
                        'is_debtors_contact': rec.is_debtors_contact,
                        'is_creditors_contact': rec.is_creditors_contact,
                        'is_driver_contact': rec.is_driver_contact,
                        'driver_id_number': rec.driver_id_number or '',
                    },
                    'trigger_type': 'create_sub',
                    'token': str(uuid.uuid4()),
                    'expiry_datetime': fields.Datetime.now() + timedelta(hours=48),
                    'company_id': rec.env.company.id,
                })._send_verification_email()

                rec.parent_id.show_subcontact_verification_warning = True

        return partners
 
    def write(self, vals):
        if not self._is_partner_management_enabled():
            return super().write(vals)
            
        for rec in self:
            # ✅ Skip all verification logic if globally disabled or all skips are explicitly passed
            verification_disabled = rec._is_verification_disabled()
            skip_sub = rec.env.context.get('skip_subcontact_verification', False)
            skip_role = rec.env.context.get('skip_role_verification', False)
            skip_sensitive = rec.env.context.get('skip_sensitive_verification', False)

            if verification_disabled or (skip_sub and skip_role and skip_sensitive):
                return super(ResPartner, rec).write(vals)

            # ✅ Sensitive field handling (email / driver ID first-time set allowed)
            if not skip_sensitive:
                if rec._is_one_time_email_update(vals):
                    return super(ResPartner, rec).write(vals)
                if rec._is_one_time_driver_id_update(vals) and not vals.get('is_driver_contact'):
                    return super(ResPartner, rec).write(vals)

            # ✅ Subcontact assignment / removal verification
            if not skip_sub and 'parent_id' in vals:
                old_parent_id = rec.parent_id.id if rec.parent_id else None
                new_parent_id = vals.get('parent_id')

                if not old_parent_id and new_parent_id:
                    new_parent = rec.env['res.partner'].browse(new_parent_id)
                    rec._pending_parent_id = new_parent.id
                    vals.pop('parent_id')

                    rec.env['contact.change.request'].create({
                        'partner_id': rec.id,
                        'change_data': {
                            'subcontact_id': rec.id,
                            'new_parent_id': new_parent.id,
                            'is_driver_contact': rec.is_driver_contact,
                            'driver_id_number': rec.driver_id_number or '',
                        },
                        'trigger_type': 'assign_sub',
                        'token': str(uuid.uuid4()),
                        'expiry_datetime': fields.Datetime.now() + timedelta(hours=48),
                        'company_id': rec.env.company.id,
                    })._send_verification_email()

                    return {
                        'warning': {
                            'title': _('Subcontact Assignment Submitted'),
                            'message': _('This change will only apply after verification.')
                        }
                    }

                elif old_parent_id and not new_parent_id:
                    old_parent = rec.parent_id
                    vals.pop('parent_id')

                    rec.env['contact.change.request'].create({
                        'partner_id': rec.id,
                        'change_data': {
                            'subcontact_id': rec.id,
                            'old_parent_id': old_parent.id,
                        },
                        'trigger_type': 'remove_sub',
                        'token': str(uuid.uuid4()),
                        'expiry_datetime': fields.Datetime.now() + timedelta(hours=48),
                        'company_id': rec.env.company.id,
                    })._send_verification_email()

                    return {
                        'warning': {
                            'title': _('Subcontact Removal Submitted'),
                            'message': _('This change will only apply after verification.')
                        }
                    }

            # ✅ Role assignment/removal verification
            if not skip_role:
                for field, trigger_assign, trigger_remove in [
                    ('is_master_contact', 'assign_master', 'remove_master'),
                    ('is_accountant_contact', 'assign_accountant', 'remove_accountant'),
                    ('is_driver_contact', 'assign_driver', 'remove_driver'),
                    ('is_debtors_contact', 'assign_debtors', 'remove_debtors'),
                    ('is_creditors_contact', 'assign_creditors', 'remove_creditors'),
                ]:
                    if field in vals:
                        new_value = vals[field]
                        old_value = getattr(rec, field)

                        if new_value and not old_value:
                            # Special case: driver must have ID before assigning
                            if field == 'is_driver_contact':
                                driver_id_val = vals.get('driver_id_number') or rec.driver_id_number
                                if not driver_id_val:
                                    raise ValidationError(_("You must enter a Driver ID Number before assigning as Driver Contact."))

                            rec._clear_other_roles_on_assign(field)

                            # Build change_data payload
                            change_data = {field: True}
                            if field == 'is_driver_contact':
                                change_data['driver_id_number'] = driver_id_val

                            rec.env['contact.change.request'].create({
                                'partner_id': rec.id,
                                'change_data': change_data,
                                'trigger_type': trigger_assign,
                                'token': str(uuid.uuid4()),
                                'expiry_datetime': fields.Datetime.now() + timedelta(hours=48),
                                'company_id': rec.env.company.id,
                            })._send_verification_email()

                            # Cancel the immediate update
                            vals[field] = False
                            if field == 'is_driver_contact' and 'driver_id_number' in vals:
                                vals['driver_id_number'] = rec.driver_id_number

                            return {
                                'warning': {
                                    'title': _(f"{field.replace('is_', '').replace('_', ' ').title()} Assignment Submitted"),
                                    'message': _("This role assignment will only apply after approval.")
                                }
                            }

                        elif not new_value and old_value:
                            rec.env['contact.change.request'].create({
                                'partner_id': rec.id,
                                'change_data': {field: False},
                                'trigger_type': trigger_remove,
                                'token': str(uuid.uuid4()),
                                'expiry_datetime': fields.Datetime.now() + timedelta(hours=48),
                                'company_id': rec.env.company.id,
                            })._send_verification_email()

                            vals[field] = True  # revert removal
                            return {
                                'warning': {
                                    'title': _(f"{field.replace('is_', '').replace('_', ' ').title()} Removal Submitted"),
                                    'message': _("This role removal will only apply after approval.")
                                }
                            }

        return super().write(vals)

    @api.onchange('child_ids')
    def _onchange_child_contacts(self):
        if not self._is_partner_management_enabled():
            return
        if self._context.get('form_view_initial_mode') != 'edit':
            return
        for rec in self:
            if rec._is_verification_disabled():
                continue
            if not rec._origin or not rec._origin.id:
                continue
            new_children = rec.child_ids.filtered(lambda c: not c.id)
            if new_children:
                return {
                    'warning': {
                        'title': _("Sub-Contact Creation Detected"),
                        'message': _("A new contact has been added and will be submitted for verification upon saving."),
                    }
                }

    @api.onchange('name', 'email', 'phone', 'mobile', 'vat', 'street', 'city', 'zip', 'driver_id_number')
    def _onchange_sensitive_fields(self):
        if not self._is_partner_management_enabled():
            return
        for rec in self:
            if rec._is_verification_disabled():
                continue
            if not rec._origin or not rec._origin.id:
                continue

            # === PATCH: Allow first time email filling without verification ===
            if 'email' in rec._fields and not rec._origin.email and rec.email:
                continue

            # === PATCH: Allow first time driver ID input without verification ===
            if 'driver_id_number' in rec._fields and not rec._origin.driver_id_number and rec.driver_id_number:
                continue

            # === Detect any sensitive field changes ===
            changed_fields = {}
            for field in self.SENSITIVE_FIELDS:
                if field in rec._fields and getattr(rec, field) != getattr(rec._origin, field):
                    changed_fields[field] = getattr(rec, field)

            if changed_fields:
                request = rec.env['contact.change.request'].create({
                    'partner_id': rec._origin.id,
                    'change_data': changed_fields,
                    'trigger_type': 'edit',
                    'token': str(uuid.uuid4()),
                    'expiry_datetime': fields.Datetime.now() + timedelta(hours=48),
                    'company_id': rec.env.company.id,
                })

                if 'driver_id_number' in changed_fields:
                    parent = rec.parent_id
                    master_contact = None
                    if parent:
                        master_contact = parent.child_ids.filtered(lambda c: c.is_master_contact and c.email)
                        master_contact = master_contact[0] if master_contact else None

                    recipient_email = (
                        (master_contact and master_contact.email)
                        or (parent and parent.email)
                    )

                    if recipient_email:
                        template_id = rec.env.ref('partner_management_suite.contact_change_verification_email_template', raise_if_not_found=False)
                        if template_id:
                            template_id.send_mail(request.id, force_send=True, email_values={'email_to': recipient_email})
                        else:
                            _logger.warning(f"⚠️ No email template found for Driver ID Change Request {request.id}.")
                    else:
                        _logger.warning(f"⚠️ No valid recipient email found for Driver ID Change Request {request.id}.")
                else:
                    request._send_verification_email()

                for field in changed_fields.keys():
                    setattr(rec, field, getattr(rec._origin, field))

                return {
                    'warning': {
                        'title': _("Change Request Submitted"),
                        'message': _("Your changes have been submitted for verification and will only be applied once approved."),
                    }
                }

    @api.depends('market_segment', 'distribution_group_id', 'customer_grading')
    def _compute_has_customer_info(self):
        for rec in self:
            rec.has_customer_info = bool(
                rec.market_segment or rec.distribution_group_id or rec.customer_grading
            )

    @api.depends('supplier_of_ids')
    def _compute_has_supplier_info(self):
        for rec in self:
            rec.has_supplier_info = bool(rec.supplier_of_ids)

    @api.depends(
        'market_segment', 'distribution_group_id', 'customer_grading',
        'supplier_of_ids', 'is_verified_contact', 'is_master_contact'
    )
    def _compute_badge_flags(self):
        for rec in self:
            rec.has_customer_info = bool(
                rec.market_segment or rec.distribution_group_id or rec.customer_grading
            )
            rec.has_supplier_info = bool(rec.supplier_of_ids)
            rec.show_verified_badge = rec.is_verified_contact
            rec.show_master_badge = rec.is_master_contact

    @api.depends('name', 'is_verified_contact')
    def _compute_display_name(self):
        for partner in self:
            partner.display_name = f"{partner.name} ✅" if partner.is_verified_contact else partner.name

    @api.constrains('is_master_contact', 'parent_id')
    def _check_single_master_contact(self):
        for rec in self:
            if rec.is_master_contact and rec.parent_id:
                existing = self.search([('id', '!=', rec.id), ('parent_id', '=', rec.parent_id.id), ('is_master_contact', '=', True)])
                if existing:
                    raise ValidationError("Only one Master Contact is allowed per company.")

    @api.constrains('is_accountant_contact', 'parent_id')
    def _check_single_accountant_contact(self):
        for rec in self:
            if rec.is_accountant_contact and rec.parent_id:
                existing = self.search([('id', '!=', rec.id), ('parent_id', '=', rec.parent_id.id), ('is_accountant_contact', '=', True)])
                if existing:
                    raise ValidationError("Only one Accountant Contact is allowed per company.")

    @api.constrains('is_debtors_contact', 'parent_id')
    def _check_single_debtors_contact(self):
        for rec in self:
            if rec.is_debtors_contact and rec.parent_id:
                existing = self.search([
                    ('id', '!=', rec.id),
                    ('parent_id', '=', rec.parent_id.id),
                    ('is_debtors_contact', '=', True)
                ])
                if existing:
                    raise ValidationError("Only one Debtors Account Contact is allowed per company.")

    @api.constrains('is_creditors_contact', 'parent_id')
    def _check_single_creditors_contact(self):
        for rec in self:
            if rec.is_creditors_contact and rec.parent_id:
                existing = self.search([
                    ('id', '!=', rec.id),
                    ('parent_id', '=', rec.parent_id.id),
                    ('is_creditors_contact', '=', True)
                ])
                if existing:
                    raise ValidationError("Only one Creditors Account Contact is allowed per company.")

    @api.model
    def _cron_delete_expired_pending(self):
        expired_contacts = self.search([
            ('is_pending_contact', '=', True),
            ('create_date', '<', fields.Datetime.now() - timedelta(hours=48))
        ])
        expired_contacts.unlink()
        
    @api.model
    def _cron_delete_stale_subcontacts(self):
        """
        Delete sub-contacts that have been pending and inactive for more than 7 days.
        """
        cutoff = fields.Datetime.now() - timedelta(days=7)
        stale_subs = self.search([
            ('is_pending_contact', '=', True),
            ('active', '=', False),
            ('parent_id', '!=', False),
            ('create_date', '<', cutoff),
        ])
        count = len(stale_subs)
        if stale_subs:
            stale_subs.unlink()
            _logger.info("🧹 Cron Cleanup: Removed %s stale sub-contact(s).", count)

    def action_block_credit(self):
        for partner in self:
            if not partner.is_credit_blocked:
                partner.is_credit_blocked = True
                partner.message_post(body=_("Customer manually blocked for credit by %s." % self.env.user.name))

    def action_unblock_credit(self):
        for partner in self:
            if partner.is_credit_blocked:
                partner.is_credit_blocked = False
                partner.message_post(body=_("Customer manually unblocked for credit by %s." % self.env.user.name))

    @api.depends('credit_limit')
    def _compute_show_credit_buttons(self):
        for rec in self:
            rec.show_credit_block_buttons = rec.credit_limit > 0

    def open_verification_wizard(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'contact.verification.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_trigger_type': 'new',
            }
        }

    def open_competitor_products_wizard(self):
        self.ensure_one()
        return {
            'name': 'Competitor Products',
            'type': 'ir.actions.act_window',
            'res_model': 'competitor.product.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_contact_id': self.id},
        }

    def _create_contact_verification_log(self, trigger_type, field_name=None):
        self.ensure_one()
        values = {
            'partner_id': self.id,
            'action_type': trigger_type,
        }
        if 'field_name' in self.env['contact.verification.log']._fields and field_name:
            values['field_name'] = field_name
        self.env['contact.verification.log'].create(values)
        
    def _is_one_time_email_update(self, vals):
        return (
            'email' in vals
            and not self.email     # email was previously empty
            and vals['email']      # now being set to a non-empty value
        )
        
    def _is_one_time_driver_id_update(self, vals):
        return (
            'driver_id_number' in vals
            and not self.driver_id_number  # Previously no ID
            and vals['driver_id_number']   # Now ID is being set
        )

    @api.depends('is_master_contact', 'is_accountant_contact', 'is_debtors_contact', 'is_creditors_contact', 'is_driver_contact')
    def _compute_contact_type_label(self):
        for rec in self:
            if rec.is_master_contact:
                rec.contact_type_label = "Master Contact"
            elif rec.is_accountant_contact:
                rec.contact_type_label = "Accountant Contact"
            elif rec.is_debtors_contact:
                rec.contact_type_label = "Debtors Contact"
            elif rec.is_creditors_contact:
                rec.contact_type_label = "Creditors Contact"
            elif rec.is_driver_contact:
                rec.contact_type_label = "Driver Contact"
            else:
                rec.contact_type_label = "Other Contact"

    @api.depends('is_master_contact', 'is_accountant_contact', 'is_debtors_contact', 'is_creditors_contact', 'is_driver_contact')
    def _compute_contact_role_visibility(self):
        for rec in self:
            ctx = rec.env.context

            # 🌟 1. If creating and force_show_toggles => show all
            if ctx.get('force_show_toggles'):
                rec.is_master_contact_visible = True
                rec.is_accountant_contact_visible = True
                rec.is_debtors_contact_visible = True
                rec.is_creditors_contact_visible = True
                rec.is_driver_contact_visible = True
                continue

            # 🌟 2. If coming from specific tab creation (context default_x)
            if ctx.get('default_is_master_contact'):
                rec.is_master_contact_visible = True
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = False
                continue

            if ctx.get('default_is_accountant_contact'):
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = True
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = False
                continue

            if ctx.get('default_is_debtors_contact'):
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = True
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = False
                continue

            if ctx.get('default_is_creditors_contact'):
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = True
                rec.is_driver_contact_visible = False
                continue

            if ctx.get('default_is_driver_contact'):
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = True
                continue

            # 🌟 3. Otherwise, infer from record itself (editing existing)
            if rec.is_master_contact:
                rec.is_master_contact_visible = True
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = False
            elif rec.is_accountant_contact:
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = True
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = False
            elif rec.is_debtors_contact:
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = True
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = False
            elif rec.is_creditors_contact:
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = True
                rec.is_driver_contact_visible = False
            elif rec.is_driver_contact:
                rec.is_master_contact_visible = False
                rec.is_accountant_contact_visible = False
                rec.is_debtors_contact_visible = False
                rec.is_creditors_contact_visible = False
                rec.is_driver_contact_visible = True
            else:
                # No role assigned => free choice
                rec.is_master_contact_visible = True
                rec.is_accountant_contact_visible = True
                rec.is_debtors_contact_visible = True
                rec.is_creditors_contact_visible = True
                rec.is_driver_contact_visible = True

    def _clear_other_roles_on_assign(self, assigned_role):
        """
        Ensure only one role is assigned to a sub-contact at a time.
        """
        role_fields = [
            'is_master_contact',
            'is_accountant_contact',
            'is_debtors_contact',
            'is_creditors_contact',
            'is_driver_contact',
        ]
        for field in role_fields:
            if field != assigned_role:
                setattr(self, field, False)
  
    # Explicitly expose the field to all users (removes group restriction from original definition)
credit_limit = fields.Monetary(
    string="Credit Limit",
    currency_field='property_payment_currency_id',
    default=0.0,
    groups=False,  # ← removes access restriction
)

