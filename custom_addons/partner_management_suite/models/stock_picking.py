from odoo import models, exceptions, _

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def button_validate(self):
        for picking in self:
            if picking.sale_id and picking.sale_id.partner_id.is_credit_blocked:
                raise exceptions.UserError(
                    _("Customer '%s' is manually blocked for credit. Cannot validate delivery.") % picking.sale_id.partner_id.name
                )
        return super().button_validate()
