from odoo import models, fields, api

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    master_contact_id = fields.Many2one(
        'res.partner',
        string="Master Contact",
        related='company_id.master_contact_id',
        readonly=False
    )

    disable_verification_globally = fields.Boolean(
        string="Disable Contact Verification Globally",
        config_parameter='partner_management_suite.disable_verification_globally',
        help="If enabled, all contact verification logic will be skipped across the system."
    )

    enabled_company_ids = fields.Many2many(
        'res.company',
        string='Enabled Companies for Partner Management',
        help='These companies will have the partner management features active.',
        compute='_compute_enabled_company_ids',
        inverse='_inverse_enabled_company_ids',
        store=False,
    )

    @api.model
    def _get_stored_enabled_company_ids(self):
        param = self.env['ir.config_parameter'].sudo().get_param('partner_management_suite.enabled_company_ids', '')
        return [int(x) for x in param.split(',') if x.strip().isdigit()]

    @api.depends()
    def _compute_enabled_company_ids(self):
        ids = self._get_stored_enabled_company_ids()
        self.enabled_company_ids = [(6, 0, ids)]

    def _inverse_enabled_company_ids(self):
        ids = self.enabled_company_ids.ids
        value = ','.join(str(x) for x in ids)
        self.env['ir.config_parameter'].sudo().set_param('partner_management_suite.enabled_company_ids', value)
