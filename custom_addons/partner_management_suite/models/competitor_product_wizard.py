from odoo import models, fields, api

class CompetitorProductLine(models.TransientModel):
    _name = 'competitor.product.line'
    _description = 'Competitor Product Line'

    wizard_id = fields.Many2one('competitor.product.wizard', string="Wizard", ondelete='cascade')
    product_id = fields.Many2one('product.template', string="Product", readonly=True)
    internal_reference = fields.Char(string="SKU", related='product_id.default_code', readonly=True)
    your_price = fields.Float(string="Quoted Price", readonly=True)
    competitor_price = fields.Float(string="Competitor Price", readonly=True)


class CompetitorProductWizard(models.TransientModel):
    _name = 'competitor.product.wizard'
    _description = 'Competitor Product Wizard'

    contact_id = fields.Many2one('res.partner', string="Contact", readonly=True)
    competitor_selection = fields.Many2one('res.partner', string="Select Competitor")
    search_term = fields.Char(string="Search")
    product_line_ids = fields.One2many('competitor.product.line', 'wizard_id', string="Products")

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        partner_id = self.env.context.get('active_id')
        if partner_id:
            contact = self.env['res.partner'].browse(partner_id)
            res['contact_id'] = contact.id
        return res

    @api.onchange('competitor_selection', 'search_term')
    def _onchange_competitor_selection(self):
        self.product_line_ids = [(5, 0, 0)]  # Clear previous

        if not self.competitor_selection:
            return

        competitor = self.competitor_selection
        contact = self.contact_id
        pricelist = contact.property_product_pricelist

        base_domain = ['|'] * 4 + [
            ('competitor_1_id', '=', competitor.id),
            ('competitor_2_id', '=', competitor.id),
            ('competitor_3_id', '=', competitor.id),
            ('competitor_4_id', '=', competitor.id),
            ('competitor_5_id', '=', competitor.id)
        ]

        if self.search_term:
            search_filter = ['|'] * 4 + [
                ('name', 'ilike', self.search_term),
                ('default_code', 'ilike', self.search_term),
                ('original_part_ids.name', 'ilike', self.search_term),
                ('engine_code_ids.name', 'ilike', self.search_term),
                ('oe_number_ids.name', 'ilike', self.search_term),
            ]
            domain = ['&'] + search_filter + base_domain
        else:
            domain = base_domain

        products = self.env['product.template'].search(domain)

        if not products:
            return {
                'warning': {
                    'title': 'No Results',
                    'message': 'No products found for this competitor with that search term.'
                }
            }

        lines = []
        for product in products:
            if product.competitor_1_id.id == competitor.id:
                comp_price = product.competitor_1_price
            elif product.competitor_2_id.id == competitor.id:
                comp_price = product.competitor_2_price
            elif product.competitor_3_id.id == competitor.id:
                comp_price = product.competitor_3_price
            elif product.competitor_4_id.id == competitor.id:
                comp_price = product.competitor_4_price
            elif product.competitor_5_id.id == competitor.id:
                comp_price = product.competitor_5_price
            else:
                comp_price = 0.0

            if pricelist:
                try:
                    your_price = pricelist._get_product_price(product, quantity=10.0, partner=contact)
                except Exception:
                    your_price = product.list_price
            else:
                your_price = product.list_price

            lines.append((0, 0, {
                'product_id': product.id,
                'your_price': your_price,
                'competitor_price': comp_price,
            }))

        self.product_line_ids = lines
