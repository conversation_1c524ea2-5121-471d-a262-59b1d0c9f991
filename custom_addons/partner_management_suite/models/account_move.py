from odoo import models, exceptions, _

class AccountMove(models.Model):
    _inherit = 'account.move'

    def action_post(self):
        for move in self:
            if move.move_type == 'out_invoice' and move.partner_id.is_credit_blocked:
                raise exceptions.UserError(
                    _("Customer '%s' is manually blocked for credit. Cannot post invoice.") % move.partner_id.name
                )
        return super().action_post()
