from odoo import models, fields, api

class ResendVerificationWizard(models.TransientModel):
    _name = 'resend.verification.wizard'
    _description = 'Resend Contact Verification Email'

    partner_id = fields.Many2one('res.partner', required=True, string="Contact")

    def action_resend_verification(self):
        if not self.partner_id:
            return True

        if self.partner_id.is_pending_contact:
            self.partner_id._send_verification_email('new')
        else:
            # Support resending for role-based pending requests (master, accountant, driver)
            change_request = self.env['contact.change.request'].search([
                ('partner_id', '=', self.partner_id.id),
                ('status', '=', 'pending')
            ], limit=1, order='id desc')

            if change_request:
                change_request._send_verification_email()

        self.env['contact.verification.log'].create({
            'partner_id': self.partner_id.id,
            'action_type': 'new_contact',  # Could be expanded if needed
            'status': 'pending',
            'timestamp': fields.Datetime.now(),
        })

        return {'type': 'ir.actions.act_window_close'}