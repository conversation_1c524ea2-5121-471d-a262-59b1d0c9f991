from odoo import models, fields, exceptions, _, api
from datetime import date

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    credit_limit_warning = fields.Text(string="Credit Warning", compute="_compute_credit_limit_warning", store=False)

    @api.depends('partner_id', 'amount_total')
    def _compute_credit_limit_warning(self):
        for order in self:
            warning = ""
            partner = order.partner_id
            if partner and partner.credit_limit > 0:
                unpaid = self.env['account.move'].search([
                    ('partner_id', '=', partner.id),
                    ('move_type', '=', 'out_invoice'),
                    ('state', '=', 'posted'),
                    ('payment_state', '!=', 'paid')
                ])
                total_due = sum(
                    inv.amount_residual for inv in unpaid
                    if not inv.invoice_date_due or inv.invoice_date_due < date.today()
                )
                exposure = total_due + order.amount_total
                if exposure > partner.credit_limit:
                    warning = _("⚠️ %s has exceeded credit limit!\nLimit: %.2f, Due: %.2f, This Order: %.2f, Total: %.2f") % (
                        partner.name, partner.credit_limit, total_due, order.amount_total, exposure
                    )
            order.credit_limit_warning = warning

    def action_confirm(self):
        for order in self:
            if order.partner_id.is_credit_blocked:
                raise exceptions.UserError(
                    _("Customer '%s' is manually blocked for credit. Please unblock to proceed.") % order.partner_id.name
                )
        return super().action_confirm()
