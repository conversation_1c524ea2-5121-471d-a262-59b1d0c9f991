from odoo import models, fields, api

class ResCompany(models.Model):
    _inherit = 'res.company'

    master_contact_id = fields.Many2one('res.partner', string="Master Contact", ondelete='set null')
    default_verification_email = fields.Char(string="Default Verification Email")

    @api.onchange('master_contact_id')
    def _onchange_master_contact_id(self):
        for company in self:
            if company.master_contact_id:
                company.default_verification_email = company.master_contact_id.email
            else:
                company.default_verification_email = False
