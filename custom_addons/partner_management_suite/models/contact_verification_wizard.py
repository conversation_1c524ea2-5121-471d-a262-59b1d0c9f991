from odoo import models, fields

class ContactVerificationWizard(models.TransientModel):
    _name = 'contact.verification.wizard'
    _description = 'Contact Verification Wizard'

    partner_id = fields.Many2one('res.partner', string="Partner", required=True)
    trigger_type = fields.Selection([
        ('new', 'New Contact'),
        ('change', 'Sensitive Change'),
        ('create_sub', 'Create Sub-Contact'),
        ('assign_master', 'Assign Master Contact'),
        ('assign_accountant', 'Assign Accountant Contact'),
        ('assign_debtors', 'Assign Debtors Contact'),
        ('assign_creditors', 'Assign Creditors Contact'),
        ('assign_driver', 'Assign Driver Contact'),
        ('remove_master', 'Remove Master Contact'),
        ('remove_accountant', 'Remove Accountant Contact'),
        ('remove_debtors', 'Remove Debtors Contact'),
        ('remove_creditors', 'Remove Creditors Contact'),
        ('remove_driver', 'Remove Driver Contact'),
    ], required=True, string="Trigger Type")
    field_name = fields.Char(string="Field Name")
    message = fields.Text(string="Message", compute='_compute_message')

    def _compute_message(self):
        for wizard in self:
            if wizard.trigger_type == 'new':
                wizard.message = "This is a new contact. No verification needed."
            elif wizard.trigger_type == 'change':
                wizard.message = "An email will be sent to the contact to verify the changes made."
            elif wizard.trigger_type == 'create_sub':
                wizard.message = "An email will be sent to the Master Contact to approve the creation of this sub-contact."
            elif wizard.trigger_type == 'assign_master':
                wizard.message = "An email will be sent to the company to approve the assignment of a Master Contact."
            elif wizard.trigger_type == 'assign_accountant':
                wizard.message = "An email will be sent to the company to approve the assignment of an Accountant Contact."
            elif wizard.trigger_type == 'assign_debtors':
                wizard.message = "An email will be sent to the Master Contact to approve the assignment of a Debtors Contact."
            elif wizard.trigger_type == 'assign_creditors':
                wizard.message = "An email will be sent to the Master Contact to approve the assignment of a Creditors Contact."
            elif wizard.trigger_type == 'assign_driver':
                wizard.message = "An email will be sent to the company to approve the assignment of a Driver Contact."
            elif wizard.trigger_type == 'remove_master':
                wizard.message = "An email will be sent to the company to approve the removal of a Master Contact."
            elif wizard.trigger_type == 'remove_accountant':
                wizard.message = "An email will be sent to the company to approve the removal of an Accountant Contact."
            elif wizard.trigger_type == 'remove_debtors':
                wizard.message = "An email will be sent to the Master Contact to approve the removal of a Debtors Contact."
            elif wizard.trigger_type == 'remove_creditors':
                wizard.message = "An email will be sent to the Master Contact to approve the removal of a Creditors Contact."
            elif wizard.trigger_type == 'remove_driver':
                wizard.message = "An email will be sent to the company to approve the removal of a Driver Contact."
            else:
                wizard.message = "Verification action required."

    def action_confirm(self):
        self.ensure_one()

        change_request = self.env['contact.change.request'].search([
            ('partner_id', '=', self.partner_id.id),
            ('status', '=', 'pending')
        ], limit=1, order='id desc')

        if not change_request:
            return {'type': 'ir.actions.act_window_close'}

        change_data = change_request.change_data or {}

        if change_request.trigger_type == 'edit':
            self.partner_id.sudo().with_context(
                skip_sensitive_verification=True,
                skip_subcontact_verification=True,
                skip_role_verification=True
            ).write(change_data)

        elif change_request.trigger_type == 'create_sub':
            sub_contact = self.partner_id
            if sub_contact and sub_contact.is_pending_contact and not sub_contact.active:
                update_vals = {'active': True, 'is_pending_contact': False}
                role_fields = [
                    'is_master_contact', 'is_accountant_contact',
                    'is_debtors_contact', 'is_creditors_contact',
                    'is_driver_contact', 'driver_id_number'
                ]
                for field in role_fields:
                    if field in change_data:
                        update_vals[field] = change_data[field]

                sub_contact.sudo().with_context(
                    skip_role_verification=True,
                    skip_sensitive_verification=True,
                    skip_subcontact_verification=True
                ).write(update_vals)

        elif change_request.trigger_type in [
            'assign_master', 'assign_accountant', 'assign_driver',
            'assign_debtors', 'assign_creditors'
        ]:
            vals = {}
            for field in [
                'is_master_contact', 'is_accountant_contact',
                'is_driver_contact', 'is_debtors_contact', 'is_creditors_contact',
                'driver_id_number'
            ]:
                if field in change_data:
                    vals[field] = change_data[field]
            self.partner_id.sudo().with_context(
                skip_role_verification=True,
                skip_sensitive_verification=True
            ).write(vals)

        elif change_request.trigger_type in [
            'remove_master', 'remove_accountant', 'remove_driver',
            'remove_debtors', 'remove_creditors'
        ]:
            vals = {}
            for field in [
                'is_master_contact', 'is_accountant_contact',
                'is_driver_contact', 'is_debtors_contact', 'is_creditors_contact'
            ]:
                if field in change_data:
                    vals[field] = change_data[field]
            self.partner_id.sudo().with_context(
                skip_role_verification=True,
                skip_sensitive_verification=True
            ).write(vals)

        elif change_request.trigger_type == 'assign_sub':
            subcontact = self.env['res.partner'].browse(change_data.get('subcontact_id'))
            new_parent = self.env['res.partner'].browse(change_data.get('new_parent_id'))
            if subcontact and new_parent:
                subcontact.sudo().with_context(
                    skip_subcontact_verification=True,
                    skip_sensitive_verification=True,
                    skip_role_verification=True
                ).write({'parent_id': new_parent.id})

        elif change_request.trigger_type == 'remove_sub':
            subcontact = self.env['res.partner'].browse(change_data.get('subcontact_id'))
            if subcontact:
                subcontact.sudo().with_context(
                    skip_subcontact_verification=True,
                    skip_sensitive_verification=True,
                    skip_role_verification=True
                ).write({
                    'parent_id': False,
                    'email': False,
                    'phone': False,
                    'mobile': False,
                    'driver_id_number': False,
                    'title': False,
                    'function': False,
                    'street': False,
                    'street2': False,
                    'zip': False,
                    'city': False,
                    'state_id': False,
                    'country_id': False,
                    'vat': False,
                    'company_type': 'person',
                })

        change_request.status = 'approved'

        return {'type': 'ir.actions.act_window_close'}

