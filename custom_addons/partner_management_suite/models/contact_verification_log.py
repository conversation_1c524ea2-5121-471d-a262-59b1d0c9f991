from odoo import models, fields

class ContactVerificationLog(models.Model):
    _name = 'contact.verification.log'
    _description = 'Contact Verification Audit Log'
    _order = 'timestamp desc'

    partner_id = fields.Many2one('res.partner', required=True)
    action_type = fields.Selection([
        ('new_contact', 'New Contact'),
        ('field_change', 'Field Change'),
        ('verified', 'Verified'),
        ('expired', 'Expired'),
        ('rejected', 'Rejected')
    ], required=True)
    old_value = fields.Text()
    new_value = fields.Text()
    status = fields.Selection([
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
        ('rejected', 'Rejected')
    ], required=True)
    timestamp = fields.Datetime(default=lambda self: fields.Datetime.now(), required=True)
    user_id = fields.Many2one('res.users', default=lambda self: self.env.user)
    ip_address = fields.Char()
