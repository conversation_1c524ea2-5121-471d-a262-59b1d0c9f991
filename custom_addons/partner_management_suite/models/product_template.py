from odoo import models, fields

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    competitor_1_id = fields.Many2one('res.partner', string="Competitor 1", ondelete='set null')
    competitor_1_price = fields.Float(string="Competitor 1 Price")

    competitor_2_id = fields.Many2one('res.partner', string="Competitor 2", ondelete='set null')
    competitor_2_price = fields.Float(string="Competitor 2 Price")

    competitor_3_id = fields.Many2one('res.partner', string="Competitor 3", ondelete='set null')
    competitor_3_price = fields.Float(string="Competitor 3 Price")

    competitor_4_id = fields.Many2one('res.partner', string="Competitor 4", ondelete='set null')
    competitor_4_price = fields.Float(string="Competitor 4 Price")

    competitor_5_id = fields.Many2one('res.partner', string="Competitor 5", ondelete='set null')
    competitor_5_price = fields.Float(string="Competitor 5 Price")
