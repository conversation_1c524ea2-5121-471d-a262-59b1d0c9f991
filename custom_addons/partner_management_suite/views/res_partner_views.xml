<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- Lightweight Subcontact Form -->
    <record id="view_partner_subcontact_form" model="ir.ui.view">
        <field name="name">res.partner.subcontact.form</field>
        <field name="model">res.partner</field>
        <field name="arch" type="xml">
            <form string="Create Sub-Contact">
                <sheet>
                    <group>
                        <field name="type" widget="radio" options="{&quot;horizontal&quot;: true}" context="{&quot;default_type&quot;: &quot;other&quot;}"/>
                    </group>

                    <!-- ✨ Contact Type Label at the top -->
                    <group>
                        <field name="contact_type_label" readonly="1"/>
                    </group>

                    <!-- ✨ Add Toggles for Contact Roles -->
                    <group string="Contact Role">
                        <field name="is_master_contact" widget="boolean_toggle" invisible="not is_master_contact_visible"/>
                        <field name="is_accountant_contact" widget="boolean_toggle" invisible="not is_accountant_contact_visible"/>
                        <field name="is_debtors_contact" widget="boolean_toggle" invisible="not is_debtors_contact_visible"/>
                        <field name="is_creditors_contact" widget="boolean_toggle" invisible="not is_creditors_contact_visible"/>
                        <field name="is_driver_contact" widget="boolean_toggle" invisible="not is_driver_contact_visible"/>
                        <field name="driver_id_number" placeholder="e.g. *************" invisible="is_driver_contact != True"/>
                    </group>

                    <!-- ✨ Standard Contact Info -->
                    <group string="Contact Information">
                        <field name="name"/>
                        <field name="branch_name"/>
                        <field name="phone"/>
                        <field name="mobile"/>
                        <field name="email"/>
                        <field name="street"/>
                        <field name="street2"/>
                        <field name="city"/>
                        <field name="state_id"/>
                        <field name="zip"/>
                        <field name="country_id"/>
                    </group>

                </sheet>
            </form>
        </field>
    </record>

    <!-- Extended Partner Form -->
    <record id="view_partner_form_inherit_suite_fields" model="ir.ui.view">
        <field name="name">res.partner.form.add.suite.enabled.helper</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form/sheet" position="before">
                <field name="is_partner_suite_enabled" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="view_partner_form_inherit_combined" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.customer.supplier.competitor.creditblock.verification.tabs</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">

            <!-- Hidden fields for badge control -->
            <xpath expr="//form/sheet/notebook" position="before">
                <field name="is_verified_contact" invisible="1"/>
                <field name="is_master_contact" invisible="1"/>
                <field name="is_accountant_contact" invisible="1"/>
                <field name="is_driver_contact" invisible="1"/>
                <field name="is_debtors_contact" invisible="1"/>
                <field name="is_creditors_contact" invisible="1"/>
                <field name="has_customer_info" invisible="1"/>
                <field name="has_supplier_info" invisible="1"/>
                <field name="customer_grading" invisible="1"/>
            </xpath>

            <!-- Customer/Supplier/Competitor badges -->
            <xpath expr="//field[@name='company_type']" position="after">
                <div class="oe_banner_box">
                    <div class="badge badge-customer" invisible="not has_customer_info">Customer ✓</div>
                    <div class="badge badge-supplier" invisible="not has_supplier_info">Supplier ✓</div>
                    <div class="badge badge-cod" invisible="customer_grading != 'cod'">C.O.D Customer</div>
                    <div class="badge badge-competitor" invisible="customer_grading != 'competitor'">Competitor</div>
                </div>
            </xpath>

            <!-- Verified/Master/Accountant/Driver badges after Name -->
            <xpath expr="//field[@name='name']" position="after">
                <div class="mt-2 d-flex flex-wrap align-items-center gap-2">
                    <div class="badge badge-verified" invisible="not show_verified_badge">
                        <img src="/partner_management_suite/static/src/img/verified_icon.png" class="badge-icon"/>
                    </div>
                    <div class="badge badge-master" invisible="not show_master_badge">
                        <img src="/partner_management_suite/static/src/img/master_icon.png" class="badge-icon"/>
                    </div>
                    <div class="badge badge-accountant" invisible="not is_accountant_contact">
                        <span class="badge-icon">💼</span> Accountant
                    </div>
                    <div class="badge badge-driver" invisible="not is_driver_contact">
                        <span class="badge-icon">🚚</span> Driver
                    </div>
                </div>
            </xpath>

            <!-- Contacts & Addresses Section -->
            <xpath expr="//page[@name='contact_addresses']/field[@name='child_ids']" position="replace">
                <field name="child_ids" mode="kanban"
                       context="{
                           'default_parent_id': id,
                           'default_street': street,
                           'default_street2': street2,
                           'default_city': city,
                           'default_state_id': state_id,
                           'default_zip': zip,
                           'default_country_id': country_id,
                           'default_lang': lang,
                           'default_user_id': user_id,
                           'default_type': 'other'
                       }"
                       invisible="1"
                />
            </xpath>

			<xpath expr="//page[@name='contact_addresses']" position="inside">
				<notebook>

					<page string="Master Contacts">
						<field name="master_child_ids"
							   context="{
								   'default_parent_id': id,
								   'default_is_master_contact': True,
								   'default_contact_type_label': 'Master Contact',
								   'form_view_ref': 'partner_management_suite.view_partner_subcontact_form'
							   }">
							<list create="true" edit="false" delete="false">
								<field name="name"/>
								<field name="email"/>
								<field name="phone"/>
								<field name="mobile"/>
								<field name="branch_name"/>
							</list>
						</field>
					</page>

					<page string="Accountant Contacts">
						<field name="accountant_child_ids"
							   context="{
								   'default_parent_id': id,
								   'default_is_accountant_contact': True,
								   'default_contact_type_label': 'Accountant Contact',
								   'form_view_ref': 'partner_management_suite.view_partner_subcontact_form'
							   }">
							<list create="true" edit="false" delete="false">
								<field name="name"/>
								<field name="email"/>
								<field name="phone"/>
								<field name="mobile"/>
								<field name="branch_name"/>
							</list>
						</field>
					</page>

					<page string="Debtors Contacts">
						<field name="debtors_child_ids"
							   context="{
								   'default_parent_id': id,
								   'default_is_debtors_contact': True,
								   'default_contact_type_label': 'Debtors Contact',
								   'form_view_ref': 'partner_management_suite.view_partner_subcontact_form'
							   }">
							<list create="true" edit="false" delete="false">
								<field name="name"/>
								<field name="email"/>
								<field name="phone"/>
								<field name="mobile"/>
								<field name="branch_name"/>
							</list>
						</field>
					</page>

					<page string="Creditors Contacts">
						<field name="creditors_child_ids"
							   context="{
								   'default_parent_id': id,
								   'default_is_creditors_contact': True,
								   'default_contact_type_label': 'Creditors Contact',
								   'form_view_ref': 'partner_management_suite.view_partner_subcontact_form'
							   }">
							<list create="true" edit="false" delete="false">
								<field name="name"/>
								<field name="email"/>
								<field name="phone"/>
								<field name="mobile"/>
								<field name="branch_name"/>
							</list>
						</field>
					</page>

					<page string="Driver Contacts">
						<field name="driver_child_ids"
							   context="{
								   'default_parent_id': id,
								   'default_is_driver_contact': True,
								   'default_contact_type_label': 'Driver Contact',
								   'form_view_ref': 'partner_management_suite.view_partner_subcontact_form'
							   }">
							<list create="true" edit="false" delete="false">
								<field name="name"/>
								<field name="email"/>
								<field name="phone"/>
								<field name="mobile"/>
								<field name="driver_id_number"/>
								<field name="branch_name"/>
							</list>
						</field>
					</page>

					<page string="Other Contacts">
						<field name="other_child_ids"
							   context="{
								   'default_parent_id': id,
								   'default_contact_type_label': 'Other Contact',
								   'form_view_ref': 'partner_management_suite.view_partner_subcontact_form',
								   'force_show_toggles': True
							   }">
							<list create="true" edit="false" delete="false">
								<field name="name"/>
								<field name="email"/>
								<field name="phone"/>
								<field name="mobile"/>
								<field name="branch_name"/>
							</list>
						</field>
					</page>

				</notebook>
			</xpath>

            <!-- Customer Info tab -->
            <xpath expr="//notebook" position="inside">
                <page string="Customer Info" name="customer_info_tab" modifiers="{'invisible': [('is_partner_suite_enabled','=',False)]}">
                    <group>
                        <field name="market_segment"/>
                        <field name="distribution_group_id"/>
                        <field name="customer_grading"/>
                    </group>
                </page>

                <page string="Supplier Info" name="supplier_info_tab" modifiers="{'invisible': [('is_partner_suite_enabled','=',False)]}">
                    <group>
                        <field name="supplier_of_ids" widget="many2many_tags"/>
                    </group>
                </page>

                <page string="Competitor" name="competitor_info_tab" modifiers="{'invisible': [('is_partner_suite_enabled','=',False)]}">
                    <group>
                        <field name="competitor_1_id"/>
                        <field name="competitor_2_id"/>
                        <field name="competitor_3_id"/>
                        <field name="competitor_4_id"/>
                        <field name="competitor_5_id"/>
                    </group>
                    <div class="mt-2">
                        <button name="open_competitor_products_wizard" type="object" string="View Competitor Products" class="btn btn-primary"/>
                    </div>
                </page>
            </xpath>
			
            <!-- Manual Credit Control inside Accounting Tab -->
            <xpath expr="//page[@name='accounting']" position="inside">
                <group string="Manual Credit Control" groups="account.group_account_user,base.group_system">
                    <field name="show_credit_block_buttons" invisible="1"/>
                    <div class="d-flex align-items-center mb-3">
                        <field name="is_credit_blocked" readonly="1" class="me-3" modifiers='{"invisible": [["show_credit_block_buttons", "=", false]]}'/>
                        <button name="action_block_credit" type="object" class="btn btn-danger me-2" icon="fa-ban" string="Block for Credit" modifiers='{"invisible": [["show_credit_block_buttons", "=", false]]}'/>
                        <button name="action_unblock_credit" type="object" class="btn btn-success" icon="fa-check-circle" string="Unblock for Credit" modifiers='{"invisible": [["show_credit_block_buttons", "=", false]]}'/>
                    </div>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Resend Verification Wizard Action -->
    <record id="action_resend_verification_wizard" model="ir.actions.act_window">
        <field name="name">Resend Verification</field>
        <field name="res_model">resend.verification.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Attach Resend Verification Wizard to Action Menu -->
    <act_window id="partner_resend_verification_action" name="Resend Verification" res_model="resend.verification.wizard" view_mode="form" target="new" binding_model="res.partner" binding_type="action" context="{'default_partner_id': active_id}"/>

</odoo>
