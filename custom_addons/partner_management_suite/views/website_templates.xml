<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- ✅ Successful Approval -->
    <template id="verification_success" name="Verification Success">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h1 class="text-success">✅ Action Approved</h1>
                <t t-if="partner">
                    <p class="lead">Hello <strong><t t-esc="partner.name"/></strong>, the requested changes have been approved successfully.</p>
                </t>
                <t t-else="">
                    <p class="lead">The requested changes were approved successfully.</p>
                </t>
                <a href="/" class="btn btn-primary mt-4">Return to Homepage</a>
            </div>
        </t>
    </template>

    <!-- ❌ Rejected Action -->
    <template id="verification_rejected" name="Verification Rejected">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h1 class="text-danger">❌ Action Rejected</h1>
                <t t-if="partner">
                    <p class="lead">Hello <strong><t t-esc="partner.name"/></strong>, the request has been rejected as per your decision.</p>
                </t>
                <t t-else="">
                    <p class="lead">The change request has been rejected successfully.</p>
                </t>
                <a href="/" class="btn btn-secondary mt-4">Return to Homepage</a>
            </div>
        </t>
    </template>

    <!-- ⚠️ Expired Token or Invalid Request -->
    <template id="verification_expired" name="Verification Expired">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h1 class="text-warning">⚠️ Link Expired or Invalid</h1>
                <p class="lead">The verification link you used has expired or is no longer valid.</p>
                <a href="/" class="btn btn-secondary mt-4">Return to Homepage</a>
            </div>
        </t>
    </template>
</odoo>
