<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_competitor_product_wizard_form" model="ir.ui.view">
        <field name="name">competitor.product.wizard.form</field>
        <field name="model">competitor.product.wizard</field>
        <field name="arch" type="xml">
            <form string="Competitor Products">
                <group>
                    <field name="contact_id" readonly="1"/>
                    <field name="competitor_selection" options="{'no_create': True}"/>
                    <field name="search_term" placeholder="Search by product name or part number..."/>
                </group>
                <separator string="PRODUCTS"/>
                <field name="product_line_ids">
                    <list editable="bottom">
                        <field name="product_id" widget="many2one"/>
                        <field name="internal_reference"/>
                        <field name="your_price"/>
                        <field name="competitor_price"/>
                    </list>
                </field>
                <footer>
                    <button string="Close" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
