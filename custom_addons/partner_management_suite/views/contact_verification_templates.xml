<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- Landing page for editing contact -->
    <template id="change_request_landing_edit" name="Edit Contact Change Request">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h2>🔄 Pending Contact Update</h2>
                <p>You are about to update the contact <strong><t t-esc="change_request.partner_id.name"/></strong> with the following changes:</p>

                <table class="table table-bordered mt-4">
                    <thead>
                        <tr>
                            <th>Field</th>
                            <th>Old Value</th>
                            <th>New Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-if="change_request.change_data">
                            <t t-foreach="change_request.change_data.items()" t-as="field">
                                <tr>
                                    <td><t t-esc="field[0]"/></td>
                                    <td><t t-esc="change_request.old_value or '-'"/></td>
                                    <td><t t-esc="change_request.new_value or '-'"/></td>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                </table>

                <div class="mt-4">
                    <a t-att-href="'/verify/change/accept/' + change_request.token" class="btn btn-success me-3">✅ Approve Changes</a>
                    <a t-att-href="'/verify/change/reject/' + change_request.token" class="btn btn-danger">❌ Reject Changes</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Landing page for creating sub-contact -->
    <template id="change_request_landing_create_sub" name="Sub-Contact Creation Request">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h2>👤 New Sub-Contact Creation Request</h2>
                <p>A new sub-contact is being created under <strong><t t-esc="change_request.partner_id.name"/></strong> with the following details:</p>

                <table class="table table-bordered mt-4">
                    <tbody>
                        <t t-if="change_request.change_data">
                            <t t-foreach="change_request.change_data.items()" t-as="field">
                                <tr>
                                    <th><t t-esc="field[0]"/></th>
                                    <td><t t-esc="field[1]"/></td>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                </table>

                <div class="mt-4">
                    <a t-att-href="'/verify/change/accept/' + change_request.token" class="btn btn-success me-3">✅ Approve Creation</a>
                    <a t-att-href="'/verify/change/reject/' + change_request.token" class="btn btn-danger">❌ Reject Creation</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Master Contact Assignment -->
    <template id="change_request_landing_assign_master" name="Master Contact Assignment Request">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h2>🏢 Master Contact Assignment Request</h2>
                <p>You are being assigned as the new Master Contact for the company <strong><t t-esc="change_request.partner_id.parent_id.name"/></strong>.</p>
                <div class="mt-4">
                    <a t-att-href="'/verify/change/accept/' + change_request.token" class="btn btn-success me-3">✅ Accept Assignment</a>
                    <a t-att-href="'/verify/change/reject/' + change_request.token" class="btn btn-danger">❌ Reject Assignment</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Accountant Contact Assignment -->
    <template id="change_request_landing_assign_accountant" name="Accountant Contact Assignment Request">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h2>💼 Accountant Contact Assignment Request</h2>
                <p>You are being assigned as the Accountant Contact for <strong><t t-esc="change_request.partner_id.parent_id.name"/></strong>.</p>
                <div class="mt-4">
                    <a t-att-href="'/verify/change/accept/' + change_request.token" class="btn btn-success me-3">✅ Accept Assignment</a>
                    <a t-att-href="'/verify/change/reject/' + change_request.token" class="btn btn-danger">❌ Reject Assignment</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Driver Contact Assignment -->
    <template id="change_request_landing_assign_driver" name="Driver Contact Assignment Request">
        <t t-call="website.layout">
            <div class="container py-5 text-center">
                <h2>🚚 Driver Contact Assignment Request</h2>
                <p>You are being assigned as the Driver Contact for <strong><t t-esc="change_request.partner_id.parent_id.name"/></strong>.</p>
                <div class="mt-4">
                    <a t-att-href="'/verify/change/accept/' + change_request.token" class="btn btn-success me-3">✅ Accept Assignment</a>
                    <a t-att-href="'/verify/change/reject/' + change_request.token" class="btn btn-danger">❌ Reject Assignment</a>
                </div>
            </div>
        </t>
    </template>

</odoo>
