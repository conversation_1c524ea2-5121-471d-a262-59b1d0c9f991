<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_partner_filter_inherit_customer_supplier" model="ir.ui.view">
        <field name="name">res.partner.search.inherit.customer.supplier</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">

            <xpath expr="//group" position="inside">

                <!-- Group by basic boolean logic -->
                <filter string="Customer Info" name="group_by_customer"
                        context="{'group_by': 'has_customer_info'}"/>
                <filter string="Supplier Info" name="group_by_supplier"
                        context="{'group_by': 'has_supplier_info'}"/>

                <!-- Group by Customer Info fields -->
                <filter string="Market Segment" name="group_by_market_segment"
                        context="{'group_by': 'market_segment'}"/>
                <filter string="Distribution Group" name="group_by_distribution_group"
                        context="{'group_by': 'distribution_group_id'}"/>
                <filter string="Customer Grading" name="group_by_customer_grading"
                        context="{'group_by': 'customer_grading'}"/>

                <!-- Group by Supplier Info field -->
                <filter string="Supplier Of" name="group_by_supplier_of"
                        context="{'group_by': 'supplier_of_ids'}"/>

            </xpath>

        </field>
    </record>
</odoo>
