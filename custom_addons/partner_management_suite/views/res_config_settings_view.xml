<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_partner_verification_settings_form" model="ir.ui.view">
        <field name="name">res.config.settings.partner.verification</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <div class="app_settings_block" data-string="Partner Tools Settings" string="Partner Tools Settings">
                    <h2>Partner Tools Settings</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_right_pane">
                                <field name="disable_verification_globally"/>
                                <label for="disable_verification_globally"/>
                                <div class="text-muted">
                                    Disable all contact verification rules globally for testing or maintenance.
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_right_pane">
                                <field name="enabled_company_ids" widget="many2many_tags"/>
                                <label for="enabled_company_ids"/>
                                <div class="text-muted">
                                    Select the companies where Partner Management Suite should be active.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </field>
        </field>
    </record>
</odoo>
