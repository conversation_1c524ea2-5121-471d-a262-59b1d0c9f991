<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- List View (clean, no buttons) -->
    <record id="view_contact_change_request_list" model="ir.ui.view">
        <field name="name">contact.change.request.list</field>
        <field name="model">contact.change.request</field>
        <field name="arch" type="xml">
            <list>
                <field name="partner_id"/>
                <field name="trigger_type"/>
                <field name="field_name"/>
                <field name="status"/>
                <field name="expiry_datetime"/>
            </list>
        </field>
    </record>

    <!-- Form View with Approve + Reject buttons in header -->
    <record id="view_contact_change_request_form" model="ir.ui.view">
        <field name="name">contact.change.request.form</field>
        <field name="model">contact.change.request</field>
        <field name="arch" type="xml">
            <form string="Contact Change Request">
                <header>
                    <button name="action_admin_approve" type="object" string="Approve" class="btn btn-primary"
                            modifiers="{'invisible': [['show_action_buttons', '=', False]]}"/>
                    <button name="action_admin_reject" type="object" string="Reject" class="btn btn-secondary"
                            modifiers="{'invisible': [['show_action_buttons', '=', False]]}"/>
                </header>
                <sheet>
                    <group>
                        <field name="partner_id" readonly="1"/>
                        <field name="trigger_type" readonly="1"/>
                        <field name="field_name" readonly="1"/>
                        <field name="old_value" readonly="1"/>
                        <field name="new_value" readonly="1"/>
                        <field name="change_data" readonly="1"/>
                        <field name="status" readonly="1"/>
                        <field name="token" readonly="1"/>
                        <field name="expiry_datetime" readonly="1"/>
                        <field name="show_action_buttons" invisible="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_contact_change_request" model="ir.actions.act_window">
        <field name="name">Contact Change Requests</field>
        <field name="res_model">contact.change.request</field>
        <field name="view_mode">list,form</field>
    </record>

</odoo>