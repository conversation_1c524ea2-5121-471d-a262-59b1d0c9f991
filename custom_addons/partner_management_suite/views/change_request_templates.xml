<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <template id="change_request_landing_edit" name="Edit Change Request">
    <t t-call="website.layout">
      <div class="container py-5">
        <h1 class="text-primary">✏️ Review Contact Edit</h1>
        <p>A change has been requested for the following contact information.</p>
        <a href="/verify/change/accept/{{ change_request.token }}" class="btn btn-success mt-3">✔️ Approve Changes</a>
        <a href="/verify/change/reject/{{ change_request.token }}" class="btn btn-danger mt-3">❌ Reject Changes</a>
      </div>
    </t>
  </template>

  <template id="change_request_landing_create_sub" name="Create Sub Contact">
    <t t-call="website.layout">
      <div class="container py-5">
        <h1 class="text-info">👥 Review Sub-Contact Creation</h1>
        <p>A request has been made to create a new sub-contact under your company.</p>
        <a href="/verify/change/accept/{{ change_request.token }}" class="btn btn-success mt-3">✔️ Approve Sub-Contact</a>
        <a href="/verify/change/reject/{{ change_request.token }}" class="btn btn-danger mt-3">❌ Reject Sub-Contact</a>
      </div>
    </t>
  </template>

  <template id="change_request_landing_assign_master" name="Assign Master Contact">
    <t t-call="website.layout">
      <div class="container py-5">
        <h1 class="text-warning">🏆 Assign Master Contact</h1>
        <p>You have been selected as the Master Contact for your company. Please confirm.</p>
        <a href="/verify/change/accept/{{ change_request.token }}" class="btn btn-success mt-3">✔️ Accept Master Contact Role</a>
        <a href="/verify/change/reject/{{ change_request.token }}" class="btn btn-danger mt-3">❌ Reject Assignment</a>
      </div>
    </t>
  </template>

</odoo>
