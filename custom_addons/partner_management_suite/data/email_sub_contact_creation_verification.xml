<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="sub_contact_creation_verification_email_template" model="mail.template">
        <field name="name">Sub-Contact Creation/Removal Approval</field>
        <field name="model_id" ref="partner_management_suite.model_contact_change_request"/>
        <field name="subject">Approval Needed: Sub-Contact Update</field>
        <field name="email_from">{{ (object.company_id.email_formatted or user.company_id.email_formatted or user.email_formatted) }}</field>
        <field name="email_to">{{ object.partner_id.master_contact_id.email or object.partner_id.parent_id.email }}</field>
        <field name="body_html">
            <![CDATA[
                <div style="font-family: Arial, sans-serif; padding: 15px;">
                    <p>Hello <t t-esc="(object.partner_id.master_contact_id.name or object.partner_id.parent_id.name)"/>,</p>

                    <t t-if="object.trigger_type in ['create_sub', 'assign_sub']">
                        <p>A new sub-contact has been <strong>created</strong> or <strong>assigned</strong> under your company profile and requires your approval before activation.</p>
                        <p><strong>New Sub-Contact Details:</strong></p>
                        <ul style="padding-left: 20px;">
                            <li><strong>Name:</strong> <t t-esc="object.partner_id.name"/></li>
                            <li><strong>Email:</strong> <t t-esc="object.partner_id.email or 'N/A'"/></li>
                            <li><strong>Phone:</strong> <t t-esc="object.partner_id.phone or 'N/A'"/></li>
                            <li><strong>Mobile:</strong> <t t-esc="object.partner_id.mobile or 'N/A'"/></li>
                            <li><strong>Company:</strong> <t t-esc="object.partner_id.parent_id.name or 'N/A'"/></li>
                        </ul>
                        <p>Please review and approve or reject this sub-contact creation:</p>
                    </t>

                    <t t-if="object.trigger_type == 'remove_sub'">
                        <p>A request has been made to <strong>remove</strong> the following sub-contact from your company profile. Please review and confirm:</p>
                        <p><strong>Sub-Contact To Be Removed:</strong></p>
                        <ul style="padding-left: 20px;">
                            <li><strong>Name:</strong> <t t-esc="object.partner_id.name"/></li>
                            <li><strong>Email:</strong> <t t-esc="object.partner_id.email or 'N/A'"/></li>
                            <li><strong>Phone:</strong> <t t-esc="object.partner_id.phone or 'N/A'"/></li>
                            <li><strong>Mobile:</strong> <t t-esc="object.partner_id.mobile or 'N/A'"/></li>
                        </ul>
                        <p>Please approve or reject this removal request:</p>
                    </t>

                    <div style="margin: 20px 0;">
                        <a t-att-href="object._get_approval_url() or ''"
                           style="background-color:#28a745;color:white;padding:12px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-bottom:10px;">
                           ✔️ Approve
                        </a><br/>
                        <a t-att-href="object._get_rejection_url() or ''"
                           style="background-color:#dc3545;color:white;padding:12px 20px;text-decoration:none;border-radius:5px;display:inline-block;">
                           ❌ Reject
                        </a>
                    </div>

                    <p><i>Note: This verification link will expire automatically after 48 hours.</i></p>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;"/>

                    <div style="text-align: center; font-size: 12px; color: #666; margin-top: 20px;">
                        <t t-if="object.company_id.logo">
                            <img t-att-src="'/web/image/res.company/%s/logo' % object.company_id.id"
                                 alt="Company Logo"
                                 style="max-height:100px; height:auto; width:auto; display:inline-block; margin-bottom:10px;"/><br/>
                        </t>
                        <strong><t t-esc="object.company_id.name"/></strong><br/>
                        <t t-if="object.company_id.street"><t t-esc="object.company_id.street"/>, </t>
                        <t t-if="object.company_id.city"><t t-esc="object.company_id.city"/>, </t>
                        <t t-if="object.company_id.country_id"><t t-esc="object.company_id.country_id.name"/></t><br/>
                        <t t-if="object.company_id.phone">Phone: <t t-esc="object.company_id.phone"/> | </t>
                        <t t-if="object.company_id.email">Email: <t t-esc="object.company_id.email"/></t>
                    </div>
                </div>
            ]]>
        </field>
    </record>
</odoo>
