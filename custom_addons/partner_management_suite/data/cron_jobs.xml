<odoo>
  <data noupdate="1">

    <!-- Cron: Expire stale change requests -->
    <record id="cron_expire_change_requests" model="ir.cron">
      <field name="name">Expire Contact Change Requests</field>
      <field name="model_id" ref="model_contact_change_request"/>
      <field name="state">code</field>
      <field name="code">model._cron_expire_changes()</field>
      <field name="interval_number">1</field>
      <field name="interval_type">hours</field>
      <field name="numbercall">-1</field>
      <field name="active">True</field>
    </record>

    <!-- Cron: Auto-delete expired unverified contacts -->
    <record id="cron_delete_unverified_contacts" model="ir.cron">
      <field name="name">Delete Expired Unverified Contacts</field>
      <field name="model_id" ref="base.model_res_partner"/>
      <field name="state">code</field>
      <field name="code">model._cron_delete_expired_pending()</field>
      <field name="interval_number">1</field>
      <field name="interval_type">hours</field>
      <field name="numbercall">-1</field>
      <field name="active">True</field>
    </record>

  </data>
</odoo>
