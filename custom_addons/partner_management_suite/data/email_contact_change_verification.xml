<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="contact_change_verification_email_template" model="mail.template">
        <field name="name">Contact Change Verification Email</field>
        <field name="model_id" ref="partner_management_suite.model_contact_change_request"/>
        <field name="subject">Action Required: Contact Information Update</field>
        <field name="email_from">{{ (object.company_id.email_formatted or user.company_id.email_formatted or user.email_formatted) }}</field>
        <field name="email_to">{{ object.partner_id.email }}</field>
        <field name="body_html">
            <![CDATA[
                <div style="font-family: Arial, sans-serif; padding: 15px;">
                    <p>Hello <t t-esc="object.partner_id.name"/>,</p>

                    <p>We have received a request to update your contact information. Please review the requested changes below and either approve or reject them:</p>

                    <table style="width:100%;border-collapse:collapse;margin:20px 0;">
                        <thead>
                            <tr>
                                <th style="border:1px solid #ddd;padding:8px;background-color:#f2f2f2;text-align:left;">Field</th>
                                <th style="border:1px solid #ddd;padding:8px;background-color:#f2f2f2;text-align:left;">New Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="object.change_data.items()" t-as="change">
                                <tr>
                                    <td style="border:1px solid #ddd;padding:8px;"><t t-esc="change[0].replace('_', ' ').title()"/></td>
                                    <td style="border:1px solid #ddd;padding:8px;"><t t-esc="change[1]"/></td>
                                </tr>
                            </t>
                        </tbody>
                    </table>

                    <div style="margin: 20px 0;">
                        <a t-att-href="object._get_approval_url() or ''" style="background-color:#28a745;color:white;padding:12px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-bottom:10px;">✔️ Approve Changes</a><br/>
                        <a t-att-href="object._get_rejection_url() or ''" style="background-color:#dc3545;color:white;padding:12px 20px;text-decoration:none;border-radius:5px;display:inline-block;">❌ Reject Changes</a>
                    </div>

                    <p><i>Note: This verification link will expire automatically in 48 hours.</i></p>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;"/>

                    <div style="text-align: center; font-size: 12px; color: #666; margin-top: 20px;">
                        <t t-if="object.company_id.logo">
                            <img t-att-src="'/web/image/res.company/%s/logo' % object.company_id.id"
                                 alt="Company Logo"
                                 style="max-height:100px; height:auto; width:auto; display:inline-block; margin-bottom:10px;"/><br/>
                        </t>
                        <strong><t t-esc="object.company_id.name"/></strong><br/>
                        <t t-if="object.company_id.street"><t t-esc="object.company_id.street"/>, </t>
                        <t t-if="object.company_id.city"><t t-esc="object.company_id.city"/>, </t>
                        <t t-if="object.company_id.country_id"><t t-esc="object.company_id.country_id.name"/></t><br/>
                        <t t-if="object.company_id.phone">Phone: <t t-esc="object.company_id.phone"/> | </t>
                        <t t-if="object.company_id.email">Email: <t t-esc="object.company_id.email"/></t>
                    </div>
                </div>
            ]]>
        </field>
    </record>
</odoo>
