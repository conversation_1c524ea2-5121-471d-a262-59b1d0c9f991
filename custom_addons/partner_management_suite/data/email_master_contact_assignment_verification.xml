<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="master_contact_assignment_verification_email_template" model="mail.template">
        <field name="name">Master Contact Assignment/Removal Verification</field>
        <field name="model_id" ref="partner_management_suite.model_contact_change_request"/>
        <field name="subject">Action Required: Contact Role Update</field>
        <field name="email_from">{{ (object.company_id.email_formatted or user.company_id.email_formatted or user.email_formatted) }}</field>
        <field name="email_to">{{ object.partner_id.parent_id.email }}</field>
        <field name="body_html">
            <![CDATA[
                <div style="font-family: Arial, sans-serif; padding: 15px;">
                    <p>Hello <t t-esc="object.partner_id.parent_id.name"/>,</p>

                    <t t-if="object.trigger_type == 'assign_master'">
                        <p>We have received a request to assign <strong><t t-esc="object.partner_id.name"/></strong> as the new <strong>Master Contact</strong> for your company profile.</p>
                        <p>Please review and confirm this assignment:</p>
                    </t>

                    <t t-if="object.trigger_type == 'remove_master'">
                        <p>We have received a request to <strong>remove</strong> <strong><t t-esc="object.partner_id.name"/></strong> as the <strong>Master Contact</strong> for your company profile.</p>
                        <p>Please review and confirm this removal:</p>
                    </t>

                    <t t-if="object.trigger_type == 'assign_accountant'">
                        <p>We have received a request to assign <strong><t t-esc="object.partner_id.name"/></strong> as the <strong>Accountant Contact</strong> for your company profile.</p>
                        <p>Please review and confirm this assignment:</p>
                    </t>

                    <t t-if="object.trigger_type == 'remove_accountant'">
                        <p>We have received a request to <strong>remove</strong> <strong><t t-esc="object.partner_id.name"/></strong> as the <strong>Accountant Contact</strong> for your company profile.</p>
                        <p>Please review and confirm this removal:</p>
                    </t>

                    <t t-if="object.trigger_type == 'assign_driver'">
                        <p>We have received a request to assign <strong><t t-esc="object.partner_id.name"/></strong> as the <strong>Driver Contact</strong> for your company profile.</p>
                        <p>Please review and confirm this assignment:</p>
                    </t>

                    <t t-if="object.trigger_type == 'remove_driver'">
                        <p>We have received a request to <strong>remove</strong> <strong><t t-esc="object.partner_id.name"/></strong> as the <strong>Driver Contact</strong> for your company profile.</p>
                        <p>Please review and confirm this removal:</p>
                    </t>

                    <div style="margin: 20px 0;">
                        <a t-att-href="object._get_approval_url() or ''"
                           style="background-color:#28a745;color:white;padding:12px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-bottom:10px;">
                           ✔️ Approve
                        </a><br/>
                        <a t-att-href="object._get_rejection_url() or ''"
                           style="background-color:#dc3545;color:white;padding:12px 20px;text-decoration:none;border-radius:5px;display:inline-block;">
                           ❌ Reject
                        </a>
                    </div>

                    <p><i>Note: This verification link will expire automatically after 48 hours.</i></p>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;"/>

                    <div style="text-align: center; font-size: 12px; color: #666; margin-top: 20px;">
                        <t t-if="object.company_id.logo">
                            <img t-att-src="'/web/image/res.company/%s/logo' % object.company_id.id"
                                 alt="Company Logo"
                                 style="max-height:100px; height:auto; width:auto; display:inline-block; margin-bottom:10px;"/><br/>
                        </t>
                        <strong><t t-esc="object.company_id.name"/></strong><br/>
                        <t t-if="object.company_id.street"><t t-esc="object.company_id.street"/>, </t>
                        <t t-if="object.company_id.city"><t t-esc="object.company_id.city"/>, </t>
                        <t t-if="object.company_id.country_id"><t t-esc="object.company_id.country_id.name"/></t><br/>
                        <t t-if="object.company_id.phone">Phone: <t t-esc="object.company_id.phone"/> | </t>
                        <t t-if="object.company_id.email">Email: <t t-esc="object.company_id.email"/></t>
                    </div>
                </div>
            ]]>
        </field>
    </record>
</odoo>