<odoo>
  <data noupdate="1">

    <!-- Group: Can Create Contacts -->
    <record id="group_contact_creator" model="res.groups">
      <field name="name">Can Create Contacts</field>
      <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <!-- Group: Can Import Contacts -->
    <record id="group_contact_import" model="res.groups">
      <field name="name">Can Import Contacts</field>
      <field name="category_id" ref="base.module_category_hidden"/>
    </record>

  </data>
</odoo>
