id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_competitor_product_line,access.competitor.product.line,model_competitor_product_line,base.group_user,1,0,0,0
access_competitor_product_wizard,access.competitor.product.wizard,model_competitor_product_wizard,base.group_user,1,1,1,0
access_supplier_type_user,res_partner_supplier_type_user,model_res_partner_supplier_type,base.group_user,1,1,1,1
access_contact_verification_log_user,access.contact.verification.log.user,model_contact_verification_log,base.group_user,1,0,1,0

access_contact_change_request_user,access.contact.change.request.user,model_contact_change_request,base.group_user,1,0,1,0
access_contact_change_request_salesperson,access.contact.change.request.salesperson,model_contact_change_request,sales_team.group_sale_salesman,1,1,1,0
access_contact_change_request_sales_manager,access.contact.change.request.sales.manager,model_contact_change_request,sales_team.group_sale_manager,1,1,1,1
access_contact_change_request_partner_manager,access.contact.change.request.partner.manager,model_contact_change_request,partner_management_suite.group_contact_change_manager,1,1,0,0
access_contact_change_request_admin,access.contact.change.request.admin,model_contact_change_request,base.group_system,1,1,1,1

access_resend_verification_wizard_user,resend.verification.wizard,model_resend_verification_wizard,base.group_user,1,1,1,0
access_contact_verification_wizard_user,contact.verification.wizard,model_contact_verification_wizard,base.group_user,1,1,1,0
access_res_config_settings_contact_verification,res.config.settings.contact.verification.access,base.model_res_config_settings,base.group_system,1,1,0,0

