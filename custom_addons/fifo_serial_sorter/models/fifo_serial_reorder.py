from odoo import models, api
from datetime import datetime, timedelta

class FIFOReorder(models.TransientModel):
    _name = "fifo.serial.reorder"
    _description = "One-Time FIFO Serial Reorder"

    @api.model
    def run_fifo_reorder(self):
        Lot = self.env["stock.lot"]
        now = datetime.now()
        delta = timedelta(seconds=1)

        lots = Lot.search([
            ("name", "!=", False),
            ("product_id.tracking", "=", "serial")
        ])
        product_ids = lots.mapped("product_id").ids

        for product_id in product_ids:
            product_lots = lots.filtered(lambda l: l.product_id.id == product_id)
            sorted_lots = sorted(product_lots, key=lambda lot: lot.name)

            for i, lot in enumerate(sorted_lots):
                new_date = now - (len(sorted_lots) - i) * delta
                self.env.cr.execute(
                    "UPDATE stock_lot SET create_date = %s WHERE id = %s",
                    (new_date, lot.id)
                )
