from odoo import models, api

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    def _get_product_configurator_required(self):
        # Just in case — this disables the configurator flag (harmless to keep)
        return False

    @api.model_create_multi
    def create(self, vals_list):
        lines = super().create(vals_list)
        for line in lines:
            if not line.product_id or not line.order_id:
                continue

            # Automatically add optional products to sale.order.option
            for optional_template in line.product_id.optional_product_ids:
                optional_product = optional_template.product_variant_id
                if optional_product:
                    self.env['sale.order.option'].create({
                        'order_id': line.order_id.id,
                        'product_id': optional_product.id,
                        'quantity': 1,
                        'uom_id': optional_product.uom_id.id,
                        'price_unit': optional_product.lst_price,
                        'line_id': line.id,
                    })
        return lines

    def unlink(self):
        for line in self:
            self.env['sale.order.option'].search([('line_id', '=', line.id)]).unlink()
        return super().unlink()
