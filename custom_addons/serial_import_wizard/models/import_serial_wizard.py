from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import io
import pandas as pd


class ImportSerialWizard(models.TransientModel):
    _name = 'import.serial.wizard'
    _description = 'Import Serials Wizard'

    picking_id = fields.Many2one('stock.picking', string="Picking", required=True)
    file = fields.Binary(string="Excel File", required=True)
    file_name = fields.Char(string="Filename")

    def action_import_serials(self):
        self.ensure_one()

        try:
            decoded_file = base64.b64decode(self.file)
            df = pd.read_excel(io.BytesIO(decoded_file), header=None)
        except Exception as e:
            raise UserError(_('Failed to read Excel file: %s') % str(e))

        serials = []
        for index, row in df.iterrows():
            if pd.isna(row[0]):
                continue
            name = str(row[0]).strip()
            qty = float(row[1]) if len(row) > 1 and not pd.isna(row[1]) else 1.0
            serials.append((name, qty))

        move = self.picking_id.move_ids_without_package.filtered(lambda m: m.product_id.tracking != 'none')
        if not move:
            raise UserError(_('No tracked product found in this picking.'))

        move_line_vals = []
        for name, qty in serials:
            for i in range(int(qty)):
                move_line_vals.append({
                    'picking_id': self.picking_id.id,
                    'location_id': move.location_id.id,
                    'location_dest_id': move.location_dest_id.id,
                    'product_id': move.product_id.id,
                    'product_uom_id': move.product_uom.id,
                    'qty_done': 1,
                    'lot_name': name,
                })

        self.env['stock.move.line'].create(move_line_vals)
        return {'type': 'ir.actions.act_window_close'}