<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_driver_verification_settings_form" model="ir.ui.view">
        <field name="name">driver.verification.settings.form</field>
        <field name="model">driver.verification.settings</field>
        <field name="arch" type="xml">
            <form string="Driver Verification Rule">
                <sheet>
                    <div class="alert alert-info" role="alert">
                        Driver ID Verification will only trigger if:
                        - The rule matches grading &amp; operation type,
                        - The invoice contains the selected delivery product (e.g. DEL_001),
                        - The partner has a driver contact with an ID number.
                    </div>
                    <group>
                        <field name="name" help="Internal name to identify this verification rule."/>
                        <field name="company_id" help="Only applies to pickings for this company."/>
                        <field name="active"/>
                    </group>
                    <group string="Operation Type">
                        <field name="picking_type_id" help="Trigger rule only for this picking operation type (e.g. DELIVERY)."/>
                    </group>
                    <group string="Delivery Product Match">
                        <field name="delivery_product_id"
                               help="Select a delivery product with code like DEL_001. This must appear on the related invoice for the rule to apply."/>
                    </group>
                    <group string="Grading Match">
                        <field name="grading_a" help="Applies to customers graded A."/>
                        <field name="grading_b" help="Applies to customers graded B."/>
                        <field name="grading_c" help="Applies to customers graded C."/>
                        <field name="grading_d" help="Applies to customers graded D."/>
                        <field name="grading_cod" help="Applies to customers graded C.O.D."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_driver_verification_settings_list" model="ir.ui.view">
        <field name="name">driver.verification.settings.list</field>
        <field name="model">driver.verification.settings</field>
        <field name="arch" type="xml">
            <list string="Driver Verification Rules">
                <field name="name"/>
                <field name="picking_type_id"/>
                <field name="delivery_product_id"/>
                <field name="company_id"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="action_driver_verification_settings" model="ir.actions.act_window">
        <field name="name">Driver Verification Rules</field>
        <field name="res_model">driver.verification.settings</field>
        <field name="view_mode">list,form</field>
        <field name="context">{}</field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_driver_verification_settings"
              name="Driver Verification Rules"
              parent="stock.menu_stock_config_settings"
              action="action_driver_verification_settings"
              sequence="50"/>
</odoo>
