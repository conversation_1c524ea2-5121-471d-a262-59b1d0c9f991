from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class DriverIDVerificationWizard(models.TransientModel):
    _name = 'driver.id.verification.wizard'
    _description = 'Driver ID Verification Wizard'

    driver_id_entered = fields.Char(string="Driver ID Number", required=True)
    picking_id = fields.Many2one('stock.picking', string="Delivery Order", required=True)

    def action_confirm_driver_id(self):
        self.ensure_one()
        picking = self.picking_id

        # Filter driver contacts
        driver_contacts = picking.partner_id.child_ids.filtered(
            lambda c: c.is_driver_contact and c.driver_id_number
        )
        if not driver_contacts:
            raise ValidationError(_("No assigned driver with a registered ID found."))

        entered_id = (self.driver_id_entered or '').strip()
        valid_ids = [d.driver_id_number.strip() for d in driver_contacts if d.driver_id_number]

        if entered_id not in valid_ids:
            raise ValidationError(_("Entered Driver ID does not match any assigned driver's ID."))

        # Success: Save the ID and log the match
        picking.driver_id_input = entered_id
        picking.message_post(body=_("✅ Driver ID verified for collection: <strong>%s</strong>" % entered_id))

        # ✅ Proceed with the original validation
        return picking.button_validate()
