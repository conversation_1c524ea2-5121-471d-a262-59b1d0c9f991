from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    driver_id_input = fields.Char(string="Entered Driver ID", readonly=True, copy=False)

    @api.model
    def create(self, vals):
        picking = super().create(vals)
        picking.driver_id_input = False
        return picking

    def button_validate(self):
        self.ensure_one()

        _logger.info("Driver Verification: Starting validation for picking %s", self.name)

        if self.state != 'assigned':
            _logger.info("Driver Verification: Skipped - picking state is not 'assigned'")
            return super().button_validate()

        # 🚫 Skip re-triggering the wizard if driver ID already set
        if self.driver_id_input:
            _logger.info("Driver Verification: Driver ID already entered (%s) - proceeding", self.driver_id_input)
            return super().button_validate()

        # Load active rules for this company and picking type
        rules = self.env['driver.verification.settings'].search([
            ('company_id', '=', self.company_id.id),
            ('active', '=', True),
            ('picking_type_id', '=', self.picking_type_id.id),
        ])
        _logger.info("Driver Verification: Found %d rules for picking type %s", len(rules), self.picking_type_id.display_name)

        # Apply filters using new product-based logic
        matched_rules = rules.filtered(lambda r: r.applies_to(self))
        _logger.info("Driver Verification: %d rules matched after grading/delivery product filter", len(matched_rules))

        _logger.info("Driver Verification: Partner=%s | Grading=%s | Origin=%s",
                     self.partner_id.name,
                     self.partner_id.customer_grading,
                     self.origin or 'N/A')

        if not matched_rules:
            _logger.info("Driver Verification: No matching rules — proceeding without verification.")
            return super().button_validate()

        # Check for valid driver contact
        driver_contact = self.partner_id.child_ids.filtered(
            lambda c: c.is_driver_contact and c.driver_id_number
        )
        _logger.info("Driver Verification: Found %d driver contact(s) with ID:", len(driver_contact))
        for drv in driver_contact:
            _logger.info(" - Driver: %s | ID: %s", drv.name, drv.driver_id_number)

        if not driver_contact:
            _logger.info("Driver Verification: No driver contacts with ID number — skipping popup.")
            return super().button_validate()

        _logger.info("Driver Verification: Triggering wizard for picking %s", self.name)

        return {
            'name': _('Driver ID Verification'),
            'type': 'ir.actions.act_window',
            'res_model': 'driver.id.verification.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_picking_id': self.id,
            }
        }
