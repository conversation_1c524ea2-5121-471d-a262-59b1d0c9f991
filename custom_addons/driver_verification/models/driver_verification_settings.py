from odoo import models, fields
import logging

_logger = logging.getLogger(__name__)


class DriverVerificationSettings(models.Model):
    _name = 'driver.verification.settings'
    _description = 'Driver Verification Rule'
    _order = 'name'

    name = fields.Char(string="Rule Name", required=True)
    picking_type_id = fields.Many2one('stock.picking.type', string='Operation Type', required=True)

    delivery_product_id = fields.Many2one(
        'product.product',
        string='Delivery Product (e.g. DEL_001)',
        domain="[('default_code', 'ilike', 'DEL_')]",
        help="Select the delivery fee product that will trigger this rule."
    )

    grading_a = fields.Boolean(string="Grade A")
    grading_b = fields.Boolean(string="Grade B")
    grading_c = fields.Boolean(string="Grade C")
    grading_d = fields.Boolean(string="Grade D")
    grading_cod = fields.Boolean(string="Grade C.O.D.")

    company_id = fields.Many2one(
        'res.company', string='Company', required=True,
        default=lambda self: self.env.company
    )
    active = fields.Boolean(default=True)

    def applies_to(self, picking):
        grading = (picking.partner_id.customer_grading or '').strip().lower()
        product_match = True
        product_code = None

        # Match invoice lines to selected delivery product
        if self.delivery_product_id:
            product_code = (self.delivery_product_id.default_code or '').strip().upper()
            product_match = False

            if picking.origin:
                invoices = self.env['account.move'].search([
                    ('invoice_origin', '=', picking.origin),
                    ('move_type', '=', 'out_invoice'),
                    ('state', '!=', 'cancel')
                ])
                for inv in invoices:
                    for line in inv.invoice_line_ids:
                        if line.product_id.default_code and \
                                line.product_id.default_code.strip().upper() == product_code:
                            product_match = True
                            break
                    if product_match:
                        break

        grading_match = (
            (self.grading_a and grading == 'a') or
            (self.grading_b and grading == 'b') or
            (self.grading_c and grading == 'c') or
            (self.grading_d and grading == 'd') or
            (self.grading_cod and grading == 'cod')
        )

        _logger.info(
            "Driver Verification Rule [%s]: Product match=%s | Grading match=%s | Grading=%s | Product code=%s | Picking Origin=%s",
            self.name, product_match, grading_match, grading, product_code or 'N/A', picking.origin
        )

        return (
            picking.picking_type_code == 'outgoing' and
            picking.picking_type_id.id == self.picking_type_id.id and
            product_match and
            grading_match
        )
