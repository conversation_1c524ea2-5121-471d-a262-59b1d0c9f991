{
    'name': 'Driver Verification',
    'version': '1.0',
    'category': 'Inventory',
    'summary': 'Verify driver identity during handover in local collection deliveries',
    'author': '<PERSON><PERSON>',
    'license': 'LGPL-3',
    'depends': ['stock', 'base', 'delivery'],
    'data': [
        'security/ir.model.access.csv',
        'views/driver_id_verification_wizard.xml',
        'views/stock_picking_views.xml',
        'views/driver_verification_settings.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
}
