from odoo import models, fields, api
from datetime import datetime

# Generate dropdown years (with self for compatibility)
def get_year_selection(self):
    current_year = datetime.now().year
    return [(str(year), str(year)) for year in range(1975, current_year + 1)]

class VehicleMake(models.Model):
    _name = 'vehicle.make'
    _description = 'Vehicle Make'
    name = fields.Char(required=True)

class VehicleModel(models.Model):
    _name = 'vehicle.model'
    _description = 'Vehicle Model'
    name = fields.Char(required=True)

class VehicleType(models.Model):
    _name = 'vehicle.type'
    _description = 'Vehicle Type'
    name = fields.Char(required=True)

class VehicleFuel(models.Model):
    _name = 'vehicle.fuel'
    _description = 'Fuel Type'
    name = fields.Char(required=True)

class VehicleEngineCode(models.Model):
    _name = 'vehicle.engine.code'
    _description = 'Engine Code'
    name = fields.Char(required=True)

class VehicleSpecification(models.Model):
    _name = 'vehicle.specification'
    _description = 'Vehicle Specification'

    name = fields.Char(string="Name", compute="_compute_name", store=True)

    product_tmpl_id = fields.Many2one('product.template', required=True, ondelete='cascade', string='Product')

    make_id = fields.Many2one('vehicle.make', string='Make')
    type_id = fields.Many2one('vehicle.type', string='Type')
    model_id = fields.Many2one('vehicle.model', string='Model')
    fuel_id = fields.Many2one('vehicle.fuel', string='Fuel Type')
    year_start = fields.Selection(selection=get_year_selection, string='Year From')
    year_end = fields.Selection(selection=get_year_selection, string='Year To')

    engine_code_id = fields.Many2one('vehicle.engine.code', string='Engine Code')
    engine_size = fields.Char(string='Engine Size')
    cc = fields.Integer(string='CC')
    cylinders = fields.Integer(string='Cylinders')
    bore = fields.Char(string='Bore')
    stroke = fields.Char(string='Stroke')
    hp = fields.Integer(string='Horse Power')
    kw = fields.Integer(string='Kilowatt')

    oe_number = fields.Char(string='OE Number')
    vehicle_image = fields.Image(string="Vehicle Image", max_width=512, max_height=512)

    @api.depends('make_id', 'model_id', 'year_start')
    def _compute_name(self):
        for rec in self:
            make = rec.make_id.name or ''
            model = rec.model_id.name or ''
            year = rec.year_start or ''
            parts = [f"[{year}]" if year else '', make.upper(), model]
            rec.name = " ".join(p for p in parts if p).strip()

# Extend product.template to link to vehicle specs
class ProductTemplate(models.Model):
    _inherit = 'product.template'

    vehicle_spec_ids = fields.One2many(
        'vehicle.specification',
        'product_tmpl_id',
        string='Vehicle Specifications'
    )
