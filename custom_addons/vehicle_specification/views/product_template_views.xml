<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <record id="product_template_view_vehicle_specs" model="ir.ui.view">
    <field name="name">product.template.vehicle.specs</field>
    <field name="model">product.template</field>
    <field name="inherit_id" ref="product.product_template_only_form_view"/>
    <field name="arch" type="xml">
      <xpath expr="//sheet/notebook/page[1]" position="after">
        <page string="Vehicle Specs">
          <group string="Vehicle Specifications">
            <field name="vehicle_spec_ids">
              <kanban class="o_kanban_small_column">
                <field name="make_id"/>
                <field name="model_id"/>
                <field name="year_start"/>
                <field name="year_end"/>
                <field name="fuel_id"/>
                <field name="vehicle_image"/>
                <templates>
                  <t t-name="kanban-box">
                    <div class="oe_kanban_global_click o_kanban_card">
                      <div class="d-flex justify-content-between align-items-center">
                        <div>
                          <strong><field name="make_id"/></strong><br/>
                          <span><field name="model_id"/></span><br/>
                          <span><field name="fuel_id"/></span><br/>
                          <span><field name="year_start"/> - <field name="year_end"/></span>
                        </div>
                        <div class="oe_kanban_image">
                          <img t-if="record.vehicle_image.raw"
                               t-att-src="kanban_image('vehicle.specification', 'vehicle_image', record.id)"
                               style="max-width: 120px; height: auto; border-radius: 4px; object-fit: contain;"/>
                        </div>
                      </div>
                    </div>
                  </t>
                </templates>
              </kanban>
            </field>
          </group>
        </page>
      </xpath>
    </field>
  </record>
</odoo>