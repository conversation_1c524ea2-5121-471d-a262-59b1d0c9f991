<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- Kanban View -->
  <record id="view_vehicle_specification_kanban" model="ir.ui.view">
    <field name="name">vehicle.specification.kanban</field>
    <field name="model">vehicle.specification</field>
    <field name="arch" type="xml">
      <kanban class="o_kanban_small_column">
        <field name="name"/>
        <field name="vehicle_image"/>
        <field name="make_id"/>
        <field name="model_id"/>
        <field name="year_start"/>
        <field name="fuel_id"/>
        <templates>
          <t t-name="kanban-box">
            <div class="oe_kanban_global_click o_kanban_card">
              <div class="d-flex justify-content-between">
                <div>
                  <strong><field name="name"/></strong><br/>
                  <span><field name="fuel_id"/></span><br/>
                </div>
                <div class="oe_kanban_image">
                  <img t-if="record.vehicle_image.raw"
                       t-att-src="kanban_image('vehicle.specification', 'vehicle_image', record.id)"
                       style="max-height: 80px; max-width: 120px; object-fit: contain;"/>
                </div>
              </div>
            </div>
          </t>
        </templates>
      </kanban>
    </field>
  </record>

  <!-- Enhanced Styled Form View -->
  <record id="view_vehicle_specification_form_compact" model="ir.ui.view">
    <field name="name">vehicle.specification.form.compact</field>
    <field name="model">vehicle.specification</field>
    <field name="arch" type="xml">
      <form string="Vehicle Specification" class="vehicle-spec-form">
        <sheet>
          <div class="o_form_layout">

            <!-- Header and Product Link -->
            <div style="text-align: center; margin-bottom: 1rem;">
              <div style="font-size: 18px; font-weight: bold; margin-bottom: 0.5rem;">
                <field name="name" readonly="1"/>
              </div>
              <div style="max-width: 400px; margin: 0 auto;">
                <field name="product_tmpl_id"/>
              </div>
            </div>

            <!-- Main Flex Layout -->
            <div class="d-flex flex-wrap justify-content-center align-items-start gap-4 vehicle-spec-container">

              <!-- Vehicle Info -->
              <div class="vehicle-info-panel">
                <group string="Vehicle Info">
                  <field name="make_id"/>
                  <field name="model_id"/>
                  <field name="type_id"/>
                  <field name="fuel_id"/>
                  <field name="year_start"/>
                  <field name="year_end"/>
                </group>
              </div>

              <!-- Vehicle Image Center -->
              <div class="vehicle-image-wrapper">
                <field name="vehicle_image" widget="image"
                       options="{'size': [512, 512]}"/>
              </div>

              <!-- Engine Identifiers -->
              <div class="engine-info-panel">
                <group string="Engine Identifiers">
                  <field name="engine_code_id"/>
                  <field name="engine_size"/>
                  <field name="cc"/>
                  <field name="cylinders"/>
                  <field name="bore"/>
                  <field name="stroke"/>
                  <field name="hp"/>
                  <field name="kw"/>
                  <field name="oe_number"/>
                </group>
              </div>

            </div>
          </div>
        </sheet>
      </form>
    </field>
  </record>

  <!-- Kanban-Only Action -->
  <record id="action_vehicle_specification" model="ir.actions.act_window">
    <field name="name">Vehicle Specifications</field>
    <field name="res_model">vehicle.specification</field>
    <field name="view_mode">kanban,form</field>
  </record>

  <!-- Menu Items -->
  <menuitem id="menu_vehicle_spec_root" name="Vehicle Specs" parent="stock.menu_stock_root" sequence="95"/>
  <menuitem id="menu_vehicle_specifications" name="Specifications" parent="menu_vehicle_spec_root" action="action_vehicle_specification"/>

</odoo>
