.vehicle-spec-container {
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
}

.vehicle-image-wrapper {
  flex: 0 1 360px;
  max-width: 360px;
  text-align: center;
}

.vehicle-image-wrapper img {
  width: 100% !important;
  height: auto !important;
  max-height: 300px;
  object-fit: contain;
  border-radius: 10px;
  border: 1px solid #ffffff;
}

.vehicle-info-panel,
.engine-info-panel {
  flex: 0 1 260px;
  max-width: 260px;
}

.o_field_widget {
    text-align: inherit;
    display: inherit;
    margin-bottom: var(--fieldWidget-margin-bottom);
}