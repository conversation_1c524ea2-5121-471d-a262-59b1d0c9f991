{
    'name': 'Custom BoM Kanban',
    'version': '1.0',
    'depends': ['product', 'stock'],
    'category': 'Inventory',
    'summary': 'Grouped custom BoMs per product with wizard and kanban view',
    'author': '<PERSON><PERSON>',
    'data': [
        'security/ir.model.access.csv',
        'views/custom_bom_views.xml',
        'views/product_template_views.xml',
        'views/custom_bom_regenerate.xml',
        'views/bom_visibility_config_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
