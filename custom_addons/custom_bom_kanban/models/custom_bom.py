from odoo import models, fields, api
from collections import defaultdict
from itertools import product as itertools_product
import logging

_logger = logging.getLogger(__name__)

# Template mapping reused for both manual and auto generation
BOM_TYPE_TEMPLATES = {
    'turbocharger': [
        "CHRA/CARTRIDGE", "BACK & OIL SEAL PLATE", "HEAT SHIELD", "BEARING HOUSING",
        "BOLTS NUTS STUDS LOCKPLATES", "COMPRESSOR COVER", "COMPRESSOR WHEEL", "ELECTRONIC ACTUATOR",
        "GASKETS", "ORINGS", "WASHERS", "SEALS", "J BEARING", "PISTON RING", "NOZZLE RING ASSY",
        "REPA<PERSON> KIT", "ROTOR ASSY", "SOLENOID", "DIVERTER VALVES", "THRUST BEARING", "THRUST COLLAR KITS",
        "TURBINE HOUSING", "TURBINE WHEEL ASSY", "OIL DRAIN PIPE", "OIL SUPPLY PIPE", "AIR INTAKE PIPE",
        "INSTALLATION KIT", "V BAND CLAMPS"
    ],
    'diesel_injector': [
        "ACTUATOR", "CAMPLATE", "CONTROL VALVE", "DELIVERY VALVE", "FEED PUMP", "GEAR PUMP",
        "HEAD & ROTOR", "INJECTOR REPAIR KIT", "METERING VALVE", "NOZZLE", "ORING KIT", "PLUNGER",
        "REPAIR KIT", "SCV VALVE", "SEAL KIT", "SOLENOID", "TIMING CONTROL VALVE", "VALVE", "WASHER KIT"
    ],
    'diesel_pump': [
        "DRV VALVE", "ZME VALVE", "FEED PUMP", "OVERFLOW VALVE", "PRESSURE LIMITING VALVE", "FUEL TEMP SENSOR",
        "PUMP GASKET KIT", "PLUNGER GASKET KIT", "PLUNGER", "DRIVE SEAL", "DIESEL PUMP VALVE ASSY",
        "FLANGE REPAIR KIT", "FLANGE GASKET KIT", "UNLOADING VALVE"
    ],
    'production': [
        "TURBINE HOUSING", "COMPRESSOR HOUSING", "BEARING HOUSING", "ACTUATOR", "REA", "REA SHROUD",
        "REA BRACKET", "REA GASKET", "REA CONNECTING ROD", "SHAFT AND WHEEL ASSEMBLY (SWA)",
        "COMPRESSOR WHEEL", "HEAT SHROUD", "OIL DEFLECTOR", "PISTON RING-C", "PISTON RING-T",
        "CIRCLIP FOR BEARING HOUSING", "CIRCLIP FOR SEAL PLATE", "CIRCLIP FOR COMPRESSOR HOUSING",
        "V-BAND", "O-RING", "NOZZLE RING", "NOZZLE BRACKET", "NOZZLE C-RING", "STEEL VNT GASKET",
        "LOCK NUT", "TURBINE HOUSING HEAT SHROUD GASKET", "COMPRESSOR HOUSING ADAPTOR", "LOCK PLATE-C",
        "LOCK PLATE-T", "JOURNAL BEARING", "JOURNAL BEARING STOP PIN", "THRUST BEARING", "THRUST COLLAR",
        "THRUST FLINGER", "THRUST SPACER", "RUBBER PIPE", "PIPE CLAMP", "AIR VALVE ON COMPRESSOR HOUSING",
        "WATER PIPE", "SPACER IN BEARING HOUSING", "BOLTS FOR BEARING HOUSING", "BOLTS FOR COMPRESSOR HOUSING",
        "BOLTS FOR TURBINE HOUSING", "BOLTS FOR ACTUATOR", "BOLTS FOR REA", "BOLTS FOR BACK PLATE",
        "BOLTS FOR THRUST BEARING", "OIL OUTLET PAPER GASKET", "OIL INLET PAPER GASKET",
        "WATER INLET PAPER GASKE", "AIR OUTLET GASKET", "AIR INLET GASKET", "BACK PLATE", "SEAL PLATE",
        "PLASTIC FITMENT BOX", "INSTRUCTION BOOK", "INJECTION SYRINGE WITH OIL", "BOX",
        "ANTI-RUST PAPER", "ANTI-RUST BAG", "ANTI-RUST CAP", "NAME PLATE", "PIN FOR NAME PLATE",
        "PADDING PAPER", "SHRINKING FILM", "PACKING STRAP", "CABLE TIE"
    ]
}

CATEGORY_TO_BOM_TYPE = {
    5: 'turbocharger',
    71: 'diesel_injector',
    74: 'diesel_pump',
}


class ProductCustomBom(models.Model):
    _name = 'product.custom.bom'
    _description = 'Custom BoM'

    name = fields.Char(required=True)
    product_tmpl_id = fields.Many2one('product.template', string="Product Template", required=True)

    bom_type = fields.Selection(
        selection='_get_visible_bom_types',
        string="BoM Type",
        required=True
    )

    def _get_visible_bom_types(self):
        company_id = self.env.company.id
        visible_configs = self.env['bom.template.visibility.config'].sudo().search([
            ('allowed_company_ids', 'in', company_id)
        ])
        bom_type_labels = {
            'turbocharger': 'Turbocharger',
            'diesel_injector': 'Diesel Injector',
            'diesel_pump': 'Pump',
            'production': 'Production'
        }
        return [(rec.bom_type, bom_type_labels.get(rec.bom_type, rec.bom_type)) for rec in visible_configs]

    note = fields.Text()
    line_ids = fields.One2many('product.custom.bom.line', 'bom_id', string="Components")

    @api.onchange('bom_type')
    def _onchange_bom_type(self):
        if self.bom_type and self.env.context.get('default_from_template'):
            lines = [(5, 0, 0)]
            for label in BOM_TYPE_TEMPLATES.get(self.bom_type, []):
                lines.append((0, 0, {'template_label': label}))
            self.line_ids = lines

    @api.model
    def create(self, vals):
        if 'line_ids' in vals:
            vals['line_ids'] = [
                line for line in vals['line_ids']
                if line[0] != 0 or (line[2] and line[2].get('component_id'))
            ]
        return super().create(vals)

    def write(self, vals):
        if 'line_ids' in vals:
            vals['line_ids'] = [
                line for line in vals['line_ids']
                if line[0] != 0 or (line[2] and line[2].get('component_id'))
            ]
        return super().write(vals)


class ProductCustomBomLine(models.Model):
    _name = 'product.custom.bom.line'
    _description = 'Custom BoM Line'

    bom_id = fields.Many2one('product.custom.bom', string="BoM")
    template_label = fields.Char(string="Template Label")
    component_id = fields.Many2one('product.product', string="Component")
    quantity = fields.Float(default=1.0)
    sku = fields.Char(compute='_compute_sku', store=False)
    stock = fields.Float(compute='_compute_stock', store=False)

    @api.depends('component_id')
    def _compute_sku(self):
        for line in self:
            line.sku = line.component_id.default_code if line.component_id else ''

    @api.depends('component_id')
    def _compute_stock(self):
        for line in self:
            line.stock = line.component_id.qty_available if line.component_id else 0.0


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    custom_bom_ids = fields.One2many(
        'product.custom.bom',
        'product_tmpl_id',
        string='Custom BoMs',
        domain=lambda self: [
            ('bom_type', 'in', self.env['bom.template.visibility.config'].sudo().search([
                ('allowed_company_ids', 'in', self.env.company.id)
            ]).mapped('bom_type'))
        ]
    )

    def action_mass_regenerate_custom_boms(self):
        products = self.env['product.template'].search([('categ_id', 'in', list(CATEGORY_TO_BOM_TYPE.keys()))])
        _logger.info(f"[BoM] Regenerating for {len(products)} products.")
        for product in products:
            product._auto_generate_custom_boms()

    def _auto_generate_custom_boms(self):
        self.ensure_one()
        bom_type = CATEGORY_TO_BOM_TYPE.get(self.categ_id.id)

        if not bom_type:
            return

        allowed = self.env['bom.template.visibility.config'].sudo().search([
            ('bom_type', '=', bom_type),
            ('allowed_company_ids', 'in', self.env.company.id)
        ])
        if not allowed:
            _logger.info(f"[BoM] Skipped auto-generation for '{self.name}' — type '{bom_type}' not allowed for company '{self.env.company.name}'")
            return

        template_labels = BOM_TYPE_TEMPLATES.get(bom_type, [])
        matching_components = self.env['product.product'].search([
            ('original_part_ids', 'in', self.original_part_ids.ids),
            ('id', '!=', self.product_variant_id.id),
        ])

        grouped = defaultdict(list)
        for comp in matching_components:
            if comp.categ_id:
                grouped[comp.categ_id.id].append(comp)

        group_lists = [grouped[k] for k in grouped if grouped[k]]

        for combo in itertools_product(*group_lists):
            part_codes = [comp.default_code or comp.name for comp in combo]
            part_refs_str = ", ".join(part_codes)
            bom = self.env['product.custom.bom'].create({
                'name': f"Auto-BoM for {self.name or self.default_code or 'Product'} using [{part_refs_str}]",
                'product_tmpl_id': self.id,
                'bom_type': bom_type,
                'note': 'Auto-generated based on matching original part numbers',
            })

            lines = []
            for label in template_labels:
                component = next((c for c in combo if label.lower() in (c.name or '').lower()), None)
                line_vals = {
                    'bom_id': bom.id,
                    'template_label': label,
                    'component_id': component.id if component else False,
                    'quantity': 1.0
                }
                lines.append((0, 0, line_vals))

            bom.write({'line_ids': lines})
