from odoo import models, fields

class BomTemplateVisibilityConfig(models.Model):
    _name = 'bom.template.visibility.config'
    _description = 'BoM Template Visibility per Company'
    _rec_name = 'bom_type'

    bom_type = fields.Selection([
        ('turbocharger', 'Turbocharger'),
        ('diesel_injector', 'Diesel Injector'),
        ('diesel_pump', 'Pump'),
        ('production', 'Production'),
    ], required=True, string="BoM Type", unique=True)

    allowed_company_ids = fields.Many2many('res.company', string="Allowed Companies")
