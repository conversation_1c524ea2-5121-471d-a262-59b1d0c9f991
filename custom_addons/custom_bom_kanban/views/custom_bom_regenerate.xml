<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Server action to trigger auto BoM generation -->
    <record id="action_mass_regenerate_custom_boms" model="ir.actions.server">
        <field name="name">Regenerate Custom BoMs</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="binding_model_id" ref="product.model_product_template"/>
        <field name="code">action = env['product.template'].action_mass_regenerate_custom_boms()</field>
    </record>

    <!-- Menu item under Custom BoMs -->
    <menuitem id="menu_regenerate_custom_boms"
              name="Regenerate All BoMs"
              parent="custom_bom_kanban.menu_custom_bom_root"
              action="custom_bom_kanban.action_mass_regenerate_custom_boms"
              sequence="99"/>
</odoo>
