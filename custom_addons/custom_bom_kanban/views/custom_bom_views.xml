<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- BoM Creation Form Now Acts as the Wizard -->
    <record id="view_custom_bom_form" model="ir.ui.view">
        <field name="name">product.custom.bom.form</field>
        <field name="model">product.custom.bom</field>
        <field name="arch" type="xml">
            <form string="Create Custom BoM">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="product_tmpl_id"/>
                        <field name="bom_type" widget="selection"/>
                        <field name="note"/>
                    </group>
                    <field name="line_ids" context="{'default_from_template': True}" modifiers="{'invisible': [['bom_type', '=', False]]}">
                        <list editable="bottom">
                            <field name="template_label" readonly="1"/>
                            <field name="component_id"/>
                            <field name="quantity"/>
                            <field name="sku" readonly="1"/>
                            <field name="stock" readonly="1"/>
                        </list>
                    </field>
                </sheet>
            </form>
        </field>
    </record>

    <!-- BoM Line Form -->
    <record id="view_custom_bom_line_form" model="ir.ui.view">
        <field name="name">product.custom.bom.line.form</field>
        <field name="model">product.custom.bom.line</field>
        <field name="arch" type="xml">
            <form string="BoM Line">
                <group>
                    <field name="component_id"/>
                    <field name="quantity"/>
                    <field name="sku" readonly="1"/>
                    <field name="stock" readonly="1"/>
                </group>
            </form>
        </field>
    </record>

    <!-- BoM Line List -->
    <record id="view_custom_bom_line_tree" model="ir.ui.view">
        <field name="name">product.custom.bom.line.list</field>
        <field name="model">product.custom.bom.line</field>
        <field name="arch" type="xml">
            <list>
                <field name="component_id"/>
                <field name="quantity"/>
                <field name="sku"/>
                <field name="stock"/>
            </list>
        </field>
    </record>

    <!-- BoM List View -->
    <record id="view_custom_bom_tree" model="ir.ui.view">
        <field name="name">product.custom.bom.list</field>
        <field name="model">product.custom.bom</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="product_tmpl_id"/>
                <field name="bom_type"/>
            </list>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_custom_bom_kanban" model="ir.ui.view">
        <field name="name">product.custom.bom.kanban</field>
        <field name="model">product.custom.bom</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_small_column" group_by="bom_type">
                <field name="name"/>
                <field name="product_tmpl_id"/>
                <field name="bom_type"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="o_kanban_card">
                            <strong><field name="name"/></strong>
                            <div><field name="product_tmpl_id"/></div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action: List, Kanban, Form -->
	<record id="action_custom_bom_list" model="ir.actions.act_window">
		<field name="name">Custom BoMs</field>
		<field name="res_model">product.custom.bom</field>
		<field name="view_mode">kanban,list,form</field>
		<field name="context">{'default_from_template': True}</field>
		<field name="views" eval="[
			(ref('view_custom_bom_kanban'), 'kanban'),
			(ref('view_custom_bom_tree'), 'list'),
			(ref('view_custom_bom_form'), 'form')
		]"/>
		<field name="help" type="html">
			<p class="o_view_nocontent_smiling_face">
				Create your first Custom BoM
			</p>
		</field>
	</record>

    <!-- Menu Structure -->
    <menuitem id="menu_custom_bom_root"
              name="Custom BoMs"
              parent="stock.menu_stock_root"
              sequence="101"/>

    <!-- BoM List opens the standard list/kanban -->
    <menuitem id="menu_custom_bom_list"
              name="BoM List"
              parent="menu_custom_bom_root"
              action="action_custom_bom_list"
              sequence="2"/>

</odoo>
