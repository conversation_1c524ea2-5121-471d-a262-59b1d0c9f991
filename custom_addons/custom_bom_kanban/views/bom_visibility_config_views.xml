<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_bom_visibility_config_form" model="ir.ui.view">
        <field name="name">bom.template.visibility.config.form</field>
        <field name="model">bom.template.visibility.config</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="bom_type"/>
                        <field name="allowed_company_ids" widget="many2many_tags"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_bom_visibility_config_list" model="ir.ui.view">
        <field name="name">bom.template.visibility.config.list</field>
        <field name="model">bom.template.visibility.config</field>
        <field name="arch" type="xml">
            <list>
                <field name="bom_type"/>
                <field name="allowed_company_ids"/>
            </list>
        </field>
    </record>

    <record id="action_bom_visibility_config" model="ir.actions.act_window">
        <field name="name">BoM Type Access</field>
        <field name="res_model">bom.template.visibility.config</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem id="menu_bom_visibility_config_root"
              name="BoM Type Access"
              parent="custom_bom_kanban.menu_custom_bom_root"
              action="action_bom_visibility_config"
              sequence="100"/>
</odoo>
