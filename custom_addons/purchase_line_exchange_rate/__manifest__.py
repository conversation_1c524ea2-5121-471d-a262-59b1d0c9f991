{
    'name': 'Purchase Line Exchange Rate',
    'version': '2.0',
    'summary': 'Set a custom exchange rate per product line on purchase orders.',
    'description': 'Allows setting a custom exchange rate and base currency cost per line at PO level and applies it to vendor bills.',
    'author': '<PERSON><PERSON>',
    'category': 'Purchases',
    'depends': ['purchase', 'account'],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
    ],
    'installable': True,
    'application': False,
    'license': 'LGPL-3',
}
