from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    CUSTOM_EXCHANGE_MODIFIERS = [
        ('-10', '-10%'), ('-7.5', '-7.5%'), ('-5', '-5%'), ('-2.5', '-2.5%'),
        ('0', '0% (Base)'), ('2.5', '+2.5%'), ('5', '+5%'), ('7.5', '+7.5%'), ('10', '+10%'),
    ]

    custom_exchange_modifier = fields.Selection(
        selection=CUSTOM_EXCHANGE_MODIFIERS,
        string='Custom Exchange Adjustment',
        default='0',
        help='Applies a percentage adjustment to the base exchange rate.'
    )

    manual_exchange_rate = fields.Float(
        string='Manual Exchange Rate',
        digits=(12, 6),
        help='Directly enter a custom exchange rate to override system and modifier logic.'
    )

    preview_adjusted_rate = fields.Float(
        string="Preview Adjusted Rate",
        compute="_compute_preview_adjusted_rate",
        digits=(12, 6),
        store=False
    )

    exchange_log_ids = fields.One2many('purchase.exchange.log', 'purchase_order_id', string="Exchange Logs")

    exchange_applied = fields.Boolean(
        string="Exchange Adjustment Applied",
        default=False,
        help="Controls whether to lock price fields and show adjusted data."
    )

    @api.depends('custom_exchange_modifier', 'currency_rate', 'manual_exchange_rate')
    def _compute_preview_adjusted_rate(self):
        for order in self:
            base = order.currency_rate or 0.0
            if order.manual_exchange_rate:
                order.preview_adjusted_rate = order.manual_exchange_rate
            else:
                try:
                    pct = float(order.custom_exchange_modifier or 0)
                    order.preview_adjusted_rate = round(base * (1 + pct / 100), 6)
                except Exception:
                    order.preview_adjusted_rate = base

    @api.constrains('custom_exchange_modifier', 'manual_exchange_rate')
    def _check_conflicting_inputs(self):
        for order in self:
            if order.manual_exchange_rate and order.custom_exchange_modifier not in ('0', None, ''):
                raise UserError(_("You cannot use both a manual exchange rate and a percentage modifier. Please clear one."))

    def action_apply_exchange_adjustment(self):
        for order in self:
            if not order.preview_adjusted_rate:
                raise UserError(_("Calculated exchange rate is missing."))

            base_rate = order.currency_rate or 1.0
            use_manual = bool(order.manual_exchange_rate and order.manual_exchange_rate > 0)

            try:
                modifier_pct = float(order.custom_exchange_modifier or 0.0)
            except Exception:
                modifier_pct = 0.0

            for line in order.order_line:
                qty = line.product_qty

                _logger.info(f"[EXCHANGE] Before: price_unit={line.price_unit}, original_unit_price={line.original_unit_price}")

                # Preserve original unit price (still optional — can be removed if obsolete)
                if not line.original_unit_price and line.price_unit:
                    _logger.info(f"[EXCHANGE] Capturing original_unit_price for line {line.id}: {line.price_unit}")
                    line.write({'original_unit_price': line.price_unit})

                base_price = line.original_unit_price or line.price_unit

                if use_manual:
                    e_unit_price = base_price * (order.manual_exchange_rate / base_rate)
                    e_local_price = line.price_unit / order.manual_exchange_rate if order.manual_exchange_rate else 0.0
                else:
                    e_unit_price = base_price * (1 + modifier_pct / 100.0)
                    e_local_price = line.price_unit / order.preview_adjusted_rate if order.preview_adjusted_rate else 0.0

                line.write({
                    'e_unit_price': e_unit_price,
                    'e_local_price': e_local_price,
                    'e_amount': qty * e_unit_price,
                    'price_unit': e_unit_price,
                })

            order.exchange_applied = True

            self.env['purchase.exchange.log'].create({
                'purchase_order_id': order.id,
                'user_id': self.env.uid,
                'applied_rate': order.preview_adjusted_rate,
                'modifier_used': order.custom_exchange_modifier,
                'manual_rate_used': order.manual_exchange_rate,
                'applied_on': fields.Datetime.now(),
                'action_type': 'apply'
            })

    def action_reset_exchange_adjustment(self):
        for order in self:
            order.custom_exchange_modifier = '0'
            order.manual_exchange_rate = 0.0
            for line in order.order_line:
                if line.original_unit_price:
                    line.price_unit = line.original_unit_price
                line.original_unit_price = 0.0
                line.e_unit_price = 0.0
                line.e_local_price = 0.0
                line.e_amount = 0.0

            order.exchange_applied = False

            self.env['purchase.exchange.log'].create({
                'purchase_order_id': order.id,
                'user_id': self.env.uid,
                'applied_rate': 0.0,
                'modifier_used': '0',
                'manual_rate_used': 0.0,
                'applied_on': fields.Datetime.now(),
                'action_type': 'reset'
            })

class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    original_unit_price = fields.Monetary(
        string="Original Unit Price",
        currency_field='currency_id',
        help="Snapshot of the price_unit before any exchange manipulation.",
        store=True
    )

    e_unit_price = fields.Monetary(
        string="E-Unit Price",
        currency_field='currency_id',
        help="Unit price with exchange adjustment applied. Still in supplier's currency."
    )

    e_local_price = fields.Monetary(
        string="E-Local Price",
        currency_field='company_currency_id',
        help="E-Unit price converted to company (local) currency."
    )

    e_amount = fields.Monetary(
        string="E-Amount",
        currency_field='currency_id',
        compute="_compute_e_amount",
        store=True,
        help="Quantity × Original Unit Price"
    )

    company_currency_id = fields.Many2one(
        'res.currency',
        compute="_compute_company_currency_id",
        store=False
    )
    
    l_cost = fields.Float(
        string="L-Cost",
        related='product_id.standard_price',
        readonly=True,
        store=False,
    )

    @api.depends('product_qty', 'original_unit_price')
    def _compute_e_amount(self):
        for line in self:
            line.e_amount = (line.product_qty or 0.0) * (line.original_unit_price or 0.0)

    @api.model
    def create(self, vals):
        if not vals.get('original_unit_price') and vals.get('price_unit'):
            vals['original_unit_price'] = vals['price_unit']
        return super().create(vals)

    def write(self, vals):
        for line in self:
            if not line.original_unit_price and vals.get('price_unit'):
                vals['original_unit_price'] = vals['price_unit']
        return super().write(vals)

    @api.depends('order_id.company_id.currency_id')
    def _compute_company_currency_id(self):
        for line in self:
            line.company_currency_id = line.order_id.company_id.currency_id

class PurchaseExchangeLog(models.Model):
    _name = 'purchase.exchange.log'
    _description = 'Purchase Exchange Rate Application Log'
    _order = 'applied_on desc'

    purchase_order_id = fields.Many2one('purchase.order', required=True, ondelete='cascade')
    user_id = fields.Many2one('res.users', required=True)
    applied_rate = fields.Float(string="Applied Exchange Rate", digits=(12, 6))
    modifier_used = fields.Selection(PurchaseOrder.CUSTOM_EXCHANGE_MODIFIERS, string="Modifier Used")
    manual_rate_used = fields.Float(string="Manual Rate", digits=(12, 6))
    applied_on = fields.Datetime(string="Applied On", required=True)
    action_type = fields.Selection([
        ('apply', 'Apply'),
        ('reset', 'Reset')
    ], string="Action", required=True)
