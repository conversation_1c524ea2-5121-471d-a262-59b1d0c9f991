<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_purchase_order_form_exchange_modifier" model="ir.ui.view">
        <field name="name">purchase.order.form.exchange.modifier</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">

            <!-- Add Exchange Adjustment Group -->
            <xpath expr="//field[@name='currency_id'][not(@invisible)]" position="after">
                <group string="Exchange Adjustment" col="2">
                    <field name="custom_exchange_modifier"
                           widget="selection"
                           options="{'no_create': True}"/>
                    <field name="manual_exchange_rate"/>
                    <field name="preview_adjusted_rate" readonly="1"/>
                    <div class="d-flex align-items-center mt-2">
                        <button name="action_apply_exchange_adjustment"
                                type="object"
                                string="Apply"
                                class="btn btn-primary"/>
                        <button name="action_reset_exchange_adjustment"
                                type="object"
                                string="Reset"
                                class="btn btn-secondary ms-2"/>
                    </div>
                </group>
            </xpath>

            <!-- Fix: Show Original Unit Price -->
            <xpath expr="//field[@name='order_line']//list//field[@name='price_unit']" position="after">
                <field name="original_unit_price" string="Original Unit Price" readonly="1"/>
				<field name="l_cost" string="L-Cost" readonly="1"/>
                <field name="e_local_price" string="E-Local Price" readonly="1"/>
            </xpath>

        </field>
    </record>
</odoo>
